package org.dromara.love.domain.vo;

import io.github.linpeilie.AutoMapperConfig__787;
import io.github.linpeilie.BaseMapper;
import org.dromara.love.domain.LoveUserDailyStudyDuration;
import org.dromara.love.domain.LoveUserDailyStudyDurationToLoveUserDailyStudyDurationVoMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__787.class,
    uses = {LoveUserDailyStudyDurationToLoveUserDailyStudyDurationVoMapper.class},
    imports = {}
)
public interface LoveUserDailyStudyDurationVoToLoveUserDailyStudyDurationMapper extends BaseMapper<LoveUserDailyStudyDurationVo, LoveUserDailyStudyDuration> {
}
