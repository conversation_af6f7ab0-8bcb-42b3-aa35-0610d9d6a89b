package org.dromara.love.domain;

import io.github.linpeilie.AutoMapperConfig__787;
import io.github.linpeilie.BaseMapper;
import org.dromara.love.domain.bo.LoveBookUnitBoToLoveBookUnitMapper;
import org.dromara.love.domain.vo.LoveBookUnitVo;
import org.dromara.love.domain.vo.LoveBookUnitVoToLoveBookUnitMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__787.class,
    uses = {LoveBookUnitBoToLoveBookUnitMapper.class,LoveBookUnitVoToLoveBookUnitMapper.class},
    imports = {}
)
public interface LoveBookUnitToLoveBookUnitVoMapper extends BaseMapper<LoveBookUnit, LoveBookUnitVo> {
}
