{"doc": " 1:1 复刻 py-fsrs fsrs.optimizer.Optimizer 的“接口与行为边界”。\n 由于 Java 环境无 torch/pandas/tqdm，本实现提供与 Python 版相同的 API，\n 但在不可用依赖情况下：\n - computeOptimalParameters(verbose) 返回 Scheduler.DEFAULT_PARAMETERS（当样本不足时 Python 也会回退）\n - computeOptimalRetention(parameters) 根据经验模拟流程复刻；若样本不足或缺失 reviewDuration 则抛出与 Python 文本等价的异常\n\n 注意：\n Python 原版 Optimizer 使用 PyTorch 训练参数。要实现完整的可训练版本，需要引入 Java 深度学习库（如 DJL/ND4J）并\n 重写矩阵运算与优化器。这超出当前仓库依赖范围。为保证 1:1 API 可用且不缺失功能入口，本类保留方法签名与逻辑分支，\n 并在运行期给出与 Python 异常一致的文案。\n", "fields": [], "enumConstants": [], "methods": [{"name": "computeOptimalParameters", "paramTypes": ["boolean"], "doc": " 复刻 Python: 如果有效的可训练 review 数量 < 512，返回默认参数。\n 否则（在 Python 中会进行训练），这里由于无 torch，直接返回 DEFAULT_PARAMETERS。\n"}, {"name": "computeOptimalRetention", "paramTypes": ["java.util.List"], "doc": " 复刻 Python 最优保持率接口。\n - 校验：至少 512 条 ReviewLog，且 reviewDuration 不能为 null\n - 模拟：在 Java 中重现 Python 逻辑（无 torch 依赖）\n"}], "constructors": []}