package org.dromara.love.domain.vo;

import javax.annotation.processing.Generated;
import org.dromara.love.domain.LoveUserStudySession;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-07T01:55:47+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (Microsoft)"
)
@Component
public class LoveUserStudySessionVoToLoveUserStudySessionMapperImpl implements LoveUserStudySessionVoToLoveUserStudySessionMapper {

    @Override
    public LoveUserStudySession convert(LoveUserStudySessionVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        LoveUserStudySession loveUserStudySession = new LoveUserStudySession();

        loveUserStudySession.setUserId( arg0.getUserId() );
        loveUserStudySession.setUnitId( arg0.getUnitId() );
        loveUserStudySession.setStartTime( arg0.getStartTime() );
        loveUserStudySession.setEndTime( arg0.getEndTime() );
        if ( arg0.getSessionStatus() != null ) {
            loveUserStudySession.setSessionStatus( arg0.getSessionStatus().intValue() );
        }
        if ( arg0.getTotalWords() != null ) {
            loveUserStudySession.setTotalWords( arg0.getTotalWords().intValue() );
        }
        if ( arg0.getCompletedWords() != null ) {
            loveUserStudySession.setCompletedWords( arg0.getCompletedWords().intValue() );
        }
        if ( arg0.getSessionDuration() != null ) {
            loveUserStudySession.setSessionDuration( arg0.getSessionDuration().intValue() );
        }
        loveUserStudySession.setLastActivityTime( arg0.getLastActivityTime() );
        if ( arg0.getEndType() != null ) {
            loveUserStudySession.setEndType( arg0.getEndType().intValue() );
        }

        return loveUserStudySession;
    }

    @Override
    public LoveUserStudySession convert(LoveUserStudySessionVo arg0, LoveUserStudySession arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setUserId( arg0.getUserId() );
        arg1.setUnitId( arg0.getUnitId() );
        arg1.setStartTime( arg0.getStartTime() );
        arg1.setEndTime( arg0.getEndTime() );
        if ( arg0.getSessionStatus() != null ) {
            arg1.setSessionStatus( arg0.getSessionStatus().intValue() );
        }
        else {
            arg1.setSessionStatus( null );
        }
        if ( arg0.getTotalWords() != null ) {
            arg1.setTotalWords( arg0.getTotalWords().intValue() );
        }
        else {
            arg1.setTotalWords( null );
        }
        if ( arg0.getCompletedWords() != null ) {
            arg1.setCompletedWords( arg0.getCompletedWords().intValue() );
        }
        else {
            arg1.setCompletedWords( null );
        }
        if ( arg0.getSessionDuration() != null ) {
            arg1.setSessionDuration( arg0.getSessionDuration().intValue() );
        }
        else {
            arg1.setSessionDuration( null );
        }
        arg1.setLastActivityTime( arg0.getLastActivityTime() );
        if ( arg0.getEndType() != null ) {
            arg1.setEndType( arg0.getEndType().intValue() );
        }
        else {
            arg1.setEndType( null );
        }

        return arg1;
    }
}
