package org.dromara.fsrs.v4;

import java.time.Instant;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.util.Objects;

/**
 * 1:1 复刻 py-fsrs fsrs.card.Card
 * 字段/行为与 Python 对齐。时间一律使用 UTC 的 ZonedDateTime。
 */
public final class Card {

    public enum State {
        Learning(1),
        Review(2),
        Relearning(3);

        public final int value;

        State(int value) {
            this.value = value;
        }

        public static State fromValue(int v) {
            for (State s : values()) {
                if (s.value == v) return s;
            }
            throw new IllegalArgumentException("Unknown State value: " + v);
        }
    }

    public long cardId;
    public State state;
    public Integer step; // 可为 null
    public Double stability; // 可为 null
    public Double difficulty; // 可为 null
    public ZonedDateTime due; // UTC
    public ZonedDateTime lastReview; // 可为 null

    /**
     * 与 Python __init__ 对齐的构造器（带默认值）
     */
    public Card() {
        this(null, State.Learning, null, null, null, null, null);
    }

    public Card(Long cardId,
                State state,
                Integer step,
                Double stability,
                Double difficulty,
                ZonedDateTime due,
                ZonedDateTime lastReview) {

        if (cardId == null) {
            // Python: int(datetime.now(timezone.utc).timestamp() * 1000)
            long nowMs = Instant.now().toEpochMilli();
            this.cardId = nowMs;
            try {
                // Python 为避免 id 冲突 sleep 1ms
                Thread.sleep(1);
            } catch (InterruptedException ignored) { }
        } else {
            this.cardId = cardId;
        }

        this.state = state == null ? State.Learning : state;

        if (this.state == State.Learning && step == null) {
            this.step = 0;
        } else {
            this.step = step;
        }

        this.stability = stability;
        this.difficulty = difficulty;

        if (due == null) {
            this.due = ZonedDateTime.now(ZoneOffset.UTC);
        } else {
            this.due = due;
        }

        this.lastReview = lastReview;
    }

    /**
     * Python 的 to_dict：返回 JSON 可序列化对象
     */
    public java.util.Map<String, Object> toMap() {
        java.util.Map<String, Object> m = new java.util.HashMap<>();
        m.put("card_id", this.cardId);
        m.put("state", this.state.value);
        m.put("step", this.step);
        m.put("stability", this.stability);
        m.put("difficulty", this.difficulty);
        m.put("due", this.due.toOffsetDateTime().toString());
        m.put("last_review", this.lastReview == null ? null : this.lastReview.toOffsetDateTime().toString());
        return m;
    }

    /**
     * Python 的 from_dict
     */
    public static Card fromMap(java.util.Map<String, Object> source) {
        Objects.requireNonNull(source, "source map is null");

        long cardId = Long.parseLong(String.valueOf(source.get("card_id")));
        State state = State.fromValue(Integer.parseInt(String.valueOf(source.get("state"))));

        Integer step = source.get("step") == null ? null : Integer.valueOf(String.valueOf(source.get("step")));

        Double stability = null;
        if (source.get("stability") != null) {
            String s = String.valueOf(source.get("stability"));
            if (!s.isEmpty()) stability = Double.valueOf(s);
        }

        Double difficulty = null;
        if (source.get("difficulty") != null) {
            String s = String.valueOf(source.get("difficulty"));
            if (!s.isEmpty()) difficulty = Double.valueOf(s);
        }

        ZonedDateTime due = ZonedDateTime.parse(String.valueOf(source.get("due")));

        ZonedDateTime lastReview = null;
        Object lr = source.get("last_review");
        if (lr != null) {
            String s = String.valueOf(lr);
            if (!s.isEmpty() && !"null".equalsIgnoreCase(s)) {
                lastReview = ZonedDateTime.parse(s);
            }
        }

        return new Card(cardId, state, step, stability, difficulty, due, lastReview);
    }
}