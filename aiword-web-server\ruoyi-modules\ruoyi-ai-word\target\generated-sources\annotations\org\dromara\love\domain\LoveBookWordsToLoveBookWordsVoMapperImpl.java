package org.dromara.love.domain;

import javax.annotation.processing.Generated;
import org.dromara.love.domain.vo.LoveBookWordsVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-07T01:55:47+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (Microsoft)"
)
@Component
public class LoveBookWordsToLoveBookWordsVoMapperImpl implements LoveBookWordsToLoveBookWordsVoMapper {

    @Override
    public LoveBookWordsVo convert(LoveBookWords arg0) {
        if ( arg0 == null ) {
            return null;
        }

        LoveBookWordsVo loveBookWordsVo = new LoveBookWordsVo();

        loveBookWordsVo.setId( arg0.getId() );
        if ( arg0.getWordBookId() != null ) {
            loveBookWordsVo.setWordBookId( String.valueOf( arg0.getWordBookId() ) );
        }
        if ( arg0.getWordBookUnitId() != null ) {
            loveBookWordsVo.setWordBookUnitId( String.valueOf( arg0.getWordBookUnitId() ) );
        }
        loveBookWordsVo.setName( arg0.getName() );
        loveBookWordsVo.setPhoneticSymbol( arg0.getPhoneticSymbol() );
        loveBookWordsVo.setVowelSeparation( arg0.getVowelSeparation() );
        loveBookWordsVo.setPath( arg0.getPath() );
        loveBookWordsVo.setParaphrase( arg0.getParaphrase() );

        return loveBookWordsVo;
    }

    @Override
    public LoveBookWordsVo convert(LoveBookWords arg0, LoveBookWordsVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        if ( arg0.getWordBookId() != null ) {
            arg1.setWordBookId( String.valueOf( arg0.getWordBookId() ) );
        }
        else {
            arg1.setWordBookId( null );
        }
        if ( arg0.getWordBookUnitId() != null ) {
            arg1.setWordBookUnitId( String.valueOf( arg0.getWordBookUnitId() ) );
        }
        else {
            arg1.setWordBookUnitId( null );
        }
        arg1.setName( arg0.getName() );
        arg1.setPhoneticSymbol( arg0.getPhoneticSymbol() );
        arg1.setVowelSeparation( arg0.getVowelSeparation() );
        arg1.setPath( arg0.getPath() );
        arg1.setParaphrase( arg0.getParaphrase() );

        return arg1;
    }
}
