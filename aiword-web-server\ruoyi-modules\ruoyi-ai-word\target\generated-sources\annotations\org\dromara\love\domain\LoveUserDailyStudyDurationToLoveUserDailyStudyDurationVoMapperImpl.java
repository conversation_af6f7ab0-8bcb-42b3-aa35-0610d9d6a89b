package org.dromara.love.domain;

import javax.annotation.processing.Generated;
import org.dromara.love.domain.vo.LoveUserDailyStudyDurationVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-07T01:55:47+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (Microsoft)"
)
@Component
public class LoveUserDailyStudyDurationToLoveUserDailyStudyDurationVoMapperImpl implements LoveUserDailyStudyDurationToLoveUserDailyStudyDurationVoMapper {

    @Override
    public LoveUserDailyStudyDurationVo convert(LoveUserDailyStudyDuration arg0) {
        if ( arg0 == null ) {
            return null;
        }

        LoveUserDailyStudyDurationVo loveUserDailyStudyDurationVo = new LoveUserDailyStudyDurationVo();

        loveUserDailyStudyDurationVo.setUserId( arg0.getUserId() );
        loveUserDailyStudyDurationVo.setStudyDate( arg0.getStudyDate() );
        loveUserDailyStudyDurationVo.setLastUpdateTime( arg0.getLastUpdateTime() );

        return loveUserDailyStudyDurationVo;
    }

    @Override
    public LoveUserDailyStudyDurationVo convert(LoveUserDailyStudyDuration arg0, LoveUserDailyStudyDurationVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setUserId( arg0.getUserId() );
        arg1.setStudyDate( arg0.getStudyDate() );
        arg1.setLastUpdateTime( arg0.getLastUpdateTime() );

        return arg1;
    }
}
