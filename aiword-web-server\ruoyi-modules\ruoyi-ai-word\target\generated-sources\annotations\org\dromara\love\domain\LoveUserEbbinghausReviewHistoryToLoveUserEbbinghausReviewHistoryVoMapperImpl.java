package org.dromara.love.domain;

import javax.annotation.processing.Generated;
import org.dromara.love.domain.vo.LoveUserEbbinghausReviewHistoryVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-07T01:55:47+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (Microsoft)"
)
@Component
public class LoveUserEbbinghausReviewHistoryToLoveUserEbbinghausReviewHistoryVoMapperImpl implements LoveUserEbbinghausReviewHistoryToLoveUserEbbinghausReviewHistoryVoMapper {

    @Override
    public LoveUserEbbinghausReviewHistoryVo convert(LoveUserEbbinghausReviewHistory arg0) {
        if ( arg0 == null ) {
            return null;
        }

        LoveUserEbbinghausReviewHistoryVo loveUserEbbinghausReviewHistoryVo = new LoveUserEbbinghausReviewHistoryVo();

        loveUserEbbinghausReviewHistoryVo.setId( arg0.getId() );
        loveUserEbbinghausReviewHistoryVo.setUserId( arg0.getUserId() );
        loveUserEbbinghausReviewHistoryVo.setWordId( arg0.getWordId() );
        loveUserEbbinghausReviewHistoryVo.setLearnedWordId( arg0.getLearnedWordId() );
        loveUserEbbinghausReviewHistoryVo.setStudySessionId( arg0.getStudySessionId() );
        loveUserEbbinghausReviewHistoryVo.setReviewLevel( arg0.getReviewLevel() );
        loveUserEbbinghausReviewHistoryVo.setExpectedReviewDate( arg0.getExpectedReviewDate() );
        loveUserEbbinghausReviewHistoryVo.setActualReviewDate( arg0.getActualReviewDate() );
        loveUserEbbinghausReviewHistoryVo.setIsOnTime( arg0.getIsOnTime() );
        loveUserEbbinghausReviewHistoryVo.setDelayHours( arg0.getDelayHours() );

        return loveUserEbbinghausReviewHistoryVo;
    }

    @Override
    public LoveUserEbbinghausReviewHistoryVo convert(LoveUserEbbinghausReviewHistory arg0, LoveUserEbbinghausReviewHistoryVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setWordId( arg0.getWordId() );
        arg1.setLearnedWordId( arg0.getLearnedWordId() );
        arg1.setStudySessionId( arg0.getStudySessionId() );
        arg1.setReviewLevel( arg0.getReviewLevel() );
        arg1.setExpectedReviewDate( arg0.getExpectedReviewDate() );
        arg1.setActualReviewDate( arg0.getActualReviewDate() );
        arg1.setIsOnTime( arg0.getIsOnTime() );
        arg1.setDelayHours( arg0.getDelayHours() );

        return arg1;
    }
}
