2025-08-04 00:08:09 [main] INFO  o.d.l.p.ai.impl.CozeProviderImpl - Coze工作流服务初始化成功
2025-08-04 00:08:09 [main] INFO  o.d.l.p.a.i.VolcengineProviderImpl - 火山引擎AI聊天服务初始化成功
2025-08-04 00:08:09 [main] INFO  o.d.l.p.ai.AiChatProviderFactory - 初始化AI聊天提供者工厂，可用提供者数量: 1
2025-08-04 00:08:09 [main] INFO  o.d.l.p.ai.AiChatProviderFactory - 提供者: 火山引擎, 优先级: 1
2025-08-04 00:08:09 [main] INFO  o.d.l.p.tts.impl.EdgeTTSProvider - Edge TTS可用性检查: 可用
2025-08-04 00:08:09 [main] INFO  o.d.l.p.tts.TTSProviderFactory - 初始化TTS提供商工厂，可用提供商数量: 3
2025-08-04 00:08:09 [main] INFO  o.d.l.p.tts.TTSProviderFactory - 提供商: 有道词典, 优先级: 1
2025-08-04 00:08:09 [main] INFO  o.d.l.p.tts.TTSProviderFactory - 提供商: 百度翻译, 优先级: 2
2025-08-04 00:08:09 [main] INFO  o.d.l.p.tts.TTSProviderFactory - 提供商: Edge TTS, 优先级: 100
2025-08-04 00:08:17 [main] INFO  o.d.l.p.ai.CozeProviderFactory - 初始化Coze工作流提供者工厂，可用提供者数量: 1
2025-08-04 00:08:17 [main] INFO  o.d.l.p.ai.CozeProviderFactory - 提供者: Coze, 优先级: 1
2025-08-04 02:16:09 [SpringApplicationShutdownHook] INFO  o.d.l.p.a.i.VolcengineProviderImpl - 火山引擎AI聊天服务已关闭
2025-08-04 02:17:04 [main] INFO  o.d.l.p.ai.impl.CozeProviderImpl - Coze工作流服务初始化成功
2025-08-04 02:17:05 [main] INFO  o.d.l.p.a.i.VolcengineProviderImpl - 火山引擎AI聊天服务初始化成功
2025-08-04 02:17:05 [main] INFO  o.d.l.p.ai.AiChatProviderFactory - 初始化AI聊天提供者工厂，可用提供者数量: 1
2025-08-04 02:17:05 [main] INFO  o.d.l.p.ai.AiChatProviderFactory - 提供者: 火山引擎, 优先级: 1
2025-08-04 02:17:05 [main] INFO  o.d.l.p.tts.impl.EdgeTTSProvider - Edge TTS可用性检查: 可用
2025-08-04 02:17:05 [main] INFO  o.d.l.p.tts.TTSProviderFactory - 初始化TTS提供商工厂，可用提供商数量: 3
2025-08-04 02:17:05 [main] INFO  o.d.l.p.tts.TTSProviderFactory - 提供商: 有道词典, 优先级: 1
2025-08-04 02:17:05 [main] INFO  o.d.l.p.tts.TTSProviderFactory - 提供商: 百度翻译, 优先级: 2
2025-08-04 02:17:05 [main] INFO  o.d.l.p.tts.TTSProviderFactory - 提供商: Edge TTS, 优先级: 100
2025-08-04 02:17:14 [main] INFO  o.d.l.p.ai.CozeProviderFactory - 初始化Coze工作流提供者工厂，可用提供者数量: 1
2025-08-04 02:17:14 [main] INFO  o.d.l.p.ai.CozeProviderFactory - 提供者: Coze, 优先级: 1
2025-08-04 02:25:30 [SpringApplicationShutdownHook] INFO  o.d.l.p.a.i.VolcengineProviderImpl - 火山引擎AI聊天服务已关闭
2025-08-04 02:26:26 [main] INFO  o.d.l.p.ai.impl.CozeProviderImpl - Coze工作流服务初始化成功
2025-08-04 02:26:26 [main] INFO  o.d.l.p.a.i.VolcengineProviderImpl - 火山引擎AI聊天服务初始化成功
2025-08-04 02:26:26 [main] INFO  o.d.l.p.ai.AiChatProviderFactory - 初始化AI聊天提供者工厂，可用提供者数量: 1
2025-08-04 02:26:26 [main] INFO  o.d.l.p.ai.AiChatProviderFactory - 提供者: 火山引擎, 优先级: 1
2025-08-04 02:26:26 [main] INFO  o.d.l.p.tts.impl.EdgeTTSProvider - Edge TTS可用性检查: 可用
2025-08-04 02:26:26 [main] INFO  o.d.l.p.tts.TTSProviderFactory - 初始化TTS提供商工厂，可用提供商数量: 3
2025-08-04 02:26:26 [main] INFO  o.d.l.p.tts.TTSProviderFactory - 提供商: 有道词典, 优先级: 1
2025-08-04 02:26:26 [main] INFO  o.d.l.p.tts.TTSProviderFactory - 提供商: 百度翻译, 优先级: 2
2025-08-04 02:26:26 [main] INFO  o.d.l.p.tts.TTSProviderFactory - 提供商: Edge TTS, 优先级: 100
2025-08-04 02:26:35 [main] INFO  o.d.l.p.ai.CozeProviderFactory - 初始化Coze工作流提供者工厂，可用提供者数量: 1
2025-08-04 02:26:35 [main] INFO  o.d.l.p.ai.CozeProviderFactory - 提供者: Coze, 优先级: 1
2025-08-04 02:58:48 [SpringApplicationShutdownHook] INFO  o.d.l.p.a.i.VolcengineProviderImpl - 火山引擎AI聊天服务已关闭
2025-08-04 02:59:38 [main] INFO  o.d.l.p.ai.impl.CozeProviderImpl - Coze工作流服务初始化成功
2025-08-04 02:59:38 [main] INFO  o.d.l.p.a.i.VolcengineProviderImpl - 火山引擎AI聊天服务初始化成功
2025-08-04 02:59:38 [main] INFO  o.d.l.p.ai.AiChatProviderFactory - 初始化AI聊天提供者工厂，可用提供者数量: 1
2025-08-04 02:59:38 [main] INFO  o.d.l.p.ai.AiChatProviderFactory - 提供者: 火山引擎, 优先级: 1
2025-08-04 02:59:39 [main] INFO  o.d.l.p.tts.impl.EdgeTTSProvider - Edge TTS可用性检查: 可用
2025-08-04 02:59:39 [main] INFO  o.d.l.p.tts.TTSProviderFactory - 初始化TTS提供商工厂，可用提供商数量: 3
2025-08-04 02:59:39 [main] INFO  o.d.l.p.tts.TTSProviderFactory - 提供商: 有道词典, 优先级: 1
2025-08-04 02:59:39 [main] INFO  o.d.l.p.tts.TTSProviderFactory - 提供商: 百度翻译, 优先级: 2
2025-08-04 02:59:39 [main] INFO  o.d.l.p.tts.TTSProviderFactory - 提供商: Edge TTS, 优先级: 100
2025-08-04 02:59:48 [main] INFO  o.d.l.p.ai.CozeProviderFactory - 初始化Coze工作流提供者工厂，可用提供者数量: 1
2025-08-04 02:59:48 [main] INFO  o.d.l.p.ai.CozeProviderFactory - 提供者: Coze, 优先级: 1
2025-08-04 21:58:44 [main] INFO  o.d.l.p.ai.impl.CozeProviderImpl - Coze工作流服务初始化成功
2025-08-04 21:58:44 [main] INFO  o.d.l.p.a.i.VolcengineProviderImpl - 火山引擎AI聊天服务初始化成功
2025-08-04 21:58:44 [main] INFO  o.d.l.p.ai.AiChatProviderFactory - 初始化AI聊天提供者工厂，可用提供者数量: 1
2025-08-04 21:58:44 [main] INFO  o.d.l.p.ai.AiChatProviderFactory - 提供者: 火山引擎, 优先级: 1
2025-08-04 21:58:44 [main] INFO  o.d.l.p.tts.impl.EdgeTTSProvider - Edge TTS可用性检查: 可用
2025-08-04 21:58:44 [main] INFO  o.d.l.p.tts.TTSProviderFactory - 初始化TTS提供商工厂，可用提供商数量: 3
2025-08-04 21:58:44 [main] INFO  o.d.l.p.tts.TTSProviderFactory - 提供商: 有道词典, 优先级: 1
2025-08-04 21:58:44 [main] INFO  o.d.l.p.tts.TTSProviderFactory - 提供商: 百度翻译, 优先级: 2
2025-08-04 21:58:44 [main] INFO  o.d.l.p.tts.TTSProviderFactory - 提供商: Edge TTS, 优先级: 100
2025-08-04 21:58:52 [main] INFO  o.d.l.p.ai.CozeProviderFactory - 初始化Coze工作流提供者工厂，可用提供者数量: 1
2025-08-04 21:58:52 [main] INFO  o.d.l.p.ai.CozeProviderFactory - 提供者: Coze, 优先级: 1
2025-08-04 22:14:56 [XNIO-1 task-6] INFO  o.d.l.c.client.AudioProxyController - TTS请求: 'unit', 语言: uk, 语速: 3
2025-08-04 22:14:56 [XNIO-1 task-6] INFO  o.d.l.p.tts.service.TTSService - 开始获取音频: 'unit', 语言: uk, 语速: 3
2025-08-04 22:14:56 [XNIO-1 task-6] INFO  o.d.l.p.tts.service.TTSService - 检查本地缓存: unit, 识别类型: word
2025-08-04 22:14:56 [XNIO-1 task-6] INFO  o.d.l.handler.LocalWordFileHandler - 尝试读取音频文件: unit, 类型: word, 清理后文件名: unit, 完整路径: D:\MyApplication\love-word\file\LocalWordFile\word\u\unit.mp3
2025-08-04 22:14:56 [XNIO-1 task-6] INFO  o.d.l.handler.LocalWordFileHandler - 成功读取音频文件: D:\MyApplication\love-word\file\LocalWordFile\word\u\unit.mp3, 文件大小: 137800 bytes
2025-08-04 22:14:56 [XNIO-1 task-6] INFO  o.d.l.p.tts.service.TTSService - 从word缓存读取音频成功: unit
2025-08-04 22:14:56 [XNIO-1 task-6] INFO  o.d.l.p.tts.service.TTSService - 从本地缓存获取音频: unit
2025-08-04 22:14:56 [XNIO-1 task-6] INFO  o.d.l.p.tts.service.TTSService - 本地缓存音频读取成功，大小: 137800 bytes
2025-08-04 22:14:56 [XNIO-1 task-6] INFO  o.d.l.c.client.AudioProxyController - 成功返回音频数据，来源: 本地缓存，大小: 137800 bytes
2025-08-04 22:14:58 [XNIO-1 task-5] INFO  o.d.l.c.client.AudioProxyController - TTS请求: 'unit', 语言: uk, 语速: 3
2025-08-04 22:14:58 [XNIO-1 task-5] INFO  o.d.l.p.tts.service.TTSService - 开始获取音频: 'unit', 语言: uk, 语速: 3
2025-08-04 22:14:58 [XNIO-1 task-5] INFO  o.d.l.p.tts.service.TTSService - 检查本地缓存: unit, 识别类型: word
2025-08-04 22:14:58 [XNIO-1 task-5] INFO  o.d.l.handler.LocalWordFileHandler - 尝试读取音频文件: unit, 类型: word, 清理后文件名: unit, 完整路径: D:\MyApplication\love-word\file\LocalWordFile\word\u\unit.mp3
2025-08-04 22:14:58 [XNIO-1 task-5] INFO  o.d.l.handler.LocalWordFileHandler - 成功读取音频文件: D:\MyApplication\love-word\file\LocalWordFile\word\u\unit.mp3, 文件大小: 137800 bytes
2025-08-04 22:14:58 [XNIO-1 task-5] INFO  o.d.l.p.tts.service.TTSService - 从word缓存读取音频成功: unit
2025-08-04 22:14:58 [XNIO-1 task-5] INFO  o.d.l.p.tts.service.TTSService - 从本地缓存获取音频: unit
2025-08-04 22:14:58 [XNIO-1 task-5] INFO  o.d.l.p.tts.service.TTSService - 本地缓存音频读取成功，大小: 137800 bytes
2025-08-04 22:14:58 [XNIO-1 task-5] INFO  o.d.l.c.client.AudioProxyController - 成功返回音频数据，来源: 本地缓存，大小: 137800 bytes
2025-08-04 22:15:01 [XNIO-1 task-5] INFO  o.d.l.c.client.AudioProxyController - TTS请求: 'unit', 语言: uk, 语速: 3
2025-08-04 22:15:01 [XNIO-1 task-5] INFO  o.d.l.p.tts.service.TTSService - 开始获取音频: 'unit', 语言: uk, 语速: 3
2025-08-04 22:15:01 [XNIO-1 task-5] INFO  o.d.l.p.tts.service.TTSService - 检查本地缓存: unit, 识别类型: word
2025-08-04 22:15:01 [XNIO-1 task-5] INFO  o.d.l.handler.LocalWordFileHandler - 尝试读取音频文件: unit, 类型: word, 清理后文件名: unit, 完整路径: D:\MyApplication\love-word\file\LocalWordFile\word\u\unit.mp3
2025-08-04 22:15:01 [XNIO-1 task-5] INFO  o.d.l.handler.LocalWordFileHandler - 成功读取音频文件: D:\MyApplication\love-word\file\LocalWordFile\word\u\unit.mp3, 文件大小: 137800 bytes
2025-08-04 22:15:01 [XNIO-1 task-5] INFO  o.d.l.p.tts.service.TTSService - 从word缓存读取音频成功: unit
2025-08-04 22:15:01 [XNIO-1 task-5] INFO  o.d.l.p.tts.service.TTSService - 从本地缓存获取音频: unit
2025-08-04 22:15:01 [XNIO-1 task-5] INFO  o.d.l.p.tts.service.TTSService - 本地缓存音频读取成功，大小: 137800 bytes
2025-08-04 22:15:01 [XNIO-1 task-5] INFO  o.d.l.c.client.AudioProxyController - 成功返回音频数据，来源: 本地缓存，大小: 137800 bytes
2025-08-04 22:15:05 [XNIO-1 task-5] INFO  o.d.l.c.client.AudioProxyController - TTS请求: 'unit', 语言: uk, 语速: 3
2025-08-04 22:15:05 [XNIO-1 task-5] INFO  o.d.l.p.tts.service.TTSService - 开始获取音频: 'unit', 语言: uk, 语速: 3
2025-08-04 22:15:05 [XNIO-1 task-5] INFO  o.d.l.p.tts.service.TTSService - 检查本地缓存: unit, 识别类型: word
2025-08-04 22:15:05 [XNIO-1 task-5] INFO  o.d.l.handler.LocalWordFileHandler - 尝试读取音频文件: unit, 类型: word, 清理后文件名: unit, 完整路径: D:\MyApplication\love-word\file\LocalWordFile\word\u\unit.mp3
2025-08-04 22:15:05 [XNIO-1 task-5] INFO  o.d.l.handler.LocalWordFileHandler - 成功读取音频文件: D:\MyApplication\love-word\file\LocalWordFile\word\u\unit.mp3, 文件大小: 137800 bytes
2025-08-04 22:15:05 [XNIO-1 task-5] INFO  o.d.l.p.tts.service.TTSService - 从word缓存读取音频成功: unit
2025-08-04 22:15:05 [XNIO-1 task-5] INFO  o.d.l.p.tts.service.TTSService - 从本地缓存获取音频: unit
2025-08-04 22:15:05 [XNIO-1 task-5] INFO  o.d.l.p.tts.service.TTSService - 本地缓存音频读取成功，大小: 137800 bytes
2025-08-04 22:15:05 [XNIO-1 task-5] INFO  o.d.l.c.client.AudioProxyController - 成功返回音频数据，来源: 本地缓存，大小: 137800 bytes
2025-08-04 22:15:08 [XNIO-1 task-5] INFO  o.d.l.c.client.AudioProxyController - TTS请求: 'unit', 语言: uk, 语速: 3
2025-08-04 22:15:08 [XNIO-1 task-5] INFO  o.d.l.p.tts.service.TTSService - 开始获取音频: 'unit', 语言: uk, 语速: 3
2025-08-04 22:15:08 [XNIO-1 task-5] INFO  o.d.l.p.tts.service.TTSService - 检查本地缓存: unit, 识别类型: word
2025-08-04 22:15:08 [XNIO-1 task-5] INFO  o.d.l.handler.LocalWordFileHandler - 尝试读取音频文件: unit, 类型: word, 清理后文件名: unit, 完整路径: D:\MyApplication\love-word\file\LocalWordFile\word\u\unit.mp3
2025-08-04 22:15:08 [XNIO-1 task-5] INFO  o.d.l.handler.LocalWordFileHandler - 成功读取音频文件: D:\MyApplication\love-word\file\LocalWordFile\word\u\unit.mp3, 文件大小: 137800 bytes
2025-08-04 22:15:08 [XNIO-1 task-5] INFO  o.d.l.p.tts.service.TTSService - 从word缓存读取音频成功: unit
2025-08-04 22:15:08 [XNIO-1 task-5] INFO  o.d.l.p.tts.service.TTSService - 从本地缓存获取音频: unit
2025-08-04 22:15:08 [XNIO-1 task-5] INFO  o.d.l.p.tts.service.TTSService - 本地缓存音频读取成功，大小: 137800 bytes
2025-08-04 22:15:08 [XNIO-1 task-5] INFO  o.d.l.c.client.AudioProxyController - 成功返回音频数据，来源: 本地缓存，大小: 137800 bytes
