package org.dromara.love.domain.vo;

import io.github.linpeilie.AutoMapperConfig__787;
import io.github.linpeilie.BaseMapper;
import org.dromara.love.domain.LoveBookTextBook;
import org.dromara.love.domain.LoveBookTextBookToLoveBookTextBookVoMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__787.class,
    uses = {LoveBookTextBookToLoveBookTextBookVoMapper.class},
    imports = {}
)
public interface LoveBookTextBookVoToLoveBookTextBookMapper extends BaseMapper<LoveBookTextBookVo, LoveBookTextBook> {
}
