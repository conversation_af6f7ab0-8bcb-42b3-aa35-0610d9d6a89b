{"doc": " 1:1 复刻 py-fsrs fsrs.card.Card\n 字段/行为与 Python 对齐。时间一律使用 UTC 的 ZonedDateTime。\n", "fields": [], "enumConstants": [], "methods": [{"name": "toMap", "paramTypes": [], "doc": " Python 的 to_dict：返回 JSON 可序列化对象\n"}, {"name": "fromMap", "paramTypes": ["java.util.Map"], "doc": " Python 的 from_dict\n"}], "constructors": [{"name": "<init>", "paramTypes": [], "doc": " 与 Python __init__ 对齐的构造器（带默认值）\n"}]}