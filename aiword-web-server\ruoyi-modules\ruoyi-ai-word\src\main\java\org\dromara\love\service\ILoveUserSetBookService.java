package org.dromara.love.service;

import com.baomidou.mybatisplus.extension.service.IService;
import jakarta.validation.constraints.NotNull;
import org.dromara.love.domain.LoveUserSetBook;
import org.dromara.love.domain.vo.LoveUserSetBookVo;
import org.dromara.love.domain.bo.LoveUserSetBookBo;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.love.domain.vo.UserBookCollectionVo;

import java.util.Collection;
import java.util.List;

/**
 * 学习-用户设置书籍Service接口
 *
 * <AUTHOR> Li
 * @date 2025-06-16
 */
public interface ILoveUserSetBookService extends IService<LoveUserSetBook> {

    /**
     * 查询学习-用户设置书籍
     *
     * @param id 主键
     * @return 学习-用户设置书籍
     */
    LoveUserSetBookVo queryById(Long id);

    /**
     * 分页查询学习-用户设置书籍列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 学习-用户设置书籍分页列表
     */
    TableDataInfo<LoveUserSetBookVo> queryPageList(LoveUserSetBookBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的学习-用户设置书籍列表
     *
     * @param bo 查询条件
     * @return 学习-用户设置书籍列表
     */
    List<LoveUserSetBookVo> queryList(LoveUserSetBookBo bo);

    /**
     * 新增学习-用户设置书籍
     *
     * @param bo 学习-用户设置书籍
     * @return 是否新增成功
     */
    Boolean insertByBo(LoveUserSetBookBo bo);

    /**
     * 修改学习-用户设置书籍
     *
     * @param bo 学习-用户设置书籍
     * @return 是否修改成功
     */
    Boolean updateByBo(LoveUserSetBookBo bo);

    /**
     * 校验并批量删除学习-用户设置书籍信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 按用户 ID 获取已设置书籍信息（按record_type分组）
     *
     * @param userId 用户 ID
     * @return {@link UserBookCollectionVo} 按record_type分组的书籍集合
     */
    UserBookCollectionVo getInfoByUserId(@NotNull(message = "主键不能为空") Long userId);
}
