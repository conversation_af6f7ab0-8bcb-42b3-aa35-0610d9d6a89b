package org.dromara.love.domain;

import org.dromara.common.mybatis.core.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 学习-用户设置书籍对象 love_user_set_book
 *
 * <AUTHOR>
 * @date 2025-06-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("love_user_set_book")
public class LoveUserSetBook extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 学段ID
     */
    private Long bookStudyFieldId;

    /**
     * 学段名称
     */
    private String bookStudyFieldName;

    /**
     * 类型ID
     */
    private Long bookTypeId;

    /**
     * 类型名称
     */
    private String bookTypeName;

    /**
     * 书籍ID
     */
    private Long bookTextBookId;

    /**
     * 书籍名称
     */
    private String bookTextBookName;

    /**
     * 记录类型(learning/review)
     */
    private String recordType;

    /**
     * 是否当前正在学习
     */
    private Boolean isActive;

    /**
     * 删除标志
     */
    @TableLogic
    private Long delFlag;

}
