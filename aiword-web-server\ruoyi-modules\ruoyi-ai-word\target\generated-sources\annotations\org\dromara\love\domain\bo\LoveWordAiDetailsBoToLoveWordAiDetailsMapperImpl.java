package org.dromara.love.domain.bo;

import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.dromara.love.domain.LoveWordAiDetails;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-07T01:55:47+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (Microsoft)"
)
@Component
public class LoveWordAiDetailsBoToLoveWordAiDetailsMapperImpl implements LoveWordAiDetailsBoToLoveWordAiDetailsMapper {

    @Override
    public LoveWordAiDetails convert(LoveWordAiDetailsBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        LoveWordAiDetails loveWordAiDetails = new LoveWordAiDetails();

        loveWordAiDetails.setSearchValue( arg0.getSearchValue() );
        loveWordAiDetails.setCreateDept( arg0.getCreateDept() );
        loveWordAiDetails.setCreateBy( arg0.getCreateBy() );
        loveWordAiDetails.setCreateTime( arg0.getCreateTime() );
        loveWordAiDetails.setUpdateBy( arg0.getUpdateBy() );
        loveWordAiDetails.setUpdateTime( arg0.getUpdateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            loveWordAiDetails.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        loveWordAiDetails.setId( arg0.getId() );
        loveWordAiDetails.setWordId( arg0.getWordId() );
        loveWordAiDetails.setAiDetails( arg0.getAiDetails() );

        return loveWordAiDetails;
    }

    @Override
    public LoveWordAiDetails convert(LoveWordAiDetailsBo arg0, LoveWordAiDetails arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setId( arg0.getId() );
        arg1.setWordId( arg0.getWordId() );
        arg1.setAiDetails( arg0.getAiDetails() );

        return arg1;
    }
}
