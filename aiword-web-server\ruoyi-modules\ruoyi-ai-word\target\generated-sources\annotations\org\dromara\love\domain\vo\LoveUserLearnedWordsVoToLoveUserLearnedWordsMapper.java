package org.dromara.love.domain.vo;

import io.github.linpeilie.AutoMapperConfig__787;
import io.github.linpeilie.BaseMapper;
import org.dromara.love.domain.LoveUserLearnedWords;
import org.dromara.love.domain.LoveUserLearnedWordsToLoveUserLearnedWordsVoMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__787.class,
    uses = {LoveUserLearnedWordsToLoveUserLearnedWordsVoMapper.class},
    imports = {}
)
public interface LoveUserLearnedWordsVoToLoveUserLearnedWordsMapper extends BaseMapper<LoveUserLearnedWordsVo, LoveUserLearnedWords> {
}
