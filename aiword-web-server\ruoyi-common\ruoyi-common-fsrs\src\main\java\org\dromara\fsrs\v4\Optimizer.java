package org.dromara.fsrs.v4;

import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.util.*;

/**
 * 1:1 复刻 py-fsrs fsrs.optimizer.Optimizer 的“接口与行为边界”。
 * 由于 Java 环境无 torch/pandas/tqdm，本实现提供与 Python 版相同的 API，
 * 但在不可用依赖情况下：
 * - computeOptimalParameters(verbose) 返回 Scheduler.DEFAULT_PARAMETERS（当样本不足时 Python 也会回退）
 * - computeOptimalRetention(parameters) 根据经验模拟流程复刻；若样本不足或缺失 reviewDuration 则抛出与 Python 文本等价的异常
 *
 * 注意：
 * Python 原版 Optimizer 使用 PyTorch 训练参数。要实现完整的可训练版本，需要引入 Java 深度学习库（如 DJL/ND4J）并
 * 重写矩阵运算与优化器。这超出当前仓库依赖范围。为保证 1:1 API 可用且不缺失功能入口，本类保留方法签名与逻辑分支，
 * 并在运行期给出与 Python 异常一致的文案。
 */
public final class Optimizer {

    private final List<ReviewLog> reviewLogs;
    // 训练序列：cardId -> 按 reviewDatetime 升序的 [ [datetime, rating, duration], recall ]
    private final Map<Long, List<Object[]>> revlogsTrain;

    public Optimizer(Collection<ReviewLog> reviewLogs) {
        this.reviewLogs = new ArrayList<>(Objects.requireNonNull(reviewLogs, "reviewLogs"));
        this.revlogsTrain = formatRevlogs();
    }

    private Map<Long, List<Object[]>> formatRevlogs() {
        Map<Long, List<Object[]>> map = new HashMap<>();
        for (ReviewLog rl : this.reviewLogs) {
            long cardId = rl.cardId;
            ReviewLog.Rating rating = rl.rating;
            ZonedDateTime reviewDatetime = rl.reviewDatetime;
            Integer reviewDuration = rl.reviewDuration;

            int recall = (rating == ReviewLog.Rating.Again) ? 0 : 1;
            Object[] datum = new Object[]{ new Object[]{ reviewDatetime, rating, reviewDuration }, recall };

            map.computeIfAbsent(cardId, k -> new ArrayList<>()).add(datum);
        }
        // 每个卡片序列按时间排序
        for (List<Object[]> list : map.values()) {
            list.sort(Comparator.comparing(o -> (ZonedDateTime)((Object[])o[0])[0]));
        }
        // 按卡片起始时间顺序排序（Python 中是 dict(sorted(...)) 的效果）
        Map<Long, List<Object[]>> sorted = new TreeMap<>(Comparator.naturalOrder());
        sorted.putAll(map);
        return sorted;
    }

    /**
     * 复刻 Python: 如果有效的可训练 review 数量 < 512，返回默认参数。
     * 否则（在 Python 中会进行训练），这里由于无 torch，直接返回 DEFAULT_PARAMETERS。
     */
    public List<Double> computeOptimalParameters(boolean verbose) {
        int numReviews = countNonSameDayReviews();
        if (numReviews < 512) {
            return toList(Scheduler.DEFAULT_PARAMETERS);
        }
        // Java 无 torch，直接返回 DEFAULT_PARAMETERS。保持 API 一致，不破坏上层调用。
        return toList(Scheduler.DEFAULT_PARAMETERS);
    }

    /**
     * 复刻 Python 最优保持率接口。
     * - 校验：至少 512 条 ReviewLog，且 reviewDuration 不能为 null
      * - 模拟：在 Java 中重现 Python 逻辑（无 torch 依赖）
     */
    public double computeOptimalRetention(List<Double> parameters) {
        validateReviewLogsForRetention();

        final int NUM_CARDS_SIMULATE = 1000;
        final double[] DESIRED_RETENTIONS = new double[]{0.7, 0.75, 0.8, 0.85, 0.9, 0.95};

        Map<String, Double> probsAndCosts = computeProbsAndCosts();

        double bestCost = Double.POSITIVE_INFINITY;
        double bestRetention = DESIRED_RETENTIONS[0];

        for (double r : DESIRED_RETENTIONS) {
            double cost = simulateCost(
                    r,
                    parameters == null || parameters.isEmpty() ? toList(Scheduler.DEFAULT_PARAMETERS) : parameters,
                    NUM_CARDS_SIMULATE,
                    probsAndCosts
            );
            if (cost < bestCost) {
                bestCost = cost;
                bestRetention = r;
            }
        }
        return bestRetention;
    }

    // 统计“非同日”的评审次数（对应 Python 中 _num_reviews）
    private int countNonSameDayReviews() {
        Scheduler scheduler = new Scheduler();
        int num = 0;

        for (Map.Entry<Long, List<Object[]>> e : this.revlogsTrain.entrySet()) {
            List<Object[]> hist = e.getValue();
            Card card = null;
            for (int i = 0; i < hist.size(); i++) {
                Object[] review = hist.get(i);
                ZonedDateTime dt = (ZonedDateTime)((Object[])review[0])[0];
                ReviewLog.Rating rating = (ReviewLog.Rating)((Object[])review[0])[1];

                if (i == 0) {
                    card = new Card(e.getKey(), Card.State.Learning, null, null, null, dt, null);
                }
                if (card.lastReview != null) {
                    long days = java.time.Duration.between(card.lastReview, dt).toDays();
                    if (days > 0) num++;
                }
                Map.Entry<Card, ReviewLog> res = scheduler.reviewCard(card, rating, dt, null);
                card = res.getKey();
            }
        }
        return num;
    }

    private Map<String, Double> computeProbsAndCosts() {
        // 等价 Python：先按 cardId, reviewDatetime 排序
        List<ReviewLog> list = new ArrayList<>(this.reviewLogs);
        list.sort(Comparator.comparingLong((ReviewLog r) -> r.cardId)
                .thenComparing(r -> r.reviewDatetime));

        Map<String, Double> out = new HashMap<>();

        // 第一次评审（每个 cardId 的第一条）
        Map<Long, ReviewLog> firstByCard = new LinkedHashMap<>();
        for (ReviewLog r : list) {
            firstByCard.putIfAbsent(r.cardId, r);
        }
        Collection<ReviewLog> firstReviews = firstByCard.values();
        long numFirst = firstReviews.size();
        long numFirstAgain = firstReviews.stream().filter(r -> r.rating == ReviewLog.Rating.Again).count();
        long numFirstHard = firstReviews.stream().filter(r -> r.rating == ReviewLog.Rating.Hard).count();
        long numFirstGood = firstReviews.stream().filter(r -> r.rating == ReviewLog.Rating.Good).count();
        long numFirstEasy = firstReviews.stream().filter(r -> r.rating == ReviewLog.Rating.Easy).count();

        double probFirstAgain = numFirst == 0 ? 0.0 : (double) numFirstAgain / numFirst;
        double probFirstHard = numFirst == 0 ? 0.0 : (double) numFirstHard / numFirst;
        double probFirstGood = numFirst == 0 ? 0.0 : (double) numFirstGood / numFirst;
        double probFirstEasy = numFirst == 0 ? 0.0 : (double) numFirstEasy / numFirst;

        out.put("prob_first_again", probFirstAgain);
        out.put("prob_first_hard", probFirstHard);
        out.put("prob_first_good", probFirstGood);
        out.put("prob_first_easy", probFirstEasy);

        // 第一次成本（平均时长）
        out.put("avg_first_again_review_duration", avg(firstReviews.stream().filter(r -> r.rating == ReviewLog.Rating.Again).map(r -> r.reviewDuration)));
        out.put("avg_first_hard_review_duration", avg(firstReviews.stream().filter(r -> r.rating == ReviewLog.Rating.Hard).map(r -> r.reviewDuration)));
        out.put("avg_first_good_review_duration", avg(firstReviews.stream().filter(r -> r.rating == ReviewLog.Rating.Good).map(r -> r.reviewDuration)));
        out.put("avg_first_easy_review_duration", avg(firstReviews.stream().filter(r -> r.rating == ReviewLog.Rating.Easy).map(r -> r.reviewDuration)));

        // 非第一次的评审
        // 按 cardId 聚类
        Map<Long, List<ReviewLog>> byCard = new LinkedHashMap<>();
        for (ReviewLog r : list) byCard.computeIfAbsent(r.cardId, k -> new ArrayList<>()).add(r);
        List<ReviewLog> nonFirst = new ArrayList<>();
        for (List<ReviewLog> seq : byCard.values()) {
            if (seq.size() >= 2) nonFirst.addAll(seq.subList(1, seq.size()));
        }

        long numHard = nonFirst.stream().filter(r -> r.rating == ReviewLog.Rating.Hard).count();
        long numGood = nonFirst.stream().filter(r -> r.rating == ReviewLog.Rating.Good).count();
        long numEasy = nonFirst.stream().filter(r -> r.rating == ReviewLog.Rating.Easy).count();
        long numRecall = numHard + numGood + numEasy;

        double probHard = numRecall == 0 ? 0.0 : (double) numHard / numRecall;
        double probGood = numRecall == 0 ? 0.0 : (double) numGood / numRecall;
        double probEasy = numRecall == 0 ? 0.0 : (double) numEasy / numRecall;

        out.put("prob_hard", probHard);
        out.put("prob_good", probGood);
        out.put("prob_easy", probEasy);

        out.put("avg_again_review_duration", avg(nonFirst.stream().filter(r -> r.rating == ReviewLog.Rating.Again).map(r -> r.reviewDuration)));
        out.put("avg_hard_review_duration", avg(nonFirst.stream().filter(r -> r.rating == ReviewLog.Rating.Hard).map(r -> r.reviewDuration)));
        out.put("avg_good_review_duration", avg(nonFirst.stream().filter(r -> r.rating == ReviewLog.Rating.Good).map(r -> r.reviewDuration)));
        out.put("avg_easy_review_duration", avg(nonFirst.stream().filter(r -> r.rating == ReviewLog.Rating.Easy).map(r -> r.reviewDuration)));

        return out;
    }

    private static double avg(java.util.stream.Stream<Integer> s) {
        int[] sumCount = s.filter(Objects::nonNull).mapToInt(Integer::intValue).collect(() -> new int[]{0,0},
                (acc, v) -> { acc[0] += v; acc[1] += 1; },
                (a, b) -> { a[0] += b[0]; a[1] += b[1]; });
        if (sumCount[1] == 0) return 0.0;
        return (double) sumCount[0] / sumCount[1];
    }

    private double simulateCost(double desiredRetention,
                                List<Double> parameters,
                                int numCardsSimulate,
                                Map<String, Double> probsAndCosts) {

        Random rng = new Random(42);

        ZonedDateTime start = ZonedDateTime.of(2025,1,1,0,0,0,0, ZoneOffset.UTC);
        ZonedDateTime end = ZonedDateTime.of(2026,1,1,0,0,0,0, ZoneOffset.UTC);

        Scheduler scheduler = new Scheduler(
                toArray(parameters),
                desiredRetention,
                List.of(java.time.Duration.ofMinutes(1), java.time.Duration.ofMinutes(10)),
                List.of(java.time.Duration.ofMinutes(10)),
                36500,
                false
        );

        double probFirstAgain = probsAndCosts.getOrDefault("prob_first_again", 0.0);
        double probFirstHard = probsAndCosts.getOrDefault("prob_first_hard", 0.0);
        double probFirstGood = probsAndCosts.getOrDefault("prob_first_good", 0.0);
        double probFirstEasy = probsAndCosts.getOrDefault("prob_first_easy", 0.0);

        double avgFirstAgain = probsAndCosts.getOrDefault("avg_first_again_review_duration", 0.0);
        double avgFirstHard = probsAndCosts.getOrDefault("avg_first_hard_review_duration", 0.0);
        double avgFirstGood = probsAndCosts.getOrDefault("avg_first_good_review_duration", 0.0);
        double avgFirstEasy = probsAndCosts.getOrDefault("avg_first_easy_review_duration", 0.0);

        double probHard = probsAndCosts.getOrDefault("prob_hard", 0.0);
        double probGood = probsAndCosts.getOrDefault("prob_good", 0.0);
        double probEasy = probsAndCosts.getOrDefault("prob_easy", 0.0);

        double avgAgain = probsAndCosts.getOrDefault("avg_again_review_duration", 0.0);
        double avgHard = probsAndCosts.getOrDefault("avg_hard_review_duration", 0.0);
        double avgGood = probsAndCosts.getOrDefault("avg_good_review_duration", 0.0);
        double avgEasy = probsAndCosts.getOrDefault("avg_easy_review_duration", 0.0);

        double cost = 0.0;

        for (int i = 0; i < numCardsSimulate; i++) {
            Card card = new Card();
            ZonedDateTime curr = start;

            while (curr.isBefore(end)) {
                ReviewLog.Rating rating;

                if (curr.equals(start)) {
                    rating = pick(rng, new ReviewLog.Rating[]{
                            ReviewLog.Rating.Again, ReviewLog.Rating.Hard, ReviewLog.Rating.Good, ReviewLog.Rating.Easy
                    }, new double[]{probFirstAgain, probFirstHard, probFirstGood, probFirstEasy});

                    if (rating == ReviewLog.Rating.Again) cost += avgFirstAgain;
                    else if (rating == ReviewLog.Rating.Hard) cost += avgFirstHard;
                    else if (rating == ReviewLog.Rating.Good) cost += avgFirstGood;
                    else if (rating == ReviewLog.Rating.Easy) cost += avgFirstEasy;

                } else {
                    // 先判断是否 recall
                    double r = rng.nextDouble();
                    if (r < desiredRetention) {
                        // 在 Hard/Good/Easy 中抽
                        rating = pick(rng, new ReviewLog.Rating[]{
                                ReviewLog.Rating.Hard, ReviewLog.Rating.Good, ReviewLog.Rating.Easy
                        }, new double[]{probHard, probGood, probEasy});
                    } else {
                        rating = ReviewLog.Rating.Again;
                    }

                    if (rating == ReviewLog.Rating.Again) cost += avgAgain;
                    else if (rating == ReviewLog.Rating.Hard) cost += avgHard;
                    else if (rating == ReviewLog.Rating.Good) cost += avgGood;
                    else if (rating == ReviewLog.Rating.Easy) cost += avgEasy;
                }

                Map.Entry<Card, ReviewLog> res = scheduler.reviewCard(card, rating, curr, null);
                card = res.getKey();
                curr = card.due;
            }
        }

        double totalKnowledge = desiredRetention * numCardsSimulate;
        return cost / Math.max(totalKnowledge, 1e-9);
    }

    private static ReviewLog.Rating pick(Random rng, ReviewLog.Rating[] items, double[] weights) {
        double sum = 0.0;
        for (double w : weights) sum += w;
        if (sum <= 0) {
            // 无分布时，退化为均匀随机
            return items[rng.nextInt(items.length)];
        }
        double r = rng.nextDouble() * sum;
        double acc = 0.0;
        for (int i = 0; i < items.length; i++) {
            acc += weights[i];
            if (r <= acc) return items[i];
        }
        return items[items.length - 1];
    }

    private void validateReviewLogsForRetention() {
        if (this.reviewLogs.size() < 512) {
            throw new IllegalArgumentException(
                    "Not enough ReviewLog's: at least 512 ReviewLog objects are required to compute optimal retention");
        }
        for (ReviewLog rl : this.reviewLogs) {
            if (rl.reviewDuration == null) {
                throw new IllegalArgumentException(
                        "ReviewLog.review_duration cannot be None when computing optimal retention");
            }
        }
    }

    private static List<Double> toList(double[] arr) {
        List<Double> list = new ArrayList<>(arr.length);
        for (double v : arr) list.add(v);
        return list;
    }

    private static double[] toArray(List<Double> list) {
        double[] arr = new double[list.size()];
        for (int i = 0; i < list.size(); i++) arr[i] = list.get(i);
        return arr;
    }
}