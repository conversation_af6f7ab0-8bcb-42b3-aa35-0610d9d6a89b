package org.dromara.love.domain;

import io.github.linpeilie.AutoMapperConfig__787;
import io.github.linpeilie.BaseMapper;
import org.dromara.love.domain.bo.LoveUserDailyStudyDurationBoToLoveUserDailyStudyDurationMapper;
import org.dromara.love.domain.vo.LoveUserDailyStudyDurationVo;
import org.dromara.love.domain.vo.LoveUserDailyStudyDurationVoToLoveUserDailyStudyDurationMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__787.class,
    uses = {LoveUserDailyStudyDurationVoToLoveUserDailyStudyDurationMapper.class,LoveUserDailyStudyDurationBoToLoveUserDailyStudyDurationMapper.class},
    imports = {}
)
public interface LoveUserDailyStudyDurationToLoveUserDailyStudyDurationVoMapper extends BaseMapper<LoveUserDailyStudyDuration, LoveUserDailyStudyDurationVo> {
}
