package org.dromara.love.domain;

import javax.annotation.processing.Generated;
import org.dromara.love.domain.vo.LoveBookTextBookVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-07T01:55:47+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (Microsoft)"
)
@Component
public class LoveBookTextBookToLoveBookTextBookVoMapperImpl implements LoveBookTextBookToLoveBookTextBookVoMapper {

    @Override
    public LoveBookTextBookVo convert(LoveBookTextBook arg0) {
        if ( arg0 == null ) {
            return null;
        }

        LoveBookTextBookVo loveBookTextBookVo = new LoveBookTextBookVo();

        loveBookTextBookVo.setId( arg0.getId() );
        loveBookTextBookVo.setName( arg0.getName() );

        return loveBookTextBookVo;
    }

    @Override
    public LoveBookTextBookVo convert(LoveBookTextBook arg0, LoveBookTextBookVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setName( arg0.getName() );

        return arg1;
    }
}
