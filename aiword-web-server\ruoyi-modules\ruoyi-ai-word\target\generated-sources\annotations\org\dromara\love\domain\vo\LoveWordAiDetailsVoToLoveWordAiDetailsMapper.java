package org.dromara.love.domain.vo;

import io.github.linpeilie.AutoMapperConfig__787;
import io.github.linpeilie.BaseMapper;
import org.dromara.love.domain.LoveWordAiDetails;
import org.dromara.love.domain.LoveWordAiDetailsToLoveWordAiDetailsVoMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__787.class,
    uses = {LoveWordAiDetailsToLoveWordAiDetailsVoMapper.class},
    imports = {}
)
public interface LoveWordAiDetailsVoToLoveWordAiDetailsMapper extends BaseMapper<LoveWordAiDetailsVo, LoveWordAiDetails> {
}
