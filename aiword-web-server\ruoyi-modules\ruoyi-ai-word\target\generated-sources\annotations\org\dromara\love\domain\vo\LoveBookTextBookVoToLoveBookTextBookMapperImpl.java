package org.dromara.love.domain.vo;

import javax.annotation.processing.Generated;
import org.dromara.love.domain.LoveBookTextBook;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-07T01:55:47+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (Microsoft)"
)
@Component
public class LoveBookTextBookVoToLoveBookTextBookMapperImpl implements LoveBookTextBookVoToLoveBookTextBookMapper {

    @Override
    public LoveBookTextBook convert(LoveBookTextBookVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        LoveBookTextBook loveBookTextBook = new LoveBookTextBook();

        loveBookTextBook.setId( arg0.getId() );
        loveBookTextBook.setName( arg0.getName() );

        return loveBookTextBook;
    }

    @Override
    public LoveBookTextBook convert(LoveBookTextBookVo arg0, LoveBookTextBook arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setName( arg0.getName() );

        return arg1;
    }
}
