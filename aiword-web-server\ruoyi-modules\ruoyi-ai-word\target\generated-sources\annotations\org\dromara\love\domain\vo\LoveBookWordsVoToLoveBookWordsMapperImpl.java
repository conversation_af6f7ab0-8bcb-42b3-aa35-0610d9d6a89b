package org.dromara.love.domain.vo;

import javax.annotation.processing.Generated;
import org.dromara.love.domain.LoveBookWords;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-07T01:55:47+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (Microsoft)"
)
@Component
public class LoveBookWordsVoToLoveBookWordsMapperImpl implements LoveBookWordsVoToLoveBookWordsMapper {

    @Override
    public LoveBookWords convert(LoveBookWordsVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        LoveBookWords loveBookWords = new LoveBookWords();

        loveBookWords.setId( arg0.getId() );
        if ( arg0.getWordBookId() != null ) {
            loveBookWords.setWordBookId( Long.parseLong( arg0.getWordBookId() ) );
        }
        if ( arg0.getWordBookUnitId() != null ) {
            loveBookWords.setWordBookUnitId( Long.parseLong( arg0.getWordBookUnitId() ) );
        }
        loveBookWords.setName( arg0.getName() );
        loveBookWords.setPhoneticSymbol( arg0.getPhoneticSymbol() );
        loveBookWords.setVowelSeparation( arg0.getVowelSeparation() );
        loveBookWords.setPath( arg0.getPath() );
        loveBookWords.setParaphrase( arg0.getParaphrase() );

        return loveBookWords;
    }

    @Override
    public LoveBookWords convert(LoveBookWordsVo arg0, LoveBookWords arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        if ( arg0.getWordBookId() != null ) {
            arg1.setWordBookId( Long.parseLong( arg0.getWordBookId() ) );
        }
        else {
            arg1.setWordBookId( null );
        }
        if ( arg0.getWordBookUnitId() != null ) {
            arg1.setWordBookUnitId( Long.parseLong( arg0.getWordBookUnitId() ) );
        }
        else {
            arg1.setWordBookUnitId( null );
        }
        arg1.setName( arg0.getName() );
        arg1.setPhoneticSymbol( arg0.getPhoneticSymbol() );
        arg1.setVowelSeparation( arg0.getVowelSeparation() );
        arg1.setPath( arg0.getPath() );
        arg1.setParaphrase( arg0.getParaphrase() );

        return arg1;
    }
}
