package org.dromara.love.domain;

import javax.annotation.processing.Generated;
import org.dromara.love.domain.vo.LoveUserTestBookWordsRecordVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-07T01:55:47+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (Microsoft)"
)
@Component
public class LoveUserTestBookWordsRecordToLoveUserTestBookWordsRecordVoMapperImpl implements LoveUserTestBookWordsRecordToLoveUserTestBookWordsRecordVoMapper {

    @Override
    public LoveUserTestBookWordsRecordVo convert(LoveUserTestBookWordsRecord arg0) {
        if ( arg0 == null ) {
            return null;
        }

        LoveUserTestBookWordsRecordVo loveUserTestBookWordsRecordVo = new LoveUserTestBookWordsRecordVo();

        loveUserTestBookWordsRecordVo.setUserId( arg0.getUserId() );
        loveUserTestBookWordsRecordVo.setBookTextBookId( arg0.getBookTextBookId() );
        loveUserTestBookWordsRecordVo.setBookTextBookName( arg0.getBookTextBookName() );
        loveUserTestBookWordsRecordVo.setTestDuration( arg0.getTestDuration() );
        loveUserTestBookWordsRecordVo.setCorrectAnswers( arg0.getCorrectAnswers() );
        loveUserTestBookWordsRecordVo.setAccuracyRate( arg0.getAccuracyRate() );
        loveUserTestBookWordsRecordVo.setEn2cnAccuracy( arg0.getEn2cnAccuracy() );
        loveUserTestBookWordsRecordVo.setCn2enAccuracy( arg0.getCn2enAccuracy() );
        loveUserTestBookWordsRecordVo.setAudio2cnAccuracy( arg0.getAudio2cnAccuracy() );
        loveUserTestBookWordsRecordVo.setSuitableDifficultyLevel( arg0.getSuitableDifficultyLevel() );
        loveUserTestBookWordsRecordVo.setBaseScore( arg0.getBaseScore() );
        loveUserTestBookWordsRecordVo.setDifficultyBonus( arg0.getDifficultyBonus() );
        loveUserTestBookWordsRecordVo.setTimeBonus( arg0.getTimeBonus() );
        loveUserTestBookWordsRecordVo.setCompletionBonus( arg0.getCompletionBonus() );
        loveUserTestBookWordsRecordVo.setTotalScore( arg0.getTotalScore() );
        loveUserTestBookWordsRecordVo.setGradeLevel( arg0.getGradeLevel() );
        loveUserTestBookWordsRecordVo.setGradeDesc( arg0.getGradeDesc() );
        loveUserTestBookWordsRecordVo.setAnswerDetails( arg0.getAnswerDetails() );
        loveUserTestBookWordsRecordVo.setRemarks( arg0.getRemarks() );
        loveUserTestBookWordsRecordVo.setTags( arg0.getTags() );

        return loveUserTestBookWordsRecordVo;
    }

    @Override
    public LoveUserTestBookWordsRecordVo convert(LoveUserTestBookWordsRecord arg0, LoveUserTestBookWordsRecordVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setUserId( arg0.getUserId() );
        arg1.setBookTextBookId( arg0.getBookTextBookId() );
        arg1.setBookTextBookName( arg0.getBookTextBookName() );
        arg1.setTestDuration( arg0.getTestDuration() );
        arg1.setCorrectAnswers( arg0.getCorrectAnswers() );
        arg1.setAccuracyRate( arg0.getAccuracyRate() );
        arg1.setEn2cnAccuracy( arg0.getEn2cnAccuracy() );
        arg1.setCn2enAccuracy( arg0.getCn2enAccuracy() );
        arg1.setAudio2cnAccuracy( arg0.getAudio2cnAccuracy() );
        arg1.setSuitableDifficultyLevel( arg0.getSuitableDifficultyLevel() );
        arg1.setBaseScore( arg0.getBaseScore() );
        arg1.setDifficultyBonus( arg0.getDifficultyBonus() );
        arg1.setTimeBonus( arg0.getTimeBonus() );
        arg1.setCompletionBonus( arg0.getCompletionBonus() );
        arg1.setTotalScore( arg0.getTotalScore() );
        arg1.setGradeLevel( arg0.getGradeLevel() );
        arg1.setGradeDesc( arg0.getGradeDesc() );
        arg1.setAnswerDetails( arg0.getAnswerDetails() );
        arg1.setRemarks( arg0.getRemarks() );
        arg1.setTags( arg0.getTags() );

        return arg1;
    }
}
