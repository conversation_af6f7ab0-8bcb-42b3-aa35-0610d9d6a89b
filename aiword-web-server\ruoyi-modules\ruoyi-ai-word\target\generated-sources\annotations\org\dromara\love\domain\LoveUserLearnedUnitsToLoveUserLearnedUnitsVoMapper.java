package org.dromara.love.domain;

import io.github.linpeilie.AutoMapperConfig__787;
import io.github.linpeilie.BaseMapper;
import org.dromara.love.domain.bo.LoveUserLearnedUnitsBoToLoveUserLearnedUnitsMapper;
import org.dromara.love.domain.vo.LoveUserLearnedUnitsVo;
import org.dromara.love.domain.vo.LoveUserLearnedUnitsVoToLoveUserLearnedUnitsMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__787.class,
    uses = {LoveUserLearnedUnitsBoToLoveUserLearnedUnitsMapper.class,LoveUserLearnedUnitsVoToLoveUserLearnedUnitsMapper.class},
    imports = {}
)
public interface LoveUserLearnedUnitsToLoveUserLearnedUnitsVoMapper extends BaseMapper<LoveUserLearnedUnits, LoveUserLearnedUnitsVo> {
}
