package org.dromara.love.domain;

import javax.annotation.processing.Generated;
import org.dromara.love.domain.vo.LoveWordAiDetailsVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-07T01:55:47+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (Microsoft)"
)
@Component
public class LoveWordAiDetailsToLoveWordAiDetailsVoMapperImpl implements LoveWordAiDetailsToLoveWordAiDetailsVoMapper {

    @Override
    public LoveWordAiDetailsVo convert(LoveWordAiDetails arg0) {
        if ( arg0 == null ) {
            return null;
        }

        LoveWordAiDetailsVo loveWordAiDetailsVo = new LoveWordAiDetailsVo();

        loveWordAiDetailsVo.setId( arg0.getId() );
        loveWordAiDetailsVo.setWordId( arg0.getWordId() );
        loveWordAiDetailsVo.setAiDetails( arg0.getAiDetails() );

        return loveWordAiDetailsVo;
    }

    @Override
    public LoveWordAiDetailsVo convert(LoveWordAiDetails arg0, LoveWordAiDetailsVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setWordId( arg0.getWordId() );
        arg1.setAiDetails( arg0.getAiDetails() );

        return arg1;
    }
}
