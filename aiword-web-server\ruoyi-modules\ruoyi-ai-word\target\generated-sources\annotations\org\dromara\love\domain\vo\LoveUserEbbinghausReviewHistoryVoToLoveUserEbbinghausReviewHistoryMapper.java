package org.dromara.love.domain.vo;

import io.github.linpeilie.AutoMapperConfig__787;
import io.github.linpeilie.BaseMapper;
import org.dromara.love.domain.LoveUserEbbinghausReviewHistory;
import org.dromara.love.domain.LoveUserEbbinghausReviewHistoryToLoveUserEbbinghausReviewHistoryVoMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__787.class,
    uses = {LoveUserEbbinghausReviewHistoryToLoveUserEbbinghausReviewHistoryVoMapper.class},
    imports = {}
)
public interface LoveUserEbbinghausReviewHistoryVoToLoveUserEbbinghausReviewHistoryMapper extends BaseMapper<LoveUserEbbinghausReviewHistoryVo, LoveUserEbbinghausReviewHistory> {
}
