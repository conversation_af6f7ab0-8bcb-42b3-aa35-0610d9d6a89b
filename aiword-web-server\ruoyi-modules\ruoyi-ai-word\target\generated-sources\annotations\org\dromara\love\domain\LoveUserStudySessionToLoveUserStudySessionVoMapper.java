package org.dromara.love.domain;

import io.github.linpeilie.AutoMapperConfig__787;
import io.github.linpeilie.BaseMapper;
import org.dromara.love.domain.bo.LoveUserStudySessionBoToLoveUserStudySessionMapper;
import org.dromara.love.domain.vo.LoveUserStudySessionVo;
import org.dromara.love.domain.vo.LoveUserStudySessionVoToLoveUserStudySessionMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__787.class,
    uses = {LoveUserStudySessionBoToLoveUserStudySessionMapper.class,LoveUserStudySessionVoToLoveUserStudySessionMapper.class},
    imports = {}
)
public interface LoveUserStudySessionToLoveUserStudySessionVoMapper extends BaseMapper<LoveUserStudySession, LoveUserStudySessionVo> {
}
