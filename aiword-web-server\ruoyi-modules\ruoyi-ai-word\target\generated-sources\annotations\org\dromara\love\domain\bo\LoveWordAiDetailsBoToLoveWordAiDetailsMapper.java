package org.dromara.love.domain.bo;

import io.github.linpeilie.AutoMapperConfig__787;
import io.github.linpeilie.BaseMapper;
import org.dromara.love.domain.LoveWordAiDetails;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__787.class,
    uses = {},
    imports = {}
)
public interface LoveWordAiDetailsBoToLoveWordAiDetailsMapper extends BaseMapper<LoveWordAiDetailsBo, LoveWordAiDetails> {
}
