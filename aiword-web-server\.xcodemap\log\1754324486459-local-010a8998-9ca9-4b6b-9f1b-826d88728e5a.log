[2025-08-05 00:21:26.462] [WARN] [main] - 检测到 JRebel，发现相关类: org.zeroturnaround.javarebel.integration.cxf.CXFPlugin [o.a.x.a.x.X]
[2025-08-05 00:21:26.465] [WARN] [main] - [XCodeMap] 检测到 JRebel，发现相关类: org.zeroturnaround.javarebel.integration.cxf.CXFPlugin
 [o.a.x.a.p]
[2025-08-05 00:21:26.466] [WARN] [main] - [XCodeMap] 开始启动, pid:34664, xcodemap.executionId:010a8998-9ca9-4b6b-9f1b-826d88728e5a, version:1.16.0
 [o.a.x.a.p]
[2025-08-05 00:21:26.472] [WARN] [main] - workDir:D:\MyApplication\love-word\aiword-web-server\.xcodemap agentMain:false [o.a.x.a.p]
[2025-08-05 00:21:26.662] [WARN] [main] - AsmSysChangeData 加载完成，共加载 58 个集合类，67 个系统设置方法，147 个集合变更方法 [o.a.x.a.x.X]
[2025-08-05 00:21:26.669] [WARN] [XCodeMap-Install] - [XCodeMap] Install Agent, version:1.16.0
 [o.a.x.a.p]
[2025-08-05 00:21:26.670] [WARN] [XCodeMap-Install] - Start retransformAllClasses [o.a.x.a.x.X]
[2025-08-05 00:21:26.677] [WARN] [XCodeMap-Install] - ReTransform agentmain metrics other classNum:0 [o.a.x.a.x.X]
[2025-08-05 00:21:26.677] [WARN] [XCodeMap-Install] - [XCodeMap] ReTransform class num:0
 [o.a.x.a.p]
[2025-08-05 00:21:26.678] [WARN] [XCodeMap-Install] - [XCodeMap] All 0 batches completed, total time: 0ms
 [o.a.x.a.p]
[2025-08-05 00:21:26.678] [WARN] [XCodeMap-Install] - End retransformAllClasses, cost 8 ms [o.a.x.a.x.X]
[2025-08-05 00:21:26.678] [WARN] [XCodeMap-Install] - [XCodeMap] Install Agent finished, cost 9 ms, version:1.16.0
 [o.a.x.a.p]
[2025-08-05 00:21:26.775] [WARN] [main] - [XCodeMap] 启动完成, cost 364 ms, version:1.16.0
 [o.a.x.a.p]
[2025-08-05 00:21:26.900] [WARN] [XCodeMap-Trans-4] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.B [o.a.x.a.v]
[2025-08-05 00:21:26.901] [WARN] [XCodeMap-Trans-4] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.C [o.a.x.a.v]
[2025-08-05 00:21:26.902] [WARN] [XCodeMap-Trans-4] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.A [o.a.x.a.v]
[2025-08-05 00:21:26.903] [WARN] [XCodeMap-Trans-4] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.q [o.a.x.a.v]
[2025-08-05 00:21:26.903] [WARN] [XCodeMap-Trans-4] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.y [o.a.x.a.v]
[2025-08-05 00:21:26.910] [WARN] [XCodeMap-Trans-4] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.V [o.a.x.a.v]
[2025-08-05 00:21:26.912] [WARN] [XCodeMap-Trans-4] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.P [o.a.x.a.v]
[2025-08-05 00:21:26.914] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:11, classes:1 [o.a.x.a.x.X]
[2025-08-05 00:21:26.923] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:8, classes:1 [o.a.x.a.x.X]
[2025-08-05 00:21:26.932] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:8, classes:1 [o.a.x.a.x.X]
[2025-08-05 00:21:26.939] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:7, classes:1 [o.a.x.a.x.X]
[2025-08-05 00:21:26.948] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:9, classes:1 [o.a.x.a.x.X]
[2025-08-05 00:21:26.956] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:8, classes:1 [o.a.x.a.x.X]
[2025-08-05 00:21:27.241] [WARN] [XCodeMap-Trans-3] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.org.a.a.o [o.a.x.a.v]
[2025-08-05 00:21:27.242] [WARN] [XCodeMap-Trans-3] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.org.a.a.n [o.a.x.a.v]
[2025-08-05 00:21:27.255] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:11, classes:1 [o.a.x.a.x.X]
[2025-08-05 00:21:27.296] [WARN] [XCodeMap-Trans-9] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.Y [o.a.x.a.v]
[2025-08-05 00:21:27.562] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: jakarta.servlet.Servlet:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> jakarta.servlet.Servlet:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef [o.a.x.a.x.X]
[2025-08-05 00:21:27.563] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: jakarta.servlet.Servlet:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> jakarta.servlet.Servlet:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef [o.a.x.a.x.X]
[2025-08-05 00:21:27.563] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: org.springframework.web.servlet.FrameworkServlet:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> jakarta.servlet.Servlet:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef [o.a.x.a.x.X]
[2025-08-05 00:21:27.565] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: jakarta.servlet.GenericServlet:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> jakarta.servlet.Servlet:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef [o.a.x.a.x.X]
[2025-08-05 00:21:27.565] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: jakarta.servlet.http.HttpServlet:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> jakarta.servlet.Servlet:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef [o.a.x.a.x.X]
[2025-08-05 00:21:27.565] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: org.springframework.web.servlet.DispatcherServlet:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> jakarta.servlet.Servlet:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef [o.a.x.a.x.X]
[2025-08-05 00:21:27.565] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: org.springframework.web.servlet.HttpServletBean:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> jakarta.servlet.Servlet:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef [o.a.x.a.x.X]
[2025-08-05 00:21:28.631] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.h [o.a.x.a.v]
[2025-08-05 00:21:28.632] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.m [o.a.x.a.v]
[2025-08-05 00:21:28.644] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.m$a [o.a.x.a.v]
[2025-08-05 00:21:28.644] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.l [o.a.x.a.v]
[2025-08-05 00:21:28.645] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.k [o.a.x.a.v]
[2025-08-05 00:21:28.645] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.n [o.a.x.a.v]
[2025-08-05 00:21:28.645] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:12, classes:1 [o.a.x.a.x.X]
[2025-08-05 00:21:28.645] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.m [o.a.x.a.v]
[2025-08-05 00:21:28.655] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.d [o.a.x.a.v]
[2025-08-05 00:21:28.655] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.i [o.a.x.a.v]
[2025-08-05 00:21:28.656] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.h [o.a.x.a.v]
[2025-08-05 00:21:28.657] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.e [o.a.x.a.v]
[2025-08-05 00:21:28.658] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.i [o.a.x.a.v]
[2025-08-05 00:21:28.660] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.b [o.a.x.a.v]
[2025-08-05 00:21:28.660] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:15, classes:1 [o.a.x.a.x.X]
[2025-08-05 00:21:28.670] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.q [o.a.x.a.v]
[2025-08-05 00:21:28.670] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.a.v [o.a.x.a.v]
[2025-08-05 00:21:28.671] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.j [o.a.x.a.v]
[2025-08-05 00:21:28.672] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:12, classes:1 [o.a.x.a.x.X]
[2025-08-05 00:21:28.672] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.f [o.a.x.a.v]
[2025-08-05 00:21:28.682] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.r [o.a.x.a.v]
[2025-08-05 00:21:28.683] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.n [o.a.x.a.v]
[2025-08-05 00:21:28.683] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.a.i [o.a.x.a.v]
[2025-08-05 00:21:28.684] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.a.a [o.a.x.a.v]
[2025-08-05 00:21:28.684] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.a.b [o.a.x.a.v]
[2025-08-05 00:21:28.684] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:12, classes:1 [o.a.x.a.x.X]
[2025-08-05 00:21:28.684] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.a.t [o.a.x.a.v]
[2025-08-05 00:21:28.693] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.l [o.a.x.a.v]
[2025-08-05 00:21:28.694] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.h [o.a.x.a.v]
[2025-08-05 00:21:28.695] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.g [o.a.x.a.v]
[2025-08-05 00:21:28.696] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.p [o.a.x.a.v]
[2025-08-05 00:21:28.696] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.a.u [o.a.x.a.v]
[2025-08-05 00:21:28.696] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.a.o [o.a.x.a.v]
[2025-08-05 00:21:28.696] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.a.k [o.a.x.a.v]
[2025-08-05 00:21:28.697] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.a.n [o.a.x.a.v]
[2025-08-05 00:21:28.697] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.a.s [o.a.x.a.v]
[2025-08-05 00:21:28.697] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.a.f [o.a.x.a.v]
[2025-08-05 00:21:28.697] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:13, classes:1 [o.a.x.a.x.X]
[2025-08-05 00:21:28.697] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.a.e [o.a.x.a.v]
[2025-08-05 00:21:28.697] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.a.l [o.a.x.a.v]
[2025-08-05 00:21:28.698] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.a.g [o.a.x.a.v]
[2025-08-05 00:21:28.698] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.a.r [o.a.x.a.v]
[2025-08-05 00:21:28.707] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.j$a [o.a.x.a.v]
[2025-08-05 00:21:28.708] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.a [o.a.x.a.v]
[2025-08-05 00:21:28.709] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.s [o.a.x.a.v]
[2025-08-05 00:21:28.709] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.k [o.a.x.a.v]
[2025-08-05 00:21:28.709] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.c [o.a.x.a.v]
[2025-08-05 00:21:28.711] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.m$d [o.a.x.a.v]
[2025-08-05 00:21:28.711] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.ab [o.a.x.a.v]
[2025-08-05 00:21:28.712] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.b.b [o.a.x.a.v]
[2025-08-05 00:21:28.712] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:15, classes:1 [o.a.x.a.x.X]
[2025-08-05 00:21:28.722] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.b.c [o.a.x.a.v]
[2025-08-05 00:21:28.723] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:11, classes:1 [o.a.x.a.x.X]
[2025-08-05 00:21:28.733] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.b.e [o.a.x.a.v]
[2025-08-05 00:21:28.733] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.b.f [o.a.x.a.v]
[2025-08-05 00:21:28.734] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.b.a [o.a.x.a.v]
[2025-08-05 00:21:28.734] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.b.f$a [o.a.x.a.v]
[2025-08-05 00:21:28.735] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.b.a$c [o.a.x.a.v]
[2025-08-05 00:21:28.736] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:12, classes:1 [o.a.x.a.x.X]
[2025-08-05 00:21:28.736] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.b.a$d [o.a.x.a.v]
[2025-08-05 00:21:28.743] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.b.d [o.a.x.a.v]
[2025-08-05 00:21:28.743] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.b.d$d [o.a.x.a.v]
[2025-08-05 00:21:28.743] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.b.d$e [o.a.x.a.v]
[2025-08-05 00:21:28.745] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.b.d$j [o.a.x.a.v]
[2025-08-05 00:21:28.745] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.b.d$a [o.a.x.a.v]
[2025-08-05 00:21:28.745] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.b.d$h [o.a.x.a.v]
[2025-08-05 00:21:28.745] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.b.d$g [o.a.x.a.v]
[2025-08-05 00:21:28.746] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.ac$h [o.a.x.a.v]
[2025-08-05 00:21:28.746] [WARN] [XCodeMap-Trans-0] - Change loadClass by javassist for classname:org.springframework.context.support.ContextTypeMatchClassLoader [o.a.x.a.i]
[2025-08-05 00:21:28.747] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:11, classes:1 [o.a.x.a.x.X]
[2025-08-05 00:21:28.760] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:13, classes:1 [o.a.x.a.x.X]
[2025-08-05 00:21:28.772] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:12, classes:1 [o.a.x.a.x.X]
[2025-08-05 00:21:28.784] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:12, classes:1 [o.a.x.a.x.X]
[2025-08-05 00:21:28.795] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:11, classes:1 [o.a.x.a.x.X]
[2025-08-05 00:21:28.807] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:11, classes:1 [o.a.x.a.x.X]
[2025-08-05 00:21:28.822] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:15, classes:1 [o.a.x.a.x.X]
[2025-08-05 00:21:28.834] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:12, classes:1 [o.a.x.a.x.X]
[2025-08-05 00:21:28.848] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:14, classes:1 [o.a.x.a.x.X]
[2025-08-05 00:21:28.860] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:12, classes:1 [o.a.x.a.x.X]
[2025-08-05 00:21:28.872] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:12, classes:1 [o.a.x.a.x.X]
[2025-08-05 00:21:28.885] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:12, classes:1 [o.a.x.a.x.X]
[2025-08-05 00:21:28.897] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:12, classes:1 [o.a.x.a.x.X]
[2025-08-05 00:21:28.907] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:10, classes:1 [o.a.x.a.x.X]
[2025-08-05 00:21:28.925] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:18, classes:1 [o.a.x.a.x.X]
[2025-08-05 00:21:28.938] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:12, classes:1 [o.a.x.a.x.X]
[2025-08-05 00:21:28.952] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:14, classes:1 [o.a.x.a.x.X]
[2025-08-05 00:21:28.963] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:11, classes:1 [o.a.x.a.x.X]
[2025-08-05 00:21:28.983] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:20, classes:1 [o.a.x.a.x.X]
[2025-08-05 00:21:28.996] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:13, classes:1 [o.a.x.a.x.X]
[2025-08-05 00:21:29.011] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:15, classes:1 [o.a.x.a.x.X]
[2025-08-05 00:21:29.047] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:36, classes:1 [o.a.x.a.x.X]
[2025-08-05 00:21:29.057] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:10, classes:1 [o.a.x.a.x.X]
[2025-08-05 00:21:29.069] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:12, classes:1 [o.a.x.a.x.X]
[2025-08-05 00:21:29.079] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:10, classes:1 [o.a.x.a.x.X]
[2025-08-05 00:21:29.089] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:10, classes:1 [o.a.x.a.x.X]
[2025-08-05 00:21:29.099] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:10, classes:1 [o.a.x.a.x.X]
[2025-08-05 00:21:29.117] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:18, classes:1 [o.a.x.a.x.X]
[2025-08-05 00:21:29.131] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:13, classes:1 [o.a.x.a.x.X]
[2025-08-05 00:21:29.142] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:11, classes:1 [o.a.x.a.x.X]
[2025-08-05 00:21:29.159] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:16, classes:1 [o.a.x.a.x.X]
[2025-08-05 00:21:29.175] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:16, classes:1 [o.a.x.a.x.X]
[2025-08-05 00:21:29.187] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:12, classes:1 [o.a.x.a.x.X]
[2025-08-05 00:21:29.199] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:12, classes:1 [o.a.x.a.x.X]
[2025-08-05 00:21:29.211] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:12, classes:1 [o.a.x.a.x.X]
[2025-08-05 00:21:29.221] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:10, classes:1 [o.a.x.a.x.X]
[2025-08-05 00:21:29.232] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:11, classes:1 [o.a.x.a.x.X]
[2025-08-05 00:21:29.243] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:11, classes:1 [o.a.x.a.x.X]
[2025-08-05 00:21:29.255] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:12, classes:1 [o.a.x.a.x.X]
[2025-08-05 00:21:29.266] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:11, classes:1 [o.a.x.a.x.X]
[2025-08-05 00:21:29.276] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:10, classes:1 [o.a.x.a.x.X]
[2025-08-05 00:21:29.288] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:12, classes:1 [o.a.x.a.x.X]
[2025-08-05 00:21:29.298] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:10, classes:1 [o.a.x.a.x.X]
[2025-08-05 00:21:31.226] [WARN] [XCodeMap-Trans-4] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.af [o.a.x.a.v]
[2025-08-05 00:21:31.241] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:14, classes:1 [o.a.x.a.x.X]
[2025-08-05 00:21:31.490] [WARN] [XCodeMap-Trans-5] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.t [o.a.x.a.v]
[2025-08-05 00:21:31.506] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:14, classes:1 [o.a.x.a.x.X]
[2025-08-05 00:21:32.098] [WARN] [XCodeMap-Trans-1] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.org.a.a.c$a [o.a.x.a.v]
[2025-08-05 00:21:32.100] [WARN] [XCodeMap-Trans-1] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.org.a.a.l [o.a.x.a.v]
[2025-08-05 00:21:32.118] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:18, classes:1 [o.a.x.a.x.X]
[2025-08-05 00:21:32.135] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:17, classes:1 [o.a.x.a.x.X]
[2025-08-05 00:21:32.384] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.U [o.a.x.a.v]
[2025-08-05 00:21:32.385] [WARN] [XCodeMap-Trans-0] - Method is not accessible:org/dromara/demo/service/impl/ExportExcelServiceImpl.setPData(Lorg/dromara/demo/service/impl/ExportExcelServiceImpl$DemoCityData;)V [o.a.x.a.u]
[2025-08-05 00:21:32.400] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:15, classes:1 [o.a.x.a.x.X]
[2025-08-05 00:21:32.651] [WARN] [XCodeMap-Trans-1] - Method is not accessible:org/dromara/love/handler/DisturbOptionsHandler.ordinal()I [o.a.x.a.u]
[2025-08-05 00:21:32.651] [WARN] [XCodeMap-Trans-1] - Method is not accessible:org/dromara/love/handler/DisturbOptionsHandler.ordinal()I [o.a.x.a.u]
[2025-08-05 00:21:32.651] [WARN] [XCodeMap-Trans-1] - Method is not accessible:org/dromara/love/handler/DisturbOptionsHandler.ordinal()I [o.a.x.a.u]
[2025-08-05 00:21:32.678] [WARN] [XCodeMap-Trans-1] - Trigger loading new class from xcodemap trans thread group, java.nio.file.StandardCopyOption [o.a.x.a.v]
[2025-08-05 00:21:32.693] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:14, classes:1 [o.a.x.a.x.X]
[2025-08-05 00:21:34.065] [WARN] [XCodeMap-Trans-6] - Method is not accessible:org/dromara/love/service/impl/LoveUserTestBookWordsRecordServiceImpl.calculateSuitableDifficultyLevel(Lorg/dromara/love/service/impl/LoveUserTestBookWordsRecordServiceImpl$TestScoreResult;Lorg/dromara/love/service/impl/LoveUserTestBookWordsRecordServiceImpl$TestScoreResult;Lorg/dromara/love/service/impl/LoveUserTestBookWordsRecordServiceImpl$TestScoreResult;)J [o.a.x.a.u]
[2025-08-05 00:21:34.307] [WARN] [XCodeMap-Trans-1] - Method is not accessible:org/dromara/workflow/service/impl/FlwCommonServiceImpl.ordinal()I [o.a.x.a.u]
[2025-08-05 00:21:34.363] [WARN] [XCodeMap-Trans-8] - Method is not accessible:org/dromara/workflow/service/impl/FlwTaskAssigneeServiceImpl.ordinal()I [o.a.x.a.u]
[2025-08-05 00:21:34.364] [WARN] [XCodeMap-Trans-8] - Method is not accessible:org/dromara/workflow/service/impl/FlwTaskAssigneeServiceImpl.ordinal()I [o.a.x.a.u]
[2025-08-05 00:21:36.036] [WARN] [XCodeMap-Trans-3] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.b [o.a.x.a.v]
[2025-08-05 00:21:36.053] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:15, classes:1 [o.a.x.a.x.X]
[2025-08-05 00:21:36.107] [WARN] [XCodeMap-Trans-7] - Method is not accessible:org/dromara/common/log/aspect/LogAspect.ordinal()I [o.a.x.a.u]
[2025-08-05 00:21:36.107] [WARN] [XCodeMap-Trans-7] - Method is not accessible:org/dromara/common/log/aspect/LogAspect.ordinal()I [o.a.x.a.u]
[2025-08-05 00:21:36.107] [WARN] [XCodeMap-Trans-7] - Method is not accessible:org/dromara/common/log/aspect/LogAspect.ordinal()I [o.a.x.a.u]
[2025-08-05 00:21:36.107] [WARN] [XCodeMap-Trans-7] - Method is not accessible:org/dromara/common/log/aspect/LogAspect.ordinal()I [o.a.x.a.u]
[2025-08-05 00:21:37.397] [WARN] [XCodeMap-Trans-6] - Skip clinit method of interface, classname:org/dromara/common/mybatis/core/mapper/BaseMapperPlus method:<clinit> desc:()V [o.a.x.a.u]
[2025-08-05 00:21:38.670] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: java.sql.Connection:jdk.internal.loader.ClassLoaders$PlatformClassLoader@5c5a1b69 -> java.sql.Connection:jdk.internal.loader.ClassLoaders$PlatformClassLoader@5c5a1b69 [o.a.x.a.x.X]
[2025-08-05 00:21:38.673] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: java.sql.Connection:jdk.internal.loader.ClassLoaders$PlatformClassLoader@5c5a1b69 -> java.sql.Connection:jdk.internal.loader.ClassLoaders$PlatformClassLoader@5c5a1b69 [o.a.x.a.x.X]
[2025-08-05 00:21:40.697] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: com.mysql.cj.jdbc.JdbcConnection:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> java.sql.Connection:jdk.internal.loader.ClassLoaders$PlatformClassLoader@5c5a1b69 [o.a.x.a.x.X]
[2025-08-05 00:21:40.697] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: io.undertow.servlet.handlers.DefaultServlet:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> jakarta.servlet.Servlet:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef [o.a.x.a.x.X]
[2025-08-05 00:21:40.697] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: org.zeroturnaround.javarebel.integration.fileservlet.JakartaFileServlet:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> jakarta.servlet.Servlet:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef [o.a.x.a.x.X]
[2025-08-05 00:21:42.726] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: com.mysql.cj.jdbc.ConnectionImpl:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> java.sql.Connection:jdk.internal.loader.ClassLoaders$PlatformClassLoader@5c5a1b69 [o.a.x.a.x.X]
[2025-08-05 00:21:42.726] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: java.sql.Statement:jdk.internal.loader.ClassLoaders$PlatformClassLoader@5c5a1b69 -> java.sql.Statement:jdk.internal.loader.ClassLoaders$PlatformClassLoader@5c5a1b69 [o.a.x.a.x.X]
[2025-08-05 00:21:42.726] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: com.mysql.cj.jdbc.JdbcStatement:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> java.sql.Statement:jdk.internal.loader.ClassLoaders$PlatformClassLoader@5c5a1b69 [o.a.x.a.x.X]
[2025-08-05 00:21:42.730] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: java.sql.Statement:jdk.internal.loader.ClassLoaders$PlatformClassLoader@5c5a1b69 -> java.sql.Statement:jdk.internal.loader.ClassLoaders$PlatformClassLoader@5c5a1b69 [o.a.x.a.x.X]
[2025-08-05 00:21:42.730] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: com.mysql.cj.jdbc.JdbcStatement:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> java.sql.Statement:jdk.internal.loader.ClassLoaders$PlatformClassLoader@5c5a1b69 [o.a.x.a.x.X]
[2025-08-05 00:21:43.747] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: com.mysql.cj.jdbc.JdbcPreparedStatement:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> java.sql.Statement:jdk.internal.loader.ClassLoaders$PlatformClassLoader@5c5a1b69 [o.a.x.a.x.X]
[2025-08-05 00:21:43.747] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: java.sql.PreparedStatement:jdk.internal.loader.ClassLoaders$PlatformClassLoader@5c5a1b69 -> java.sql.Statement:jdk.internal.loader.ClassLoaders$PlatformClassLoader@5c5a1b69 [o.a.x.a.x.X]
[2025-08-05 00:21:43.747] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: com.mysql.cj.jdbc.StatementImpl:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> java.sql.Statement:jdk.internal.loader.ClassLoaders$PlatformClassLoader@5c5a1b69 [o.a.x.a.x.X]
[2025-08-05 00:21:44.547] [WARN] [XCodeMap-Trans-1] - Method is not accessible:com/mysql/cj/jdbc/result/ResultSetImpl.ordinal()I [o.a.x.a.u]
[2025-08-05 00:21:44.549] [WARN] [XCodeMap-Trans-1] - Method is not accessible:com/mysql/cj/jdbc/result/ResultSetImpl.ordinal()I [o.a.x.a.u]
[2025-08-05 00:21:44.755] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: com.mysql.cj.jdbc.ClientPreparedStatement:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> java.sql.Statement:jdk.internal.loader.ClassLoaders$PlatformClassLoader@5c5a1b69 [o.a.x.a.x.X]
[2025-08-05 00:21:44.755] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: com.mysql.cj.jdbc.result.ResultSetInternalMethods:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> java.sql.ResultSet:jdk.internal.loader.ClassLoaders$PlatformClassLoader@5c5a1b69 [o.a.x.a.x.X]
[2025-08-05 00:21:44.755] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: java.sql.CallableStatement:jdk.internal.loader.ClassLoaders$PlatformClassLoader@5c5a1b69 -> java.sql.Statement:jdk.internal.loader.ClassLoaders$PlatformClassLoader@5c5a1b69 [o.a.x.a.x.X]
[2025-08-05 00:21:44.755] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: java.sql.ResultSet:jdk.internal.loader.ClassLoaders$PlatformClassLoader@5c5a1b69 -> java.sql.ResultSet:jdk.internal.loader.ClassLoaders$PlatformClassLoader@5c5a1b69 [o.a.x.a.x.X]
[2025-08-05 00:21:44.755] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: com.mysql.cj.jdbc.ServerPreparedStatement:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> java.sql.Statement:jdk.internal.loader.ClassLoaders$PlatformClassLoader@5c5a1b69 [o.a.x.a.x.X]
[2025-08-05 00:21:44.759] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: com.mysql.cj.jdbc.result.ResultSetInternalMethods:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> java.sql.ResultSet:jdk.internal.loader.ClassLoaders$PlatformClassLoader@5c5a1b69 [o.a.x.a.x.X]
[2025-08-05 00:21:44.759] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: java.sql.ResultSet:jdk.internal.loader.ClassLoaders$PlatformClassLoader@5c5a1b69 -> java.sql.ResultSet:jdk.internal.loader.ClassLoaders$PlatformClassLoader@5c5a1b69 [o.a.x.a.x.X]
[2025-08-05 00:21:44.913] [WARN] [XCodeMap-Trans-1] - Method is not accessible:com/mysql/cj/jdbc/result/UpdatableResultSet.ordinal()I [o.a.x.a.u]
[2025-08-05 00:21:44.918] [WARN] [XCodeMap-Trans-1] - Method is not accessible:com/mysql/cj/jdbc/result/UpdatableResultSet.ordinal()I [o.a.x.a.u]
[2025-08-05 00:21:45.777] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: com.mysql.cj.jdbc.result.ResultSetImpl:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> java.sql.ResultSet:jdk.internal.loader.ClassLoaders$PlatformClassLoader@5c5a1b69 [o.a.x.a.x.X]
[2025-08-05 00:21:45.777] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: com.mysql.cj.jdbc.result.UpdatableResultSet:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> java.sql.ResultSet:jdk.internal.loader.ClassLoaders$PlatformClassLoader@5c5a1b69 [o.a.x.a.x.X]
[2025-08-05 00:22:24.133] [WARN] [XCodeMap-Trans-9] - Error getting class modifier for not found class:org/noear/snack/core/Feature [o.a.x.a.u]
[2025-08-05 00:22:24.134] [WARN] [XCodeMap-Trans-9] - Method is not accessible:org/dromara/warm/plugin/json/JsonConvertSnack.add([Lorg/noear/snack/core/Feature;)Lorg/noear/snack/core/Options; [o.a.x.a.u]
[2025-08-05 00:22:24.134] [WARN] [XCodeMap-Trans-9] - Error getting class modifier for not found class:org/noear/snack/core/Options [o.a.x.a.u]
[2025-08-05 00:22:24.134] [WARN] [XCodeMap-Trans-9] - Method is not accessible:org/dromara/warm/plugin/json/JsonConvertSnack.stringify(Ljava/lang/Object;Lorg/noear/snack/core/Options;)Ljava/lang/String; [o.a.x.a.u]
[2025-08-05 00:22:24.142] [WARN] [XCodeMap-Trans-5] - Skip clinit method of interface, classname:org/dromara/warm/flow/core/condition/ConditionStrategy method:<clinit> desc:()V [o.a.x.a.u]
[2025-08-05 00:22:24.156] [WARN] [XCodeMap-Trans-1] - Skip clinit method of interface, classname:org/dromara/warm/flow/core/variable/VariableStrategy method:<clinit> desc:()V [o.a.x.a.u]
[2025-08-05 00:22:24.162] [WARN] [XCodeMap-Trans-3] - Skip clinit method of interface, classname:org/dromara/warm/flow/core/listener/ListenerStrategy method:<clinit> desc:()V [o.a.x.a.u]
[2025-08-05 00:22:28.267] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: com.zaxxer.hikari.pool.ProxyStatement:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> java.sql.Statement:jdk.internal.loader.ClassLoaders$PlatformClassLoader@5c5a1b69 [o.a.x.a.x.X]
[2025-08-05 00:22:28.267] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: com.p6spy.engine.wrapper.ConnectionWrapper:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> java.sql.Connection:jdk.internal.loader.ClassLoaders$PlatformClassLoader@5c5a1b69 [o.a.x.a.x.X]
[2025-08-05 00:22:28.267] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: com.p6spy.engine.wrapper.StatementWrapper:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> java.sql.Statement:jdk.internal.loader.ClassLoaders$PlatformClassLoader@5c5a1b69 [o.a.x.a.x.X]
[2025-08-05 00:22:28.267] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: com.p6spy.engine.wrapper.ResultSetWrapper:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> java.sql.ResultSet:jdk.internal.loader.ClassLoaders$PlatformClassLoader@5c5a1b69 [o.a.x.a.x.X]
[2025-08-05 00:22:28.267] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: jdk.proxy3.$Proxy287:jdk.internal.loader.ClassLoaders$PlatformClassLoader@5c5a1b69 -> java.sql.Connection:jdk.internal.loader.ClassLoaders$PlatformClassLoader@5c5a1b69 [o.a.x.a.x.X]
[2025-08-05 00:22:28.267] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: com.zaxxer.hikari.pool.ProxyPreparedStatement:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> java.sql.Statement:jdk.internal.loader.ClassLoaders$PlatformClassLoader@5c5a1b69 [o.a.x.a.x.X]
[2025-08-05 00:22:28.268] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: com.zaxxer.hikari.pool.HikariProxyConnection:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> java.sql.Connection:jdk.internal.loader.ClassLoaders$PlatformClassLoader@5c5a1b69 [o.a.x.a.x.X]
[2025-08-05 00:22:28.268] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: com.zaxxer.hikari.pool.HikariProxyResultSet:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> java.sql.ResultSet:jdk.internal.loader.ClassLoaders$PlatformClassLoader@5c5a1b69 [o.a.x.a.x.X]
[2025-08-05 00:22:28.268] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: com.p6spy.engine.wrapper.PreparedStatementWrapper:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> java.sql.Statement:jdk.internal.loader.ClassLoaders$PlatformClassLoader@5c5a1b69 [o.a.x.a.x.X]
[2025-08-05 00:22:28.268] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: com.zaxxer.hikari.pool.HikariProxyPreparedStatement:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> java.sql.Statement:jdk.internal.loader.ClassLoaders$PlatformClassLoader@5c5a1b69 [o.a.x.a.x.X]
[2025-08-05 00:22:28.268] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: com.zaxxer.hikari.pool.ProxyResultSet:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> java.sql.ResultSet:jdk.internal.loader.ClassLoaders$PlatformClassLoader@5c5a1b69 [o.a.x.a.x.X]
[2025-08-05 00:22:28.268] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: com.zaxxer.hikari.pool.ProxyConnection:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> java.sql.Connection:jdk.internal.loader.ClassLoaders$PlatformClassLoader@5c5a1b69 [o.a.x.a.x.X]
[2025-08-05 00:23:00.096] [WARN] [XCodeMap-Trans-0] - Skip clinit method of interface, classname:org/dromara/common/core/constant/SystemConstants method:<clinit> desc:()V [o.a.x.a.u]
[2025-08-05 01:22:44.217] [WARN] [XCodeMap-Trans-7] - Skip clinit method of interface, classname:org/dromara/common/core/constant/Constants method:<clinit> desc:()V [o.a.x.a.u]
[2025-08-05 01:22:49.039] [WARN] [XCodeMap-Trans-4] - Method is not accessible:org/dromara/common/core/utils/DateUtils.ordinal()I [o.a.x.a.u]
[2025-08-05 02:09:45.713] [WARN] [rebel-change-detector-thread] - There is other thread to re-transform the loaded class, org.dromara.system.controller.system.SysUserController$$SpringCGLIB$$0 [o.a.x.a.v]
[2025-08-05 02:09:45.807] [WARN] [rebel-change-detector-thread] - There is other thread to re-transform the loaded class, org.springframework.aop.SpringProxy [o.a.x.a.v]
[2025-08-05 02:09:45.809] [WARN] [rebel-change-detector-thread] - There is other thread to re-transform the loaded class, java.util.function.Predicate [o.a.x.a.v]
[2025-08-05 02:09:45.810] [WARN] [rebel-change-detector-thread] - There is other thread to re-transform the loaded class, org.dromara.common.web.core.BaseController [o.a.x.a.v]
[2025-08-05 02:09:45.812] [WARN] [rebel-change-detector-thread] - There is other thread to re-transform the loaded class, org.springframework.cglib.proxy.Factory [o.a.x.a.v]
[2025-08-05 02:09:45.813] [WARN] [rebel-change-detector-thread] - There is other thread to re-transform the loaded class, org.springframework.aop.framework.Advised [o.a.x.a.v]
[2025-08-05 02:09:45.813] [WARN] [rebel-change-detector-thread] - There is other thread to re-transform the loaded class, org.springframework.aop.TargetClassAware [o.a.x.a.v]
[2025-08-05 02:09:45.814] [WARN] [rebel-change-detector-thread] - There is other thread to re-transform the loaded class, org.dromara.system.controller.system.SysUserController [o.a.x.a.v]
[2025-08-05 02:09:45.915] [WARN] [rebel-change-detector-thread] - This is maybe a redefine class, originalJavaClassName:org.dromara.system.controller.system.SysUserController$$M$_jr_1D8D30F74F7A49A4_1 actualJavaClassName:org.dromara.system.controller.system.SysUserController classBeingRedefined:null [o.a.x.a.v]
[2025-08-05 02:14:04.001] [WARN] [rebel-change-detector-thread] - There is other thread to re-transform the loaded class, java.util.function.Function [o.a.x.a.v]
[2025-08-05 02:14:04.002] [WARN] [rebel-change-detector-thread] - There is other thread to re-transform the loaded class, com.baomidou.mybatisplus.core.toolkit.support.SFunction [o.a.x.a.v]
[2025-08-05 02:14:04.002] [WARN] [rebel-change-detector-thread] - There is other thread to re-transform the loaded class, java.util.function.BinaryOperator [o.a.x.a.v]
[2025-08-05 02:14:04.003] [WARN] [rebel-change-detector-thread] - There is other thread to re-transform the loaded class, org.dromara.system.service.impl.SysUserServiceImpl$$SpringCGLIB$$0 [o.a.x.a.v]
[2025-08-05 02:14:04.079] [WARN] [rebel-change-detector-thread] - There is other thread to re-transform the loaded class, java.util.function.IntFunction [o.a.x.a.v]
[2025-08-05 02:14:04.079] [WARN] [rebel-change-detector-thread] - There is other thread to re-transform the loaded class, org.dromara.system.service.ISysUserService [o.a.x.a.v]
[2025-08-05 02:14:04.081] [WARN] [rebel-change-detector-thread] - There is other thread to re-transform the loaded class, java.util.function.BiFunction [o.a.x.a.v]
[2025-08-05 02:14:04.081] [WARN] [rebel-change-detector-thread] - There is other thread to re-transform the loaded class, org.dromara.common.core.service.UserService [o.a.x.a.v]
[2025-08-05 02:14:04.082] [WARN] [rebel-change-detector-thread] - There is other thread to re-transform the loaded class, java.util.function.Consumer [o.a.x.a.v]
[2025-08-05 02:14:04.083] [WARN] [rebel-change-detector-thread] - There is other thread to re-transform the loaded class, org.dromara.system.service.impl.SysUserServiceImpl [o.a.x.a.v]
[2025-08-05 02:14:04.251] [WARN] [rebel-change-detector-thread] - This is maybe a redefine class, originalJavaClassName:org.dromara.system.service.ISysUserService$$M$_jr_1D8D30F74F7A49A4_1 actualJavaClassName:org.dromara.system.service.ISysUserService classBeingRedefined:null [o.a.x.a.v]
[2025-08-05 02:14:50.339] [WARN] [rebel-change-detector-thread] - This is maybe a redefine class, originalJavaClassName:org.dromara.system.service.impl.SysUserServiceImpl$$M$_jr_1D8D30F74F7A49A4_1 actualJavaClassName:org.dromara.system.service.impl.SysUserServiceImpl classBeingRedefined:null [o.a.x.a.v]
[2025-08-05 02:14:50.581] [WARN] [rebel-change-detector-thread] - This is maybe a redefine class, originalJavaClassName:org.dromara.system.service.impl.SysUserServiceImpl$$SpringCGLIB$$0$$M$_jr_1D8D30F74F7A49A4_1 actualJavaClassName:org.dromara.system.service.impl.SysUserServiceImpl$$SpringCGLIB$$0 classBeingRedefined:null [o.a.x.a.v]
[2025-08-05 02:14:51.204] [WARN] [rebel-change-detector-thread] - This is maybe a redefine class, originalJavaClassName:org.dromara.system.service.impl.SysUserServiceImpl$$M$_jr_1D8D30F74F7A49A4_2 actualJavaClassName:org.dromara.system.service.impl.SysUserServiceImpl classBeingRedefined:null [o.a.x.a.v]
[2025-08-05 02:18:05.459] [WARN] [rebel-change-detector-thread] - This is maybe a redefine class, originalJavaClassName:org.dromara.system.service.impl.SysUserServiceImpl$$SpringCGLIB$$0$$M$_jr_1D8D30F74F7A49A4_2 actualJavaClassName:org.dromara.system.service.impl.SysUserServiceImpl$$SpringCGLIB$$0 classBeingRedefined:null [o.a.x.a.v]
[2025-08-05 02:18:06.282] [WARN] [rebel-change-detector-thread] - This is maybe a redefine class, originalJavaClassName:org.dromara.system.service.impl.SysUserServiceImpl$$M$_jr_1D8D30F74F7A49A4_3 actualJavaClassName:org.dromara.system.service.impl.SysUserServiceImpl classBeingRedefined:null [o.a.x.a.v]
[2025-08-05 02:18:06.294] [WARN] [XCodeMap-Trans-1] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.org.a.a.t [o.a.x.a.v]
[2025-08-05 02:18:06.302] [WARN] [XCodeMap-Trans-1] - Detect too large method, classname:org/dromara/system/service/impl/SysUserServiceImpl method:$deserializeLambda$ desc:(Ljava/lang/invoke/SerializedLambda;)Ljava/lang/Object; [o.a.x.a.u]
[2025-08-05 02:18:06.375] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:68, classes:1 [o.a.x.a.x.X]
[2025-08-05 02:18:35.057] [WARN] [rebel-change-detector-thread] - This is maybe a redefine class, originalJavaClassName:org.dromara.system.service.impl.SysUserServiceImpl$$SpringCGLIB$$0$$M$_jr_1D8D30F74F7A49A4_3 actualJavaClassName:org.dromara.system.service.impl.SysUserServiceImpl$$SpringCGLIB$$0 classBeingRedefined:null [o.a.x.a.v]
[2025-08-05 02:18:35.856] [WARN] [rebel-change-detector-thread] - This is maybe a redefine class, originalJavaClassName:org.dromara.system.service.impl.SysUserServiceImpl$$M$_jr_1D8D30F74F7A49A4_4 actualJavaClassName:org.dromara.system.service.impl.SysUserServiceImpl classBeingRedefined:null [o.a.x.a.v]
[2025-08-05 02:21:19.210] [WARN] [rebel-change-detector-thread] - There is other thread to re-transform the loaded class, org.dromara.common.mybatis.core.mapper.BaseMapperPlus [o.a.x.a.v]
[2025-08-05 02:21:19.213] [WARN] [XCodeMap-Trans-6] - Skip clinit method of interface, classname:org/dromara/common/mybatis/core/mapper/BaseMapperPlus method:<clinit> desc:()V [o.a.x.a.u]
[2025-08-05 02:21:19.240] [WARN] [rebel-change-detector-thread] - There is other thread to re-transform the loaded class, com.baomidou.mybatisplus.core.mapper.BaseMapper [o.a.x.a.v]
[2025-08-05 02:21:19.243] [WARN] [rebel-change-detector-thread] - There is other thread to re-transform the loaded class, org.dromara.system.mapper.SysUserMapper [o.a.x.a.v]
[2025-08-05 02:21:19.246] [WARN] [rebel-change-detector-thread] - There is other thread to re-transform the loaded class, org.springframework.core.DecoratingProxy [o.a.x.a.v]
[2025-08-05 02:21:19.246] [WARN] [rebel-change-detector-thread] - There is other thread to re-transform the loaded class, com.baomidou.mybatisplus.core.mapper.Mapper [o.a.x.a.v]
[2025-08-05 02:21:19.306] [WARN] [rebel-change-detector-thread] - This is maybe a redefine class, originalJavaClassName:org.dromara.system.mapper.SysUserMapper$$M$_jr_1D8D30F74F7A49A4_1 actualJavaClassName:org.dromara.system.mapper.SysUserMapper classBeingRedefined:null [o.a.x.a.v]
[2025-08-05 02:27:40.405] [WARN] [XNIO-1 task-5] - This is maybe a redefine class, originalJavaClassName:org.dromara.system.service.impl.SysUserServiceImpl$$SpringCGLIB$$0$$M$_jr_1D8D30F74F7A49A4_4 actualJavaClassName:org.dromara.system.service.impl.SysUserServiceImpl$$SpringCGLIB$$0 classBeingRedefined:null [o.a.x.a.v]
[2025-08-05 02:27:41.138] [WARN] [XNIO-1 task-5] - This is maybe a redefine class, originalJavaClassName:org.dromara.system.controller.system.SysUserController$$SpringCGLIB$$0$$M$_jr_1D8D30F74F7A49A4_1 actualJavaClassName:org.dromara.system.controller.system.SysUserController$$SpringCGLIB$$0 classBeingRedefined:null [o.a.x.a.v]
[2025-08-05 02:38:54.471] [WARN] [rebel-change-detector-thread] - This is maybe a redefine class, originalJavaClassName:org.dromara.system.controller.system.SysUserController$$M$_jr_1D8D30F74F7A49A4_2 actualJavaClassName:org.dromara.system.controller.system.SysUserController classBeingRedefined:null [o.a.x.a.v]
[2025-08-05 02:38:54.538] [WARN] [rebel-change-detector-thread] - This is maybe a redefine class, originalJavaClassName:org.dromara.system.service.impl.SysUserServiceImpl$$M$_jr_1D8D30F74F7A49A4_5 actualJavaClassName:org.dromara.system.service.impl.SysUserServiceImpl classBeingRedefined:null [o.a.x.a.v]
[2025-08-05 02:39:19.554] [WARN] [XNIO-1 task-5] - This is maybe a redefine class, originalJavaClassName:org.dromara.system.service.impl.SysUserServiceImpl$$SpringCGLIB$$0$$M$_jr_1D8D30F74F7A49A4_5 actualJavaClassName:org.dromara.system.service.impl.SysUserServiceImpl$$SpringCGLIB$$0 classBeingRedefined:null [o.a.x.a.v]
[2025-08-05 02:39:20.362] [WARN] [XNIO-1 task-5] - This is maybe a redefine class, originalJavaClassName:org.dromara.system.controller.system.SysUserController$$SpringCGLIB$$0$$M$_jr_1D8D30F74F7A49A4_2 actualJavaClassName:org.dromara.system.controller.system.SysUserController$$SpringCGLIB$$0 classBeingRedefined:null [o.a.x.a.v]
[2025-08-05 02:42:45.401] [WARN] [Thread-5] - Graceful shutdown..., metrics totalClassNum:0	transClassNum:1299	transLoadedNum:9	objectExtractNum:0	objectTravelNum:0	objectExtractErrorNum:0	diffObjectNum:0	totalFuncNum:0	transFuncNum:21012	errorTransNum:0	instanceNum:0	totalFuncCallNum:0	storedFuncCallNum:0	totalObjectNum:0	conflictedObjectNum:0	totalObjectChangeNum:0	totalArrayChangeNum:0	totalPutFieldNum:0	totalSysSetNum:0	totalArrayCopyNum:0	totalColChangeNum:0	totalAtomicChangeNum:0	totalGetFieldNum:0	storedPutFieldNum:0	storedGetFieldNum:0	invokeMethodPosNum:0	putFieldPosNum:0	maxLocalsValue:0	maxStacksValue:0	stackNodeNum:0	transformTimeNano:0	 [o.a.x.a.x.X]
