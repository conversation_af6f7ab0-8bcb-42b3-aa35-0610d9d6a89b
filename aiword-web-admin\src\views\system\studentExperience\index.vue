<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="学生姓名" prop="userName">
              <el-input v-model="queryParams.userName" placeholder="请输入学生姓名" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="手机号码" prop="phonenumber">
              <el-input v-model="queryParams.phonenumber" placeholder="请输入手机号码" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="hover">
      <template #header>
        <el-row :gutter="10">
          <el-col :span="1.5">
            <el-button v-has-permi="['system:user:add']" type="primary" plain icon="Plus" @click="handleAdd()">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button v-has-permi="['system:user:edit']" type="success" plain :disabled="single" icon="Edit" @click="handleUpdate()">
              修改
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button v-has-permi="['system:user:remove']" type="danger" plain :disabled="multiple" icon="Delete" @click="handleDelete()">
              删除
            </el-button>
          </el-col>
          <right-toolbar v-model:show-search="showSearch" :columns="columns" :search="true" @query-table="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="userList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="50" align="center" />
        <el-table-column v-if="columns[0].visible" key="userId" label="学生编号" align="center" prop="userId" />
        <el-table-column v-if="columns[1].visible" key="userName" label="学生账号" align="center" prop="userName" :show-overflow-tooltip="true" />
        <el-table-column v-if="columns[2].visible" key="nickName" label="名字" align="center" prop="nickName" :show-overflow-tooltip="true" />
        <el-table-column v-if="columns[3].visible" key="deptName" label="所属部门" align="center" prop="deptName" :show-overflow-tooltip="true" />
        <el-table-column v-if="columns[4].visible" key="phonenumber" label="联系人电话" align="center" prop="phonenumber" width="120" />
        <el-table-column v-if="columns[5].visible" label="创建时间" align="center" prop="createTime" width="160">
          <template #default="scope">
            <span>{{ scope.row.createTime }}</span>
          </template>
        </el-table-column>
        <el-table-column v-if="columns[5].visible" key="expireTime" label="到期时间" align="center" prop="expireTime" width="180">
          <template #default="scope">
            <span>{{ scope.row.expireTime }}</span>
          </template>
        </el-table-column>

        <el-table-column label="操作" fixed="right" width="180" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button v-hasPermi="['system:user:edit']" link type="primary" icon="Edit" @click="handleUpdate(scope.row)"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button v-hasPermi="['system:user:remove']" link type="primary" icon="Delete" @click="handleDelete(scope.row)"></el-button>
            </el-tooltip>
            <el-tooltip content="重置密码" placement="top">
              <el-button v-hasPermi="['system:user:resetPwd']" link type="primary" icon="Key" @click="handleResetPwd(scope.row)"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        :total="total"
        @pagination="getList"
      />
    </el-card>

    <!-- 添加或修改学生对话框 -->
    <el-dialog ref="formDialogRef" v-model="dialog.visible" :title="dialog.title" width="800px" append-to-body @close="closeDialog">
      <el-form ref="userFormRef" :model="form" :rules="rules" label-width="120px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="学生名称" prop="nickName">
              <el-input v-model="form.nickName" placeholder="请输入学生名称" maxlength="30" />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 与用户页一致：新增时显示账号与密码；编辑时整行隐藏，避免布局缺块 -->
        <el-row>
          <el-col :span="12">
            <!-- 学生账号：新增时禁用输入，自动生成 TY + 名字 -->
            <el-form-item v-if="!isEdit" label="学生账号" prop="userName">
              <el-input
                v-model="form.userName"
                placeholder="学生账号将自动生成为 TY + 名字"
                maxlength="30"
                :disabled="true"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item v-if="!isEdit" label="登录密码" prop="password">
              <el-input v-model="form.password" placeholder="请输入登录密码" type="password" maxlength="20" show-password />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form-item label="联系人电话" prop="phonenumber">
              <el-input v-model="form.phonenumber" placeholder="请输入联系人电话" maxlength="11" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="邮箱" prop="email">
              <el-input v-model="form.email" placeholder="请输入邮箱" maxlength="50" />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 所属部门，编辑时隐藏 -->
        <el-row v-if="!isEdit">
          <el-col :span="24">
            <el-form-item label="所属部门" prop="deptName">
              <el-input :model-value="form.deptName" disabled placeholder="请输入所属部门" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form-item label="邮箱" prop="email">
              <el-input v-model="form.email" placeholder="请输入邮箱" maxlength="50" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="备注">
              <el-input v-model="form.remark" type="textarea" placeholder="请输入内容"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel()">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Student" lang="ts">
import { ref, reactive, toRefs, onMounted, getCurrentInstance, type ComponentInternalInstance, watch } from 'vue';
import type { ElFormInstance, ElDialogInstance } from 'element-plus';
import api from '@/api/system/user';
import type { UserForm, UserQuery, UserVO } from '@/api/system/user/types';
import type { PageData, DialogOption, FieldOption, DateModelType } from '@/types';
import { ElMessageBox } from 'element-plus';
import { to } from 'await-to-js';
// 读取当前登录用户信息
import { useUserStore } from '@/store/modules/user';
import dayjs from 'dayjs';
const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const userStore = useUserStore();

/**
 * 根据用户管理“新增”接口返回的数据中的角色列表，找到 roleKey = 'user_student' 的角色ID
 * 用于在学生新增时默认选中该角色（不展示角色选择控件，只在提交参数中带上）
 */
function findStudentRoleId(roleList: any[]): string | number | undefined {
  if (!Array.isArray(roleList)) return undefined;
  const target = roleList.find((r: any) => r?.roleKey === 'user_student_experience');
  return target?.roleId;
}

// 列显隐信息
const columns = ref<FieldOption[]>([
  { key: 0, label: '学生编号', visible: false, children: [] },
  { key: 1, label: '学生账号', visible: true, children: [] },
  { key: 2, label: '名字', visible: true, children: [] },
  { key: 3, label: '所属部门', visible: true, children: [] },
  { key: 4, label: '联系人电话', visible: true, children: [] },
  { key: 5, label: '创建时间', visible: true, children: [] },
  { key: 6, label: '到期时间', visible: true, children: [] }
]);

const userList = ref<UserVO[]>([]);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<number | string>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const dateRange = ref<[DateModelType, DateModelType]>(['', '']);

const queryFormRef = ref<ElFormInstance>();
const userFormRef = ref<ElFormInstance>();
const formDialogRef = ref<ElDialogInstance>();
// 是否编辑状态：默认 false
const isEdit = ref(false);

const dialog = reactive<SimpleDialogOption>({
  visible: false,
  title: ''
});

const initFormData: UserForm = {
  userId: undefined,
  deptId: undefined,
  deptName: '',
  userName: '',
  nickName: undefined,
  password: '',
  phonenumber: undefined,
  email: undefined,
  sex: undefined,
  status: '0',
  remark: '',
  postIds: [],
  roleIds: [],
  expireTime: ''
};

const initData: PageData<UserForm, UserQuery> = {
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    userName: '',
    phonenumber: '',
    status: '',
    deptId: '',
    roleId: ''
  },
  rules: {
    userName: [
      { required: true, message: '学生账号不能为空', trigger: 'change' },
      { min: 2, max: 20, message: '账号长度必须介于 2 和 20 之间', trigger: 'change' }
    ],
    nickName: [{ required: true, message: '学生名称不能为空', trigger: ['blur','change'] }],
    password: [
      { required: true, message: '登录密码不能为空', trigger: 'blur' },
      { min: 5, max: 20, message: '密码长度必须介于 5 和 20 之间', trigger: 'blur' },
      { pattern: /^[^<>"'|\\]+$/, message: '不能包含非法字符：< > " \' \\ |', trigger: 'blur' }
    ],
    email: [{ type: 'email', message: '请输入正确的邮箱地址', trigger: ['blur', 'change'] }],
    phonenumber: [
      { required: true, message: '联系人电话不能为空', trigger: 'blur' },
      { pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/, message: '请输入正确的手机号码', trigger: 'blur' }
    ],
    expireTime: [{ required: true, message: '到期时间不能为空', trigger: 'change' }],
    deptName: [{required: true, message: '所属部门不能为空', trigger: 'change' }]
  }
};
const data = reactive<PageData<UserForm, UserQuery>>(initData);
const { queryParams, form, rules } = toRefs<PageData<UserForm, UserQuery>>(data);

/** 查询学生列表（复用用户接口） */
const getList = async () => {
  loading.value = true;
  const res = await api.listStudentExperience(proxy?.addDateRange(queryParams.value, dateRange.value));
  loading.value = false;
  userList.value = res.rows;
  total.value = res.total;
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};
/** 重置按钮操作 */
const resetQuery = () => {
  dateRange.value = ['', ''];
  queryFormRef.value?.resetFields();
  queryParams.value.pageNum = 1;
  handleQuery();
};

/** 删除按钮操作（复用用户接口） */
const handleDelete = async (row?: UserVO) => {
  const userIds = row?.userId || ids.value;
  const [err] = await to(proxy?.$modal.confirm('是否确认删除编号为"' + userIds + '"的数据项？') as any);
  if (!err) {
    await api.delUser(userIds);
    await getList();
    proxy?.$modal.msgSuccess('删除成功');
  }
};

/** 重置密码（复用用户接口） */
const handleResetPwd = async (row: UserVO) => {
  const [err, res] = await to(
    ElMessageBox.prompt('请输入"' + row.userName + '"的新密码', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      closeOnClickModal: false,
      inputPattern: /^.{5,20}$/,
      inputErrorMessage: '用户密码长度必须介于 5 和 20 之间',
      inputValidator: (value: string) => {
        if (/<|>|"|'|\||\\/.test(value)) {
          return '不能包含非法字符：< > " \' \\ |';
        }
      }
    })
  );
  if (!err && res) {
    await api.resetUserPwd(row.userId as any, res.value);
    proxy?.$modal.msgSuccess('修改成功，新密码是：' + res.value);
  }
};

/** 选择条数  */
const handleSelectionChange = (selection: UserVO[]) => {
  ids.value = selection.map((item) => item.userId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 重置操作表单 */
const reset = () => {
  form.value = { ...initFormData };
  userFormRef.value?.resetFields();
};
/** 取消按钮 */
const cancel = () => {
  dialog.visible = false;
  reset();
};

/** 新增按钮操作（复用用户接口） */
const handleAdd = async () => {
  reset();
  isEdit.value = false;

  // 参考用户管理：点击新增先请求 getUser()，其中包含角色列表 roleList/roles
  // 目标：从返回的角色列表中找到 roleKey = 'user_student' 对应的 roleId，写入 form.roleIds
  try {
    const { data } = await api.getUser(); // 与用户管理保持一致
    // 兼容字段名：roles 或 roleList
    const roleList: any[] = (data as any)?.roles || [];
    const roleId = findStudentRoleId(roleList);
    if (roleId !== undefined) {
      // 将默认角色ID写入 roleIds，页面不展示角色控件，仅用于提交
      (form.value as any).roleIds = [roleId];
    }
  } catch (e) {
    // 忽略异常，仍可继续手动填写其余表单
    console.warn('获取角色列表失败，无法自动设置 user_student 默认角色', e);
  }

  // 从后端配置读取默认密码（失败则回退为 '123456'）
  try {
    const res = await getConfigKey('sys.user.initPassword');
    const initPwd = res?.data || '';

    form.value.password = initPwd ? String(initPwd) : '123456';
  } catch (e) {
    form.value.password = '123456';
  }

  // 填充所属部门为当前登录用户的部门（仅新增场景展示）
  form.value.deptId = userStore.deptId as any;
  form.value.deptName = userStore.deptName as any;

  // 新增默认：到期时间 = 当前时间 + 7 天（后端需要的格式：YYYY-MM-DD HH:mm:ss）
  form.value.expireTime = dayjs().add(7, 'day').format('YYYY-MM-DD HH:mm:ss');

  dialog.visible = true;
  dialog.title = '新增学生';
};

/**
 * 根据“学生名称”自动生成账号（仅新增时）
 * 规则：userName = 'TY' + 去首尾空格后的 nickName
 */
watch(
  () => form.value.nickName,
  (newVal) => {
    if (isEdit.value) return; // 编辑模式不自动覆盖
    const name = (newVal ?? '').toString().trim();
    // 为空时 userName 一并清空，避免提交校验不通过时出现无意义提示
    form.value.userName = name ? `TY${name}` : '';
  }
);

/** 修改按钮操作（复用用户接口） */
const handleUpdate = async (row?: UserForm) => {
  reset();
  isEdit.value = !!row;
  const userId = row?.userId || ids.value[0];
  const { data } = await api.getUser(userId);
  dialog.visible = true;
  dialog.title = '修改学生';
  Object.assign(form.value, data.user);
  form.value.password = '';
};

/** 构建提交参数：仅用于添加与编辑，列表不使用 */
function buildParams() {
  let payload = Object.assign({}, form.value)

  // 根据当前 isEdit.value 控制字段
  if (isEdit.value) {
    // 编辑：不提交账号与密码（避免误改账号/密码）
    delete payload.userName;
    delete payload.password;
  }else{
    // 添加
  }

  // 清理空值字段，减少无效传参
  Object.keys(payload).forEach((k) => {
    const v = payload[k];
    if (v === '' || v === undefined || v === null) {
      delete payload[k];
    }
  });

  return payload;
}

/** 提交按钮（复用用户接口） */
const submitForm = () => {
  userFormRef.value?.validate(async (valid: boolean) => {
    if (!valid) return;
    const payload = buildParams();
    isEdit.value ? await api.updateUser(payload as any) : await api.addUser(payload as any);
    proxy?.$modal.msgSuccess('操作成功');
    dialog.visible = false;
    await getList();
  });
};

/** 关闭弹窗时重置表单 */
const closeDialog = () => {
  reset();
  dialog.visible = false;
  userFormRef.value?.resetFields();
  userFormRef.value?.clearValidate();
  isEdit.value = false;
};

const expireShortcuts = [
  {
    text: '三个月',
    value: () => {
      const d = new Date();
      d.setMonth(d.getMonth() + 3);
      return d;
    }
  },
  {
    text: '半年',
    value: () => {
      const d = new Date();
      d.setMonth(d.getMonth() + 6);
      return d;
    }
  },
  {
    text: '一年',
    value: () => {
      const d = new Date();
      d.setFullYear(d.getFullYear() + 1);
      return d;
    }
  }
];

const disabledExpireDate = (time: Date) => {
  const startOfToday = new Date();
  startOfToday.setHours(0, 0, 0, 0);
  return time.getTime() < startOfToday.getTime();
};

onMounted(() => {
  getList(); // 初始化列表数据
});
</script>
