2025-08-04 00:07:21 [Thread-20] INFO  c.a.s.c.c.window.SlidingRingWindow - JVM is about to exit, emitting data in the Window
2025-08-04 00:07:21 [SpringApplicationShutdownHook] INFO  c.a.s.c.c.init.SnailJobCloseListener - snail-job client about to shutdown v1.6.0-beta1
2025-08-04 00:07:21 [SpringApplicationShutdownHook] INFO  c.a.s.c.c.l.report.AbstractLogReport - AsyncReport Log about to shutdown
2025-08-04 00:07:21 [SpringApplicationShutdownHook] INFO  c.a.s.c.c.window.SlidingRingWindow - Sliding window is about to exit, emitting data in the Window
2025-08-04 00:07:21 [SpringApplicationShutdownHook] INFO  c.a.s.c.c.l.report.AbstractLogReport - AsyncReport Log has been shutdown
2025-08-04 00:07:21 [SpringApplicationShutdownHook] INFO  c.a.s.c.c.init.SnailJobCloseListener - snail-job client closed successfully v1.6.0-beta1
2025-08-04 00:07:34 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-08-04 00:07:35 [main] INFO  org.dromara.DromaraApplication - Starting DromaraApplication using Java 17.0.15 with PID 33012 (D:\MyApplication\love-word\aiword-web-server\ruoyi-admin\target\classes started by Administrator in D:\MyApplication\love-word\aiword-web-server)
2025-08-04 00:07:35 [main] INFO  org.dromara.DromaraApplication - The following 1 profile is active: "dev"
2025-08-04 00:07:45 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-08-04 00:07:46 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-08-04 00:07:46 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-08-04 00:07:51 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@2700e662
2025-08-04 00:07:51 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-08-04 00:07:51 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [slave] success
2025-08-04 00:07:51 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-08-04 00:07:51 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
2025-08-04 00:07:56 [main] INFO  o.d.common.redis.config.RedisConfig - 初始化 redis 配置
2025-08-04 00:07:56 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-08-04 00:07:56 [redisson-netty-1-5] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-08-04 00:07:56 [redisson-netty-1-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-08-04 00:08:08 [main] INFO  o.d.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-08-04 00:08:20 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: C:\Users\<USER>\Users\Administrator/snailJob/worker
2025-08-04 00:08:20 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: C:\Users\<USER>\MyApplication\love-word\aiword-web-server\ruoyi-admin\target\classes started by Administrator in D:\MyApplication\love-word\aiword-web-server)
2025-08-04 02:16:20 [main] INFO  org.dromara.DromaraApplication - The following 1 profile is active: "dev"
2025-08-04 02:16:34 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-08-04 02:16:35 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-08-04 02:16:35 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-08-04 02:16:41 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@25ca268d
2025-08-04 02:16:41 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-08-04 02:16:41 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [slave] success
2025-08-04 02:16:41 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-08-04 02:16:41 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
2025-08-04 02:16:48 [main] INFO  o.d.common.redis.config.RedisConfig - 初始化 redis 配置
2025-08-04 02:16:48 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-08-04 02:16:49 [redisson-netty-1-5] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-08-04 02:16:49 [redisson-netty-1-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-08-04 02:17:03 [main] INFO  o.d.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-08-04 02:17:18 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: C:\Users\<USER>\Users\Administrator/snailJob/worker
2025-08-04 02:17:18 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: C:\Users\<USER>\MyApplication\love-word\aiword-web-server\ruoyi-admin\target\classes started by Administrator in D:\MyApplication\love-word\aiword-web-server)
2025-08-04 02:25:45 [main] INFO  org.dromara.DromaraApplication - The following 1 profile is active: "dev"
2025-08-04 02:25:57 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-08-04 02:25:58 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-08-04 02:25:58 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-08-04 02:26:04 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@523898a2
2025-08-04 02:26:04 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-08-04 02:26:04 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [slave] success
2025-08-04 02:26:04 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-08-04 02:26:04 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
2025-08-04 02:26:10 [main] INFO  o.d.common.redis.config.RedisConfig - 初始化 redis 配置
2025-08-04 02:26:10 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-08-04 02:26:11 [redisson-netty-1-5] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-08-04 02:26:11 [redisson-netty-1-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-08-04 02:26:24 [main] INFO  o.d.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-08-04 02:26:38 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: C:\Users\<USER>\Users\Administrator/snailJob/worker
2025-08-04 02:26:38 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: C:\Users\<USER>\MyApplication\love-word\aiword-web-server\ruoyi-admin\target\classes started by Administrator in D:\MyApplication\love-word\aiword-web-server)
2025-08-04 02:58:58 [main] INFO  org.dromara.DromaraApplication - The following 1 profile is active: "dev"
2025-08-04 02:59:10 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-08-04 02:59:11 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-08-04 02:59:11 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-08-04 02:59:17 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@2c854396
2025-08-04 02:59:17 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-08-04 02:59:17 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [slave] success
2025-08-04 02:59:17 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-08-04 02:59:17 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
2025-08-04 02:59:22 [main] INFO  o.d.common.redis.config.RedisConfig - 初始化 redis 配置
2025-08-04 02:59:22 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-08-04 02:59:23 [redisson-netty-1-5] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-08-04 02:59:23 [redisson-netty-1-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-08-04 02:59:37 [main] INFO  o.d.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-08-04 02:59:53 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: C:\Users\<USER>\Users\Administrator/snailJob/worker
2025-08-04 02:59:53 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: C:\Users\<USER>\MyApplication\love-word\aiword-web-server\ruoyi-admin\target\classes started by Administrator in D:\MyApplication\love-word\aiword-web-server)
2025-08-04 21:58:06 [main] INFO  org.dromara.DromaraApplication - The following 1 profile is active: "dev"
2025-08-04 21:58:19 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-08-04 21:58:20 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-08-04 21:58:20 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-08-04 21:58:25 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@654e0e32
2025-08-04 21:58:25 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-08-04 21:58:25 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [slave] success
2025-08-04 21:58:25 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-08-04 21:58:25 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
2025-08-04 21:58:30 [main] INFO  o.d.common.redis.config.RedisConfig - 初始化 redis 配置
2025-08-04 21:58:30 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-08-04 21:58:31 [redisson-netty-1-5] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-08-04 21:58:31 [redisson-netty-1-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-08-04 21:58:43 [main] INFO  o.d.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-08-04 21:58:55 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: C:\Users\<USER>\Users\Administrator/snailJob/worker
2025-08-04 21:58:55 [main] INFO  c.a.s.c.j.c.e.b.AbstractScriptExecutor$SnailFileUtils - [FileUtils] [workspace] use user.home as workspace: C:\Users\<USER>