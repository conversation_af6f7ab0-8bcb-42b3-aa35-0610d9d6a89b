package org.dromara.love.domain;

import javax.annotation.processing.Generated;
import org.dromara.love.domain.vo.LoveBookUnitVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-07T01:55:47+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (Microsoft)"
)
@Component
public class LoveBookUnitToLoveBookUnitVoMapperImpl implements LoveBookUnitToLoveBookUnitVoMapper {

    @Override
    public LoveBookUnitVo convert(LoveBookUnit arg0) {
        if ( arg0 == null ) {
            return null;
        }

        LoveBookUnitVo loveBookUnitVo = new LoveBookUnitVo();

        if ( arg0.getId() != null ) {
            loveBookUnitVo.setId( String.valueOf( arg0.getId() ) );
        }
        loveBookUnitVo.setName( arg0.getName() );
        if ( arg0.getSeq() != null ) {
            loveBookUnitVo.setSeq( arg0.getSeq().longValue() );
        }
        loveBookUnitVo.setStatus( arg0.getStatus() );

        return loveBookUnitVo;
    }

    @Override
    public LoveBookUnitVo convert(LoveBookUnit arg0, LoveBookUnitVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        if ( arg0.getId() != null ) {
            arg1.setId( String.valueOf( arg0.getId() ) );
        }
        else {
            arg1.setId( null );
        }
        arg1.setName( arg0.getName() );
        if ( arg0.getSeq() != null ) {
            arg1.setSeq( arg0.getSeq().longValue() );
        }
        else {
            arg1.setSeq( null );
        }
        arg1.setStatus( arg0.getStatus() );

        return arg1;
    }
}
