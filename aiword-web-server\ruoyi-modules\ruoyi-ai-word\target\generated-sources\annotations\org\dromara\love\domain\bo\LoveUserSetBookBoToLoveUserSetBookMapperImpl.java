package org.dromara.love.domain.bo;

import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.dromara.love.domain.LoveUserSetBook;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-07T01:55:47+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (Microsoft)"
)
@Component
public class LoveUserSetBookBoToLoveUserSetBookMapperImpl implements LoveUserSetBookBoToLoveUserSetBookMapper {

    @Override
    public LoveUserSetBook convert(LoveUserSetBookBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        LoveUserSetBook loveUserSetBook = new LoveUserSetBook();

        loveUserSetBook.setSearchValue( arg0.getSearchValue() );
        loveUserSetBook.setCreateDept( arg0.getCreateDept() );
        loveUserSetBook.setCreateBy( arg0.getCreateBy() );
        loveUserSetBook.setCreateTime( arg0.getCreateTime() );
        loveUserSetBook.setUpdateBy( arg0.getUpdateBy() );
        loveUserSetBook.setUpdateTime( arg0.getUpdateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            loveUserSetBook.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        loveUserSetBook.setId( arg0.getId() );
        loveUserSetBook.setUserId( arg0.getUserId() );
        loveUserSetBook.setBookStudyFieldId( arg0.getBookStudyFieldId() );
        loveUserSetBook.setBookStudyFieldName( arg0.getBookStudyFieldName() );
        loveUserSetBook.setBookTypeId( arg0.getBookTypeId() );
        loveUserSetBook.setBookTypeName( arg0.getBookTypeName() );
        loveUserSetBook.setBookTextBookId( arg0.getBookTextBookId() );
        loveUserSetBook.setBookTextBookName( arg0.getBookTextBookName() );

        return loveUserSetBook;
    }

    @Override
    public LoveUserSetBook convert(LoveUserSetBookBo arg0, LoveUserSetBook arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setId( arg0.getId() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setBookStudyFieldId( arg0.getBookStudyFieldId() );
        arg1.setBookStudyFieldName( arg0.getBookStudyFieldName() );
        arg1.setBookTypeId( arg0.getBookTypeId() );
        arg1.setBookTypeName( arg0.getBookTypeName() );
        arg1.setBookTextBookId( arg0.getBookTextBookId() );
        arg1.setBookTextBookName( arg0.getBookTextBookName() );

        return arg1;
    }
}
