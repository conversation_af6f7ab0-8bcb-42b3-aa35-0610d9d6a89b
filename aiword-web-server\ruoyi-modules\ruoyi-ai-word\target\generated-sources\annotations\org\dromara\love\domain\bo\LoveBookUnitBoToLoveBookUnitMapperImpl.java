package org.dromara.love.domain.bo;

import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.dromara.love.domain.LoveBookUnit;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-07T01:55:47+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (Microsoft)"
)
@Component
public class LoveBookUnitBoToLoveBookUnitMapperImpl implements LoveBookUnitBoToLoveBookUnitMapper {

    @Override
    public LoveBookUnit convert(LoveBookUnitBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        LoveBookUnit loveBookUnit = new LoveBookUnit();

        loveBookUnit.setSearchValue( arg0.getSearchValue() );
        loveBookUnit.setCreateDept( arg0.getCreateDept() );
        loveBookUnit.setCreateBy( arg0.getCreateBy() );
        loveBookUnit.setCreateTime( arg0.getCreateTime() );
        loveBookUnit.setUpdateBy( arg0.getUpdateBy() );
        loveBookUnit.setUpdateTime( arg0.getUpdateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            loveBookUnit.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        loveBookUnit.setId( arg0.getId() );
        loveBookUnit.setOId( arg0.getOId() );
        if ( arg0.getWordBookId() != null ) {
            loveBookUnit.setWordBookId( Long.parseLong( arg0.getWordBookId() ) );
        }
        loveBookUnit.setWordBookOId( arg0.getWordBookOId() );
        loveBookUnit.setName( arg0.getName() );
        if ( arg0.getSeq() != null ) {
            loveBookUnit.setSeq( arg0.getSeq().intValue() );
        }
        loveBookUnit.setStatus( arg0.getStatus() );

        return loveBookUnit;
    }

    @Override
    public LoveBookUnit convert(LoveBookUnitBo arg0, LoveBookUnit arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setId( arg0.getId() );
        arg1.setOId( arg0.getOId() );
        if ( arg0.getWordBookId() != null ) {
            arg1.setWordBookId( Long.parseLong( arg0.getWordBookId() ) );
        }
        else {
            arg1.setWordBookId( null );
        }
        arg1.setWordBookOId( arg0.getWordBookOId() );
        arg1.setName( arg0.getName() );
        if ( arg0.getSeq() != null ) {
            arg1.setSeq( arg0.getSeq().intValue() );
        }
        else {
            arg1.setSeq( null );
        }
        arg1.setStatus( arg0.getStatus() );

        return arg1;
    }
}
