package org.dromara.love.domain.vo;

import io.github.linpeilie.AutoMapperConfig__787;
import io.github.linpeilie.BaseMapper;
import org.dromara.love.domain.LoveBookStudyField;
import org.dromara.love.domain.LoveBookStudyFieldToLoveBookStudyFieldVoMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__787.class,
    uses = {LoveBookStudyFieldToLoveBookStudyFieldVoMapper.class},
    imports = {}
)
public interface LoveBookStudyFieldVoToLoveBookStudyFieldMapper extends BaseMapper<LoveBookStudyFieldVo, LoveBookStudyField> {
}
