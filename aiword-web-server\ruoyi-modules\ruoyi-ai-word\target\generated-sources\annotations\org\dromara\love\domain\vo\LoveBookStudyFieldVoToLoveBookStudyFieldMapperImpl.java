package org.dromara.love.domain.vo;

import javax.annotation.processing.Generated;
import org.dromara.love.domain.LoveBookStudyField;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-07T01:55:47+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (Microsoft)"
)
@Component
public class LoveBookStudyFieldVoToLoveBookStudyFieldMapperImpl implements LoveBookStudyFieldVoToLoveBookStudyFieldMapper {

    @Override
    public LoveBookStudyField convert(LoveBookStudyFieldVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        LoveBookStudyField loveBookStudyField = new LoveBookStudyField();

        loveBookStudyField.setId( arg0.getId() );
        loveBookStudyField.setName( arg0.getName() );
        loveBookStudyField.setCode( arg0.getCode() );
        loveBookStudyField.setStatus( arg0.getStatus() );

        return loveBookStudyField;
    }

    @Override
    public LoveBookStudyField convert(LoveBookStudyFieldVo arg0, LoveBookStudyField arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setName( arg0.getName() );
        arg1.setCode( arg0.getCode() );
        arg1.setStatus( arg0.getStatus() );

        return arg1;
    }
}
