package org.dromara.love.domain.vo;

import io.github.linpeilie.AutoMapperConfig__787;
import io.github.linpeilie.BaseMapper;
import org.dromara.love.domain.LoveUserStudySessionDetail;
import org.dromara.love.domain.LoveUserStudySessionDetailToLoveUserStudySessionDetailVoMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__787.class,
    uses = {LoveUserStudySessionDetailToLoveUserStudySessionDetailVoMapper.class},
    imports = {}
)
public interface LoveUserStudySessionDetailVoToLoveUserStudySessionDetailMapper extends BaseMapper<LoveUserStudySessionDetailVo, LoveUserStudySessionDetail> {
}
