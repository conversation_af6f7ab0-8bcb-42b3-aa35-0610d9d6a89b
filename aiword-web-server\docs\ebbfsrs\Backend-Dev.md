# FSRS + 艾宾浩斯 + 生词本 后端开发文档（序号模式·字段/方法级别）

本文件为“可直接编码”的实现清单，按字段级、方法级颗粒度列出。采用建议包名与类名，实际落地时可映射到现有工程。

## 0. 约定与包结构

0.1 基础约定  
- 模块：ruoyi-modules/ruoyi-system  
- 基础包：org.dromara.system  
- ORM：MyBatis-Plus（可替换为 XML，方法签名保持）

0.2 包结构建议  
- domain.entity.LoveUserLearnedWords  
- mapper.LoveUserLearnedWordsMapper  
- service.fsrs.FsrsAlgorithm  
- service.fsrs.FsrsParams  
- service.fsrs.FsrsState  
- service.fsrs.FsrsResult  
- service.fsrs.FsrsService  
- service.review.ReviewSchedulerService  
- controller.LoveUserLearnedWordsController

## 1. 数据库与实体字段（DDL + 实体属性）

1.1 DDL（love_user_learned_words 表新增字段）
- ALTER TABLE love_user_learned_words ADD COLUMN fsrs_stability DOUBLE NOT NULL DEFAULT 0.0 COMMENT 'FSRS稳定度';
- ALTER TABLE love_user_learned_words ADD COLUMN fsrs_difficulty DOUBLE NOT NULL DEFAULT 0.0 COMMENT 'FSRS难度';
- ALTER TABLE love_user_learned_words ADD COLUMN fsrs_next_review_at DATETIME NULL COMMENT 'FSRS下次复习时间';
- ALTER TABLE love_user_learned_words ADD COLUMN fsrs_last_rating TINYINT NULL COMMENT 'FSRS上次评分q';
- ALTER TABLE love_user_learned_words ADD COLUMN fsrs_target_retention DECIMAL(3,2) NOT NULL DEFAULT 0.90 COMMENT '目标保持率R';
- ALTER TABLE love_user_learned_words ADD COLUMN is_new_word TINYINT NOT NULL DEFAULT 0 COMMENT '是否生词';
- ALTER TABLE love_user_learned_words ADD COLUMN new_word_reason VARCHAR(32) NULL COMMENT '生词原因';
- ALTER TABLE love_user_learned_words ADD COLUMN new_word_mark_time DATETIME NULL COMMENT '生词标记时间';
- CREATE INDEX idx_user_word_fsrs_next ON love_user_learned_words(user_id, fsrs_next_review_at);
- CREATE INDEX idx_user_word_is_new ON love_user_learned_words(user_id, is_new_word);

1.2 实体类 org.dromara.system.domain.entity.LoveUserLearnedWords 新增字段
- private Double fsrsStability;         // 对应 fsrs_stability
- private Double fsrsDifficulty;        // 对应 fsrs_difficulty
- private LocalDateTime fsrsNextReviewAt; // 对应 fsrs_next_review_at
- private Integer fsrsLastRating;       // 对应 fsrs_last_rating
- private BigDecimal fsrsTargetRetention; // 对应 fsrs_target_retention
- private Integer isNewWord;            // 对应 is_new_word（0/1）
- private String newWordReason;         // 对应 new_word_reason
- private LocalDateTime newWordMarkTime; // 对应 new_word_mark_time

1.3 实体字段默认值与映射要点
- fsrsStability、fsrsDifficulty 初始为 0.0 代表“未初始化”
- fsrsTargetRetention 默认 0.90
- isNewWord 默认为 0
- 时间字段使用 LocalDateTime（或 Instant，经统一转换）

## 2. Mapper 层（方法级清单）

2.1 接口 org.dromara.system.mapper.LoveUserLearnedWordsMapper
- LoveUserLearnedWords selectByUserIdAndWordId(@Param("userId") Long userId, @Param("wordId") Long wordId);
- int updateFsrsOnInitialize(@Param("userId") Long userId,
                             @Param("wordId") Long wordId,
                             @Param("difficulty") Double difficulty,
                             @Param("stability") Double stability,
                             @Param("targetRetention") BigDecimal targetRetention,
                             @Param("nextReviewAt") LocalDateTime nextReviewAt,
                             @Param("lastReviewAt") LocalDateTime lastReviewAt);
- int updateFsrsOnReview(@Param("userId") Long userId,
                         @Param("wordId") Long wordId,
                         @Param("difficulty") Double difficulty,
                         @Param("stability") Double stability,
                         @Param("nextReviewAt") LocalDateTime nextReviewAt,
                         @Param("lastRating") Integer lastRating,
                         @Param("lastReviewAt") LocalDateTime lastReviewAt);
- int markNewWord(@Param("userId") Long userId,
                  @Param("wordId") Long wordId,
                  @Param("reason") String reason,
                  @Param("markTime") LocalDateTime markTime);
- int unmarkNewWord(@Param("userId") Long userId, @Param("wordId") Long wordId);

2.2 MyBatis-Plus 实现要点
- 使用 @TableName 与 @TableField 保持字段映射
- 对 update 方法可使用 @Update 注解或 XML 编写，需携带 user_id AND word_id 条件

## 3. FSRS 内核（类与方法签名）

3.1 org.dromara.system.service.fsrs.FsrsParams
- double MIN_DIFFICULTY = 3.0;
- double MAX_DIFFICULTY = 7.0;
- double MIN_STABILITY = 0.05;
- double GROWTH_BASE = 1.5;
- double GROWTH_STEP = 0.15;
- double PENALTY_ON_FAIL = 0.5;
- double INTERVAL_SCALE = 8.0;
- double RETRIEVABILITY_SCALE = 8.0;
- Duration FIRST_INTERVAL = Duration.ofMinutes(30);

3.2 org.dromara.system.service.fsrs.FsrsState
- Long userId;
- Long wordId;
- Double difficulty;
- Double stability;
- Integer lastRating;       // nullable
- Instant lastReviewAt;     // nullable
- Double targetRetention;   // 0.9 默认
- Instant nextReviewAt;     // nullable
- static FsrsState init(Long userId, Long wordId, Instant now)

3.3 org.dromara.system.service.fsrs.FsrsResult
- Double newDifficulty;
- Double newStability;
- Instant nextReviewAt;
- Double retrievabilityNow;   // 0..1
- Double intervalHours;
- Integer lastRating;

3.4 org.dromara.system.service.fsrs.FsrsAlgorithm
- void initialize(FsrsState state, Instant now)
  - state.difficulty=5.0; state.stability=0.2; state.targetRetention=0.9;
  - state.nextReviewAt = now + FIRST_INTERVAL; state.lastReviewAt = now; state.lastRating = null;
- FsrsResult update(FsrsState state, int q, Duration response, Instant now)
  1) if q ≥ 3:
     - difficulty = max(3.0, difficulty - 0.1 - 0.05*(q-3))
     - stability = stability * (GROWTH_BASE + GROWTH_STEP*(q-3))
  2) if q < 3:
     - difficulty = min(7.0, difficulty + 0.3 + 0.1*(3-q))
     - stability = max(MIN_STABILITY, stability * PENALTY_ON_FAIL)
  3) intervalHours = max(1, stability * INTERVAL_SCALE)
  4) nextReviewAt = now + intervalHours
  5) retrievabilityNow = 1.0（或按 now - lastReviewAt 计算 exp(-t/(stability*RETRIEVABILITY_SCALE))）
  6) 返回 FsrsResult（携带新的 difficulty/stability/next）
- double retrievabilityAt(FsrsState state, Duration t)
  - return exp( - t.toHours() / (state.stability * RETRIEVABILITY_SCALE) );

## 4. 领域服务（方法级清单）

4.1 org.dromara.system.service.fsrs.FsrsService
- void initializeOnLearn(Long userId, Long wordId, Instant now)
  1) 读取 love_user_learned_words（for update）
  2) 若 fsrs_stability==0 且 fsrs_difficulty==0 → 构造 FsrsState 并调用 FsrsAlgorithm.initialize
  3) Mapper.updateFsrsOnInitialize 写回 difficulty、stability、targetRetention、fsrs_next_review_at、lastReviewAt
- FsrsResult updateOnReview(Long userId, Long wordId, int q, Duration response, Instant now)
  1) 读取状态（for update）
  2) 若未初始化则 initializeOnLearn
  3) 校验 q ∈ [0..5]
  4) FsrsAlgorithm.update → 得到 FsrsResult
  5) Mapper.updateFsrsOnReview 写回新的 difficulty、stability、next、lastRating=q、lastReviewAt=now
  6) 返回 FsrsResult
- Optional<FsrsState> getState(Long userId, Long wordId)
- double computeRetrievabilityNow(Long userId, Long wordId, Instant now)
  - 若无状态返回 0 或抛出业务空值

4.2 org.dromara.system.service.review.ReviewSchedulerService
- void saveReviewWordEBB(SaveEbbReviewRequest req)
  - 沿用原艾宾浩斯推进逻辑
- SaveFsrsReviewResponse saveReviewWordFSRS(SaveFsrsReviewRequest req)
  - FsrsService.updateOnReview → 组装 nextReviewAt、memoryStrengthScore（round(retrievabilityNow*100)）

## 5. DTO 与 Controller（签名级）

5.1 DTO
- SaveFsrsReviewRequest
  - Long userId; Long bookId; Long unitId; Long wordId;
  - String track;   // "FSRS"
  - Integer quality;
  - Long responseTime;  // ms
  - String sessionId;
- SaveFsrsReviewResponse
  - String nextReviewAt; // ISO
  - Integer memoryStrengthScore;
- SaveEbbReviewRequest
  - Long userId; Long bookId; Long unitId; Long wordId;
  - String track;   // "EBB"
  - Boolean isCorrect;
  - Long responseTime;
  - String sessionId;

5.2 Controller org.dromara.system.controller.LoveUserLearnedWordsController
- @PostMapping("/love/userLearnedWords/saveReviewWord")
  - public R<?> saveReviewWord(@RequestBody Map<String,Object> body)
    · track=EBB → saveReviewWordEBB(req)
    · track=FSRS → saveReviewWordFSRS(req) → 返回 SaveFsrsReviewResponse
- @GetMapping("/love/userLearnedWords/getFsrsReviewWords")
  - public R<List<WordItem>> getFsrsReviewWords(@RequestParam Long unitId)
- @GetMapping("/love/userLearnedWords/memoryScore")
  - public R<MemoryScoreVO> memoryScore(@RequestParam Long wordId)
- @GetMapping("/love/userLearnedWords/memoryCurve")
  - public R<MemoryCurveVO> memoryCurve(@RequestParam Long wordId, @RequestParam Integer days)

5.3 VO
- MemoryScoreVO
  - Double retrievability; Double stability; Integer memoryStrengthScore; String nextReviewAt;
- MemoryCurveVO
  - String nextReviewAt; List<PointVO> points; // PointVO{String date; Double retrievability;}

## 6. 生词本（方法与接口）

6.1 Mapper
- int markNewWord(userId, wordId, reason, markTime)
- int unmarkNewWord(userId, wordId)
- List<LoveUserLearnedWords> selectNewWordsByUnit(@Param("userId") Long userId, @Param("unitId") Long unitId)

6.2 Service
- void markNewWord(Long userId, Long wordId, String reason, Instant now)
- void unmarkNewWord(Long userId, Long wordId)

6.3 Controller
- @PostMapping("/love/newWords/mark")
- @PostMapping("/love/newWords/unmark")
- @GetMapping("/love/newWords/list")

## 7. 幂等、事务与锁（执行要点）

7.1 幂等
- saveReviewWord 携带 sessionId，可选建立 review_log 幂等表：idempotent_key=hash(userId,wordId,sessionId)
- 检测已处理则直接返回成功

7.2 事务
- initializeOnLearn 与 updateOnReview：同一事务内 select ... for update → update

7.3 锁粒度
- userId+wordId 行级锁，避免并发覆盖

## 8. 指标与埋点（字段级）

8.1 埋点事件
- fsrs.fetch, fsrs.word.save, fsrs.session.complete
- ebb.fetch, ebb.word.save
- newword.mark, newword.unmark
- memory.score.view, memory.curve.view

8.2 埋点字段
- userId, bookId, unitId, wordId, sessionId, track, quality, responseTime, nextReviewAt, timestamp

## 9. 单测清单（用例级）

9.1 FsrsAlgorithmTest
- testIncreaseIntervalWhenHighQuality
- testDecreaseStabilityWhenLowQuality
- testClampDifficultyAndStability

9.2 FsrsServiceTest
- testInitializeOnLearnIdempotent
- testUpdateOnReviewConcurrency
- testComputeRetrievability

9.3 ControllerTest
- testSaveReviewWordFsrsRequiredFields
- testMemoryCurvePointsCountAndMonotonicity

## 10. 性能与扩展

10.1 性能
- 到期查询走 idx_user_word_fsrs_next；分页 limit 控制

10.2 扩展
- 将 FsrsParams 提供可配置化（DB/配置中心）
- 在不改接口的前提下替换为标准FSRS参数化实现