package org.dromara.love.domain.vo;

import javax.annotation.processing.Generated;
import org.dromara.love.domain.LoveUserStudySessionDetail;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-07T01:55:47+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (Microsoft)"
)
@Component
public class LoveUserStudySessionDetailVoToLoveUserStudySessionDetailMapperImpl implements LoveUserStudySessionDetailVoToLoveUserStudySessionDetailMapper {

    @Override
    public LoveUserStudySessionDetail convert(LoveUserStudySessionDetailVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        LoveUserStudySessionDetail loveUserStudySessionDetail = new LoveUserStudySessionDetail();

        loveUserStudySessionDetail.setId( arg0.getId() );
        loveUserStudySessionDetail.setSessionId( arg0.getSessionId() );
        loveUserStudySessionDetail.setUserId( arg0.getUserId() );
        loveUserStudySessionDetail.setWordId( arg0.getWordId() );
        if ( arg0.getActionType() != null ) {
            loveUserStudySessionDetail.setActionType( arg0.getActionType().intValue() );
        }
        if ( arg0.getComponentType() != null ) {
            loveUserStudySessionDetail.setComponentType( arg0.getComponentType().intValue() );
        }
        if ( arg0.getIsCorrect() != null ) {
            loveUserStudySessionDetail.setIsCorrect( arg0.getIsCorrect().intValue() );
        }
        loveUserStudySessionDetail.setResponseTime( arg0.getResponseTime() );
        if ( arg0.getStatusBefore() != null ) {
            loveUserStudySessionDetail.setStatusBefore( arg0.getStatusBefore().intValue() );
        }
        if ( arg0.getStatusAfter() != null ) {
            loveUserStudySessionDetail.setStatusAfter( arg0.getStatusAfter().intValue() );
        }
        loveUserStudySessionDetail.setActionTime( arg0.getActionTime() );

        return loveUserStudySessionDetail;
    }

    @Override
    public LoveUserStudySessionDetail convert(LoveUserStudySessionDetailVo arg0, LoveUserStudySessionDetail arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setSessionId( arg0.getSessionId() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setWordId( arg0.getWordId() );
        if ( arg0.getActionType() != null ) {
            arg1.setActionType( arg0.getActionType().intValue() );
        }
        else {
            arg1.setActionType( null );
        }
        if ( arg0.getComponentType() != null ) {
            arg1.setComponentType( arg0.getComponentType().intValue() );
        }
        else {
            arg1.setComponentType( null );
        }
        if ( arg0.getIsCorrect() != null ) {
            arg1.setIsCorrect( arg0.getIsCorrect().intValue() );
        }
        else {
            arg1.setIsCorrect( null );
        }
        arg1.setResponseTime( arg0.getResponseTime() );
        if ( arg0.getStatusBefore() != null ) {
            arg1.setStatusBefore( arg0.getStatusBefore().intValue() );
        }
        else {
            arg1.setStatusBefore( null );
        }
        if ( arg0.getStatusAfter() != null ) {
            arg1.setStatusAfter( arg0.getStatusAfter().intValue() );
        }
        else {
            arg1.setStatusAfter( null );
        }
        arg1.setActionTime( arg0.getActionTime() );

        return arg1;
    }
}
