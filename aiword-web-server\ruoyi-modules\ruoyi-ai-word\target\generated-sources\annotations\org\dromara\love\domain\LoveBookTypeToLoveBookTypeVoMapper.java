package org.dromara.love.domain;

import io.github.linpeilie.AutoMapperConfig__787;
import io.github.linpeilie.BaseMapper;
import org.dromara.love.domain.bo.LoveBookTypeBoToLoveBookTypeMapper;
import org.dromara.love.domain.vo.LoveBookTypeVo;
import org.dromara.love.domain.vo.LoveBookTypeVoToLoveBookTypeMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__787.class,
    uses = {LoveBookTypeVoToLoveBookTypeMapper.class,LoveBookTypeBoToLoveBookTypeMapper.class},
    imports = {}
)
public interface LoveBookTypeToLoveBookTypeVoMapper extends BaseMapper<LoveBookType, LoveBookTypeVo> {
}
