package org.dromara.love.domain;

import io.github.linpeilie.AutoMapperConfig__787;
import io.github.linpeilie.BaseMapper;
import org.dromara.love.domain.bo.LoveBookTextBookBoToLoveBookTextBookMapper;
import org.dromara.love.domain.vo.LoveBookTextBookVo;
import org.dromara.love.domain.vo.LoveBookTextBookVoToLoveBookTextBookMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__787.class,
    uses = {LoveBookTextBookBoToLoveBookTextBookMapper.class,LoveBookTextBookVoToLoveBookTextBookMapper.class},
    imports = {}
)
public interface LoveBookTextBookToLoveBookTextBookVoMapper extends BaseMapper<LoveBookTextBook, LoveBookTextBookVo> {
}
