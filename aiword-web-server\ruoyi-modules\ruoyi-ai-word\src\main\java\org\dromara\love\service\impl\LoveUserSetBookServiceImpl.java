package org.dromara.love.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.dromara.love.domain.vo.UserBookCollectionVo;
import org.springframework.stereotype.Service;
import org.dromara.love.domain.bo.LoveUserSetBookBo;
import org.dromara.love.domain.vo.LoveUserSetBookVo;
import org.dromara.love.domain.LoveUserSetBook;
import org.dromara.love.mapper.LoveUserSetBookMapper;
import org.dromara.love.service.ILoveUserSetBookService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 学习-用户设置书籍Service业务层处理
 *
 * <AUTHOR> Li
 * @date 2025-06-16
 */
@RequiredArgsConstructor
@Service
public class LoveUserSetBookServiceImpl extends ServiceImpl<LoveUserSetBookMapper, LoveUserSetBook> implements ILoveUserSetBookService {

    private final LoveUserSetBookMapper baseMapper;

    /**
     * 查询学习-用户设置书籍
     *
     * @param id 主键
     * @return 学习-用户设置书籍
     */
    @Override
    public LoveUserSetBookVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询学习-用户设置书籍列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 学习-用户设置书籍分页列表
     */
    @Override
    public TableDataInfo<LoveUserSetBookVo> queryPageList(LoveUserSetBookBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<LoveUserSetBook> lqw = buildQueryWrapper(bo);
        Page<LoveUserSetBookVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的学习-用户设置书籍列表
     *
     * @param bo 查询条件
     * @return 学习-用户设置书籍列表
     */
    @Override
    public List<LoveUserSetBookVo> queryList(LoveUserSetBookBo bo) {
        LambdaQueryWrapper<LoveUserSetBook> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<LoveUserSetBook> buildQueryWrapper(LoveUserSetBookBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<LoveUserSetBook> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(LoveUserSetBook::getId);
        lqw.eq(bo.getUserId() != null, LoveUserSetBook::getUserId, bo.getUserId());
        lqw.eq(bo.getBookStudyFieldId() != null, LoveUserSetBook::getBookStudyFieldId, bo.getBookStudyFieldId());
        lqw.like(StringUtils.isNotBlank(bo.getBookStudyFieldName()), LoveUserSetBook::getBookStudyFieldName, bo.getBookStudyFieldName());
        lqw.eq(bo.getBookTypeId() != null, LoveUserSetBook::getBookTypeId, bo.getBookTypeId());
        lqw.like(StringUtils.isNotBlank(bo.getBookTypeName()), LoveUserSetBook::getBookTypeName, bo.getBookTypeName());
        lqw.eq(bo.getBookTextBookId() != null, LoveUserSetBook::getBookTextBookId, bo.getBookTextBookId());
        lqw.like(StringUtils.isNotBlank(bo.getBookTextBookName()), LoveUserSetBook::getBookTextBookName, bo.getBookTextBookName());
        return lqw;
    }

    /**
     * 新增学习-用户设置书籍
     *
     * @param bo 学习-用户设置书籍
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(LoveUserSetBookBo bo) {
        LoveUserSetBook add = MapstructUtils.convert(bo, LoveUserSetBook.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改学习-用户设置书籍
     *
     * @param bo 学习-用户设置书籍
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(LoveUserSetBookBo bo) {
        LoveUserSetBook update = MapstructUtils.convert(bo, LoveUserSetBook.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(LoveUserSetBook entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除学习-用户设置书籍信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
    /**
     * 按用户 ID 获取已设置书籍信息（按record_type分组）
     *
     * @param userId 用户 ID
     * @return {@link UserBookCollectionVo} 按record_type分组的书籍集合
     */
    @Override
    public UserBookCollectionVo getInfoByUserId(Long userId) {
        UserBookCollectionVo result = new UserBookCollectionVo();

        // 获取学习类型的书籍
        LambdaQueryWrapper<LoveUserSetBook> learningWrapper = new LambdaQueryWrapper<LoveUserSetBook>()
            .eq(LoveUserSetBook::getUserId, userId)
            .eq(LoveUserSetBook::getRecordType, "learning")
            .eq(LoveUserSetBook::getDelFlag, 0)
            .orderByDesc(LoveUserSetBook::getIsActive)
            .orderByDesc(LoveUserSetBook::getUpdateTime);

        List<LoveUserSetBook> learningBooks = baseMapper.selectList(learningWrapper);
        result.setLearningBooks(MapstructUtils.convert(learningBooks, LoveUserSetBookVo.class));

        // 获取回顾类型的书籍
        LambdaQueryWrapper<LoveUserSetBook> reviewWrapper = new LambdaQueryWrapper<LoveUserSetBook>()
            .eq(LoveUserSetBook::getUserId, userId)
            .eq(LoveUserSetBook::getRecordType, "review")
            .eq(LoveUserSetBook::getDelFlag, 0)
            .orderByDesc(LoveUserSetBook::getIsActive)
            .orderByDesc(LoveUserSetBook::getUpdateTime);

        List<LoveUserSetBook> reviewBooks = baseMapper.selectList(reviewWrapper);
        result.setReviewBooks(MapstructUtils.convert(reviewBooks, LoveUserSetBookVo.class));

        return result;
    }
}
