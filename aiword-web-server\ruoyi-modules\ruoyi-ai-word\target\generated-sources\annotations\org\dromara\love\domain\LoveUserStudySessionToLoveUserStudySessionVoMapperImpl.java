package org.dromara.love.domain;

import javax.annotation.processing.Generated;
import org.dromara.love.domain.vo.LoveUserStudySessionVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-07T01:55:47+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (Microsoft)"
)
@Component
public class LoveUserStudySessionToLoveUserStudySessionVoMapperImpl implements LoveUserStudySessionToLoveUserStudySessionVoMapper {

    @Override
    public LoveUserStudySessionVo convert(LoveUserStudySession arg0) {
        if ( arg0 == null ) {
            return null;
        }

        LoveUserStudySessionVo loveUserStudySessionVo = new LoveUserStudySessionVo();

        loveUserStudySessionVo.setUserId( arg0.getUserId() );
        loveUserStudySessionVo.setUnitId( arg0.getUnitId() );
        loveUserStudySessionVo.setStartTime( arg0.getStartTime() );
        loveUserStudySessionVo.setEndTime( arg0.getEndTime() );
        if ( arg0.getSessionStatus() != null ) {
            loveUserStudySessionVo.setSessionStatus( arg0.getSessionStatus().longValue() );
        }
        if ( arg0.getTotalWords() != null ) {
            loveUserStudySessionVo.setTotalWords( arg0.getTotalWords().longValue() );
        }
        if ( arg0.getCompletedWords() != null ) {
            loveUserStudySessionVo.setCompletedWords( arg0.getCompletedWords().longValue() );
        }
        if ( arg0.getSessionDuration() != null ) {
            loveUserStudySessionVo.setSessionDuration( arg0.getSessionDuration().longValue() );
        }
        loveUserStudySessionVo.setLastActivityTime( arg0.getLastActivityTime() );
        if ( arg0.getEndType() != null ) {
            loveUserStudySessionVo.setEndType( arg0.getEndType().longValue() );
        }

        return loveUserStudySessionVo;
    }

    @Override
    public LoveUserStudySessionVo convert(LoveUserStudySession arg0, LoveUserStudySessionVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setUserId( arg0.getUserId() );
        arg1.setUnitId( arg0.getUnitId() );
        arg1.setStartTime( arg0.getStartTime() );
        arg1.setEndTime( arg0.getEndTime() );
        if ( arg0.getSessionStatus() != null ) {
            arg1.setSessionStatus( arg0.getSessionStatus().longValue() );
        }
        else {
            arg1.setSessionStatus( null );
        }
        if ( arg0.getTotalWords() != null ) {
            arg1.setTotalWords( arg0.getTotalWords().longValue() );
        }
        else {
            arg1.setTotalWords( null );
        }
        if ( arg0.getCompletedWords() != null ) {
            arg1.setCompletedWords( arg0.getCompletedWords().longValue() );
        }
        else {
            arg1.setCompletedWords( null );
        }
        if ( arg0.getSessionDuration() != null ) {
            arg1.setSessionDuration( arg0.getSessionDuration().longValue() );
        }
        else {
            arg1.setSessionDuration( null );
        }
        arg1.setLastActivityTime( arg0.getLastActivityTime() );
        if ( arg0.getEndType() != null ) {
            arg1.setEndType( arg0.getEndType().longValue() );
        }
        else {
            arg1.setEndType( null );
        }

        return arg1;
    }
}
