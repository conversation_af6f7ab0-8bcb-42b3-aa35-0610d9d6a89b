# 艾宾浩斯兜底 + FSRS强化 + 记忆评分/曲线 + 生词本 — 落地方案（简版）

本文档描述如何在现有体系下，采用“两段式复习 + 个性化强化 + 生词本闭环 + 记忆评分/曲线可视化”的方案，做到改动小、可回退。

## 1. 目标

1. A段 艾宾浩斯兜底：到期必复，保障不丢词。
2. B段 FSRS强化：个性化更频繁地强化难词，少推送易词。
3. 生词本：难词闭环专项训练，可退出规则。
4. 记忆评分/曲线：每个词可视化“记忆强度”和“遗忘曲线R(t)”。

## 2. 数据结构

### 2.1 用户-单词状态表（love_user_learned_words）新增字段

- fsrs_stability double
- fsrs_difficulty double
- fsrs_next_review_at datetime
- fsrs_last_rating tinyint
- fsrs_target_retention decimal(3,2) 默认 0.9
- is_new_word tinyint
- new_word_reason varchar
- new_word_mark_time datetime
- memory_strength_score int（可选，衍生字段；也可前端实时计算）

说明：FSRS字段不覆盖艾宾浩斯的等级/时间字段，两套并存。

## 3. 接口

- GET /love/userLearnedWords/getReviewWords?unitId=xx
  A段艾宾浩斯到期清单

- GET /love/userLearnedWords/getFsrsReviewWords?unitId=xx
  B段 FSRS 到期清单（fsrs_next_review_at ≤ now）

- POST /love/userLearnedWords/saveReviewWord
  扩展 body：
  - track: EBB | FSRS
  - quality: 0..5（FSRS用）
  - responseTime: ms（可用于分析/阈值）

- GET /love/userLearnedWords/memoryScore?wordId=xx
  返回 retrievability、stability、memory_strength_score、forgetting_risk

- GET /love/userLearnedWords/memoryCurve?wordId=xx&days=30
  返回未来30天 R(t) 点序列与 nextReviewAt

- 生词本：
  - GET /love/newWords/list?unitId=xx
  - POST /love/newWords/train/submit

## 4. 两段式流程（数字序号）

### 4.1 学习（Learn）

1. 前端：setCurrentMode('learn') → initPlaylist → startStudySession。
2. 作答：错题“立即重复 + 2~3卡后再插”；易词不额外重复（仅前端队列层策略）。
3. 单词完成：saveLearnedWord（落库学会）。
4. 初始化FSRS（与艾宾浩斯互不覆盖）：
   - 触发：学会后若无FSRS
   - d0=5.0、s0=0.2、targetRetention=0.9、fsrs_next_review_at=now+30m
5. 生词本标记（可选）：学习中若错误多/极慢 → is_new_word=1，reason=learn_difficult。
6. 会话结束：studyComplete → endSession。

### 4.2 复习 A 段（艾宾浩斯兜底）

1. 前端：getReviewWords → initPlaylist → startStudySession。
2. 队列策略（仅前端）：
   - 难词多推送：答错立即重复 + 2~3卡后再插，错误越多重复越多（受配置限制）。
   - 易词少推送：IsKnow认识且后续全对且快 → 本轮不额外重复。
3. 保存：saveReviewWord(track=EBB, isCorrect, responseTime) → 后端推进艾宾浩斯等级与下次时间。
4. 难/易判定与生词本：
   - q<3 或 错≥2 或 总时长>阈值 → is_new_word=1，reason=review_difficult。
   - 易词可仅前端打标签（会话内少推送）。
5. 结束：studyComplete → endSession。

### 4.3 复习 B 段（FSRS 强化）

1. 触发：A段完成后进入“强化训练”。
2. 队列：getFsrsReviewWords + 合并A段难词（可设置合并比例与上限）。
3. 前端：setCurrentMode('review') → initPlaylist → startStudySession。
4. 队列策略：比A段更“凶”的重复（错题立即重复 + 后移再插，必要时加输出型卡 Cn2En/Write）。
5. 词级评分 q 计算（简单可靠）：
   q = clamp(基准(认识=4/不认识=2) - 错误数(每错-1) - 总时长>阈值(-1) - 提示(-1), 0, 5)
6. 提交：saveReviewWord(track=FSRS, quality=q, responseTime) → FsrsService.updateOnReview → 更新 stability/difficulty 与 fsrs_next_review_at。
7. 结束：studyComplete → endSession。

### 4.4 生词本闭环

1. 入库：学习或复习任一轮满足 q<3 或 错≥2 或 总时长>阈值 → is_new_word=1，并记录原因和时间。
2. 列表页：按教材/单元查看。
3. 训练：走与 B 段一致的强化流程，提交 quality → FSRS 更新。
4. 出库：连续两轮 q≥4 或 memory_strength_score≥阈值 → is_new_word=0。

### 4.5 记忆评分与曲线

- 记忆评分：memory_strength_score = round(retrievability × 100)
- 遗忘曲线：memoryCurve(days) 返回未来 R(t) 序列与 nextReviewAt；前端在“词详情/复盘页”绘图显示。

## 5. FSRS 初始化与卡片融合（要点）

- 初始化时机：
  - 学会落库后，若该词无 FSRS 状态 → 初始化 d0=5.0、s0=0.2、R=0.9、next=now+30m。
- 卡片融合原则：
  - 五张卡片是“测量工具”，在“词完成时”聚合为词级质量分 q（0..5），FSRS 用 q 更新稳定度/难度并计算下一次时间。
  - A段只推进艾宾浩斯；B段提交 q 进入 FSRS；生词本训练与 B 段一致。

## 6. A 段也能实现“难词多推送、易词少推送”

- 不改后端艾宾浩斯时间/等级，仅在前端“队列层”做当前会话内的重复策略：
  - 错题立即重复、2~3卡后再插；易词不重复。
  - 阈值与最大重复次数可设置，安全可回退。

## 7. 可行性与回退

- 兼容：A段（艾宾浩斯）控制“到期必复”，B段（FSRS）做个性化强化；两者字段与任务并存互不覆盖。
- 回退：关闭 B 段与生词本功能，即可恢复纯艾宾浩斯流程。
- 指标：逾期待复率、次日回忆率、平均重复次数、会话时长、正确率、学习负担变化。

## 8. 上线节奏（两周）

- 周1：
  - 后端字段与接口骨架（FsrsService、getFsrsReviewWords、saveReviewWord扩展）
  - 前端 A 段“多/少推送”策略
  - B 段入口与 q 提交
- 周2：
  - 记忆评分/曲线可视化
  - 生词本页与训练

## 9. 关键实现落点（前端参考）

- 播放引擎 Store：
  - 队列生成工厂：[`LearningModeFactory.createFactoryInstance`](aiword-web-client/src/store/modules/wordPlaylist/LearningModeFactory.ts:62)
  - 队列与作答：[`useWordPlaylistStore`](aiword-web-client/src/store/modules/wordPlaylist/wordPlaylist.ts:46) → `handleStudyResult` 汇总统计 → `checkAndSaveCompletedWord` 保存
  - A 段/B 段：统一“卡片作答 → 词完成 → 保存”的节点；B 段在保存时多传 `quality`

- 卡片层：
  - 统一走 `wordPlaylistStore.handleStudyResult` 上报，最后一张卡片完成前计算词级 q，保存时在 body 附带 `track=FSRS` 与 `quality`

---

## 10. 细化补充说明（只添加，不改动原文）

本章节在不改变现有文档结构与内容的前提下，补充每个流程的可执行细节、计算公式、参数建议与异常处理要点，便于研发直接落地。

### 10.1 学习流程（Learn）细化

前端细节
- 队列初始化：
  - 来源：当前单元的待学词集合，依据教材顺序或难度/频次排序（保持现有逻辑）。
  - 会话内重复策略：
    - 错题立即重复：当前卡片错误后，将该卡再次压入队列头部或第二位。
    - 后移再插：在后续第2~3个卡片位置再次插入同一单词的下一张卡（避免连续两次出现造成挫败）。
    - 易词不重复：同一单词本轮全部正确且响应快，不再添加额外复现。
- 统计项（前端累加到词级统计）：
  - perCard: wrong(bool)、responseMs(number)、hinted(bool)、type(enum)
  - perWord: totalWrong、totalResponseMs、usedHints（cards中hinted为true的数量）
- 词完成时机：
  - 以最后一张卡片提交成功为“词完成”标志；学习模式不计算q，仅记录学习统计并保存。

后端细节
- saveLearnedWord 行为：
  - 幂等：重复提交相同 sessionId + wordId 仅更新统计不重复计次。
  - 初始化 FSRS：
    - 条件：用户-词无FSRS状态。
    - 值：difficulty=5.0、stability=0.2、targetRetention=0.9、fsrs_next_review_at=now+30m、fsrs_last_rating=null。
  - 建议记录字段：learn_first_time、learn_last_time、learn_attempts（方便后续报表）。

异常处理
- 中断：学习中断不影响FSRS；仅在成功落库学会时才初始化FSRS。
- 设备/网络抖动：允许客户端重试提交，后端按 sessionId 幂等。

### 10.2 A段复习流程（艾宾浩斯）细化

前端细节
- 获取清单：仅请求到期清单；若为空，显示“今日无到期，建议进入B段或生词本训练”引导。
- 队列策略（只在会话层，不写回后端）：
  - 错题处理：立即重复 + 后移插一次；若连续错误≥2，提示用户放慢节奏或增加辅助卡（如 Paraphrase）。
  - 易词处理：本轮不额外重复，减少学习负担。
- 生词本判定（建议阈值）：
  - 满足任一条件：总错误≥2；总时长>30s；IsKnow=不认识。
  - 动作：调用生词本标记接口或在保存复习时携带标志，后端落库 is_new_word=1，reason=review_difficult。

后端细节
- saveReviewWord(track=EBB)：
  - 推进艾宾浩斯等级与下一次时间（按现有规则）。
  - 记录 responseTime 与错误统计（用于报表与难词判定）。
  - 不更新FSRS（保持两条线互不干扰）。

异常处理
- 超大到期量：前端分页加载或分批进入会话；不影响后端口径。
- 重复提交：按 sessionId 幂等。

### 10.3 B段复习流程（FSRS）细化

前端细节
- 清单构成：
  - 核心：fsrs_next_review_at ≤ now 的词。
  - 可合并：A段内标记的难词，按优先级排序（难词优先、到期早优先），并去重。
- 队列策略（比A段更“凶”）：
  - 错题：立即重复 + 后移再插；必要时优先排输出型卡（Cn2En、Write）加强输出练习。
  - 限额：控制本轮最大题量，避免过载。
- q计算（明确步骤）：
  1. 基准 base = 4（IsKnow=认识）或 2（不认识）。
  2. 权重（默认）：IsKnow 0.2、Paraphrase 0.2、En2Cn 0.25、Cn2En 0.25、Audio2Cn 0.1（Write 可共享0.25或单独设置）。
  3. 扣分：错误/过慢/提示 各 -1×权重；Cn2En/Write 错误额外 -0.5×权重。
  4. q = clamp(round(base - 累计扣分), 0, 5)。
  5. responseTime = sum(card.responseMs)。
- 提交时机：该词最后一张卡片提交前完成聚合，并POST保存。

后端细节
- saveReviewWord(track=FSRS)：
  - 读取 FSRS 状态（若无则先初始化）。
  - 调 FsrsService.updateOnReview(q, responseTime, now)：
    - 更新 difficulty/stability。
    - 计算 intervalHours 与 fsrs_next_review_at。
    - 回写 fsrs_last_rating = q。
  - 返回 nextReviewAt 与可选 memory_strength_score。

异常处理
- q缺失：后端拒绝FSRS更新，仅记录日志；前端保留该词至后续会话。
- 空清单：返回空数组，前端提示“无到期词，建议学习或训练生词本”。

### 10.4 生词本流程细化

前端细节
- 标记：当满足难词判定阈值时，调用接口设置 is_new_word=1，并写 new_word_reason、new_word_mark_time。
- 列表：支持按教材/单元筛选，排序优先最近标记与低记忆强度。
- 训练：走B段相同路径，提交 q → FSRS 更新；显示“已连续达标轮次”。
- 出库规则：
  - 连续两轮 q ≥ 4，或 memory_strength_score ≥ 80（示例阈值），则 is_new_word=0。

后端细节
- newWords.list：where is_new_word=1；支持分页与筛选。
- 训练提交：与 B 段相同，统一走 FSRS 更新。

异常处理
- 重复标记：幂等（已为1则直接成功返回）。
- 并发：以 user_id, word_id 唯一约束，保证状态一致。

### 10.5 记忆评分与曲线细化

计算细节
- memoryScore：
  - retrievability R(now) = exp( - 0 / (stability * 8) ) = 1.0（若按时刻计算可视为接近1），也可按 lastReviewAt 到当前的小时差计算 R(deltaHours)。
  - 如果需要更贴近实际：tHours = now - lastReviewAt（小时），R = exp( - tHours / (stability*8) )。
  - memory_strength_score = round(R * 100)。
- memoryCurve(days)：
  - 对 tDays = 0..days 采样，tHours = tDays*24，按上式计算 R(tHours)。
  - 返回 points 与 nextReviewAt；前端折线图展示，并在 nextReviewAt 位置加标记。

异常处理
- 无FSRS：返回空值，前端展示占位“未学习或未初始化FSRS”。

### 10.6 任务调度与到期生成细化

- A段到期：沿用原有艾宾浩斯计算，不变更。
- B段到期：where fsrs_next_review_at ≤ now。
- 报表与负担：
  - 统计“今日A段到期量”“今日B段到期量”“预计总题量”“平均预计时长”（可用最近7天的平均每题耗时估算）。
  - 不对条目时间做任何自动改动，保证可回退。

### 10.7 数据与索引细化

- 字段默认值：
  - fsrs_stability 默认 0（未初始化时），初始化后≥0.05。
  - fsrs_difficulty 默认 0（未初始化时），初始化后位于[3,7]。
  - fsrs_target_retention 默认 0.90。
- 索引建议：
  - idx_user_word_fsrs_next(user_id, fsrs_next_review_at)：支撑到期筛选。
  - idx_user_word_is_new(user_id, is_new_word)：支撑生词本列表。
- 幂等键：
  - 建议在保存接口中携带 sessionId，结合 userId, wordId 做一次性写入保护。

### 10.8 FSRS内核参数与保护细化

- 难度区间：difficulty ∈ [3.0, 7.0]，每次更新后 clamp。
- 稳定度下限：stability ≥ 0.05。
- 间隔下限：intervalHours ≥ 1。
- 常量参数（FsrsParams）初始建议：
  - growthBase=1.5、growthStep=0.15、penaltyOnFail=0.5、intervalScale=8.0、retrievabilityScale=8.0。
- 标准化演进：
  - 当前为可上线版本，后续用真实数据拟合参数并替换为标准化FSRS公式，保持接口与数据结构不变。

### 10.9 前端实现钩子进一步说明

- 计算 q 的聚合入口：
  - 建议统一由播放引擎 Store 在“词完成前”调用一个 qCalculator 工具函数，输入为本轮五卡片统计，输出 q 与 sumResponseMs。
  - 统一在保存前写入 body：track=FSRS、quality=q、responseTime=sumMs。
- 统计埋点：
  - 学习：learn.*；A段：ebb.*；B段：fsrs.*；生词本：newword.*；评分/曲线：memory.*。
  - 埋点至少包含 userId、bookId、unitId、wordId、sessionId、timestamp。

### 10.10 联调验收用例清单（补充）

- 学习→初始化FSRS：学会一个词后检查 fsrs_* 字段被正确写入，fsrs_next_review_at≈30min 后。
- A段→不写FSRS：完成A段复习，检查FSRS字段未变化。
- B段→提交q更新：同一词多轮 B 段，q高时 interval 逐步拉长、q低时 interval 缩短。
- 生词本→出入库：触发入库后经两轮达标自动出库。
- 评分/曲线：不同 stability 下的 R(t) 曲线单调下降，nextReviewAt 标记正确。
- 幂等与重试：相同 sessionId 重复提交不重复记次；网络重试不造成脏写。

以上细化为在不修改原文结构的前提下新增的落地细节，可按小组分工直接实施与联调。
以上方案以“艾宾浩斯兜底 + FSRS 强化 + 生词本闭环 + 记忆评分/曲线”为主线，改动集中且可控，支持回退，适合快速落地并迭代优化。