package org.dromara.love.domain;

import javax.annotation.processing.Generated;
import org.dromara.love.domain.vo.LoveUserLearnedUnitsVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-07T01:55:47+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (Microsoft)"
)
@Component
public class LoveUserLearnedUnitsToLoveUserLearnedUnitsVoMapperImpl implements LoveUserLearnedUnitsToLoveUserLearnedUnitsVoMapper {

    @Override
    public LoveUserLearnedUnitsVo convert(LoveUserLearnedUnits arg0) {
        if ( arg0 == null ) {
            return null;
        }

        LoveUserLearnedUnitsVo loveUserLearnedUnitsVo = new LoveUserLearnedUnitsVo();

        loveUserLearnedUnitsVo.setId( arg0.getId() );
        loveUserLearnedUnitsVo.setUserId( arg0.getUserId() );
        loveUserLearnedUnitsVo.setBookId( arg0.getBookId() );
        loveUserLearnedUnitsVo.setUnitId( arg0.getUnitId() );
        loveUserLearnedUnitsVo.setUnitName( arg0.getUnitName() );
        loveUserLearnedUnitsVo.setUnitNumber( arg0.getUnitNumber() );
        loveUserLearnedUnitsVo.setTotalWords( arg0.getTotalWords() );
        loveUserLearnedUnitsVo.setLearnedWords( arg0.getLearnedWords() );
        loveUserLearnedUnitsVo.setProgressPercent( arg0.getProgressPercent() );
        loveUserLearnedUnitsVo.setStatus( arg0.getStatus() );
        loveUserLearnedUnitsVo.setFirstLearnTime( arg0.getFirstLearnTime() );
        loveUserLearnedUnitsVo.setLastLearnTime( arg0.getLastLearnTime() );
        loveUserLearnedUnitsVo.setLearnDuration( arg0.getLearnDuration() );
        loveUserLearnedUnitsVo.setReviewCount( arg0.getReviewCount() );

        return loveUserLearnedUnitsVo;
    }

    @Override
    public LoveUserLearnedUnitsVo convert(LoveUserLearnedUnits arg0, LoveUserLearnedUnitsVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setBookId( arg0.getBookId() );
        arg1.setUnitId( arg0.getUnitId() );
        arg1.setUnitName( arg0.getUnitName() );
        arg1.setUnitNumber( arg0.getUnitNumber() );
        arg1.setTotalWords( arg0.getTotalWords() );
        arg1.setLearnedWords( arg0.getLearnedWords() );
        arg1.setProgressPercent( arg0.getProgressPercent() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setFirstLearnTime( arg0.getFirstLearnTime() );
        arg1.setLastLearnTime( arg0.getLastLearnTime() );
        arg1.setLearnDuration( arg0.getLearnDuration() );
        arg1.setReviewCount( arg0.getReviewCount() );

        return arg1;
    }
}
