package org.dromara.love.domain.vo;

import javax.annotation.processing.Generated;
import org.dromara.love.domain.LoveBookUnit;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-07T01:55:47+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (Microsoft)"
)
@Component
public class LoveBookUnitVoToLoveBookUnitMapperImpl implements LoveBookUnitVoToLoveBookUnitMapper {

    @Override
    public LoveBookUnit convert(LoveBookUnitVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        LoveBookUnit loveBookUnit = new LoveBookUnit();

        if ( arg0.getId() != null ) {
            loveBookUnit.setId( Long.parseLong( arg0.getId() ) );
        }
        loveBookUnit.setName( arg0.getName() );
        if ( arg0.getSeq() != null ) {
            loveBookUnit.setSeq( arg0.getSeq().intValue() );
        }
        loveBookUnit.setStatus( arg0.getStatus() );

        return loveBookUnit;
    }

    @Override
    public LoveBookUnit convert(LoveBookUnitVo arg0, LoveBookUnit arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        if ( arg0.getId() != null ) {
            arg1.setId( Long.parseLong( arg0.getId() ) );
        }
        else {
            arg1.setId( null );
        }
        arg1.setName( arg0.getName() );
        if ( arg0.getSeq() != null ) {
            arg1.setSeq( arg0.getSeq().intValue() );
        }
        else {
            arg1.setSeq( null );
        }
        arg1.setStatus( arg0.getStatus() );

        return arg1;
    }
}
