package org.dromara.love.domain;

import javax.annotation.processing.Generated;
import org.dromara.love.domain.vo.LoveUserSetBookVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-07T01:55:47+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (Microsoft)"
)
@Component
public class LoveUserSetBookToLoveUserSetBookVoMapperImpl implements LoveUserSetBookToLoveUserSetBookVoMapper {

    @Override
    public LoveUserSetBookVo convert(LoveUserSetBook arg0) {
        if ( arg0 == null ) {
            return null;
        }

        LoveUserSetBookVo loveUserSetBookVo = new LoveUserSetBookVo();

        loveUserSetBookVo.setId( arg0.getId() );
        loveUserSetBookVo.setUserId( arg0.getUserId() );
        loveUserSetBookVo.setBookStudyFieldId( arg0.getBookStudyFieldId() );
        loveUserSetBookVo.setBookStudyFieldName( arg0.getBookStudyFieldName() );
        loveUserSetBookVo.setBookTypeId( arg0.getBookTypeId() );
        loveUserSetBookVo.setBookTypeName( arg0.getBookTypeName() );
        loveUserSetBookVo.setBookTextBookId( arg0.getBookTextBookId() );
        loveUserSetBookVo.setBookTextBookName( arg0.getBookTextBookName() );

        return loveUserSetBookVo;
    }

    @Override
    public LoveUserSetBookVo convert(LoveUserSetBook arg0, LoveUserSetBookVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setBookStudyFieldId( arg0.getBookStudyFieldId() );
        arg1.setBookStudyFieldName( arg0.getBookStudyFieldName() );
        arg1.setBookTypeId( arg0.getBookTypeId() );
        arg1.setBookTypeName( arg0.getBookTypeName() );
        arg1.setBookTextBookId( arg0.getBookTextBookId() );
        arg1.setBookTextBookName( arg0.getBookTextBookName() );

        return arg1;
    }
}
