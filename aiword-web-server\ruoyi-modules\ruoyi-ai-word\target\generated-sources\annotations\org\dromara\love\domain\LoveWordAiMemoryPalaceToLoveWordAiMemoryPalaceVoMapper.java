package org.dromara.love.domain;

import io.github.linpeilie.AutoMapperConfig__787;
import io.github.linpeilie.BaseMapper;
import org.dromara.love.domain.bo.LoveWordAiMemoryPalaceBoToLoveWordAiMemoryPalaceMapper;
import org.dromara.love.domain.vo.LoveWordAiMemoryPalaceVo;
import org.dromara.love.domain.vo.LoveWordAiMemoryPalaceVoToLoveWordAiMemoryPalaceMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__787.class,
    uses = {LoveWordAiMemoryPalaceVoToLoveWordAiMemoryPalaceMapper.class,LoveWordAiMemoryPalaceBoToLoveWordAiMemoryPalaceMapper.class},
    imports = {}
)
public interface LoveWordAiMemoryPalaceToLoveWordAiMemoryPalaceVoMapper extends BaseMapper<LoveWordAiMemoryPalace, LoveWordAiMemoryPalaceVo> {
}
