[2025-08-04 21:58:01.377] [WARN] [main] - 检测到 JRebel，发现相关类: org.zeroturnaround.javarebel.integration.cxf.CXFPlugin [o.a.x.a.x.X]
[2025-08-04 21:58:01.380] [WARN] [main] - [XCodeMap] 检测到 JRebel，发现相关类: org.zeroturnaround.javarebel.integration.cxf.CXFPlugin
 [o.a.x.a.p]
[2025-08-04 21:58:01.380] [WARN] [main] - [XCodeMap] 开始启动, pid:26108, xcodemap.executionId:52e2cdf9-bda0-4ab4-932a-e1cd0b2f2eaf, version:1.16.0
 [o.a.x.a.p]
[2025-08-04 21:58:01.381] [WARN] [main] - workDir:D:\MyApplication\love-word\aiword-web-server\.xcodemap agentMain:false [o.a.x.a.p]
[2025-08-04 21:58:01.502] [WARN] [main] - AsmSysChangeData 加载完成，共加载 58 个集合类，67 个系统设置方法，147 个集合变更方法 [o.a.x.a.x.X]
[2025-08-04 21:58:01.509] [WARN] [XCodeMap-Install] - [XCodeMap] Install Agent, version:1.16.0
 [o.a.x.a.p]
[2025-08-04 21:58:01.509] [WARN] [XCodeMap-Install] - Start retransformAllClasses [o.a.x.a.x.X]
[2025-08-04 21:58:01.517] [WARN] [XCodeMap-Install] - ReTransform agentmain metrics other classNum:0 [o.a.x.a.x.X]
[2025-08-04 21:58:01.520] [WARN] [XCodeMap-Install] - [XCodeMap] ReTransform class num:0
 [o.a.x.a.p]
[2025-08-04 21:58:01.521] [WARN] [XCodeMap-Install] - [XCodeMap] All 0 batches completed, total time: 0ms
 [o.a.x.a.p]
[2025-08-04 21:58:01.521] [WARN] [XCodeMap-Install] - End retransformAllClasses, cost 12 ms [o.a.x.a.x.X]
[2025-08-04 21:58:01.521] [WARN] [XCodeMap-Install] - [XCodeMap] Install Agent finished, cost 12 ms, version:1.16.0
 [o.a.x.a.p]
[2025-08-04 21:58:01.616] [WARN] [main] - [XCodeMap] 启动完成, cost 271 ms, version:1.16.0
 [o.a.x.a.p]
[2025-08-04 21:58:01.772] [WARN] [XCodeMap-Trans-5] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.B [o.a.x.a.v]
[2025-08-04 21:58:01.773] [WARN] [XCodeMap-Trans-5] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.C [o.a.x.a.v]
[2025-08-04 21:58:01.774] [WARN] [XCodeMap-Trans-5] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.A [o.a.x.a.v]
[2025-08-04 21:58:01.781] [WARN] [XCodeMap-Trans-5] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.q [o.a.x.a.v]
[2025-08-04 21:58:01.781] [WARN] [XCodeMap-Trans-5] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.y [o.a.x.a.v]
[2025-08-04 21:58:01.782] [WARN] [XCodeMap-Trans-5] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.V [o.a.x.a.v]
[2025-08-04 21:58:01.972] [WARN] [XCodeMap-Trans-3] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.org.a.a.o [o.a.x.a.v]
[2025-08-04 21:58:01.973] [WARN] [XCodeMap-Trans-3] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.org.a.a.n [o.a.x.a.v]
[2025-08-04 21:58:01.977] [WARN] [XCodeMap-Trans-3] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.P [o.a.x.a.v]
[2025-08-04 21:58:02.026] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:252, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:02.057] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:31, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:02.080] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:22, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:02.098] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:18, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:02.112] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:14, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:02.126] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:14, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:02.138] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:12, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:02.153] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:15, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:02.466] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: jakarta.servlet.Servlet:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> jakarta.servlet.Servlet:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef [o.a.x.a.x.X]
[2025-08-04 21:58:02.469] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: jakarta.servlet.Servlet:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> jakarta.servlet.Servlet:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef [o.a.x.a.x.X]
[2025-08-04 21:58:02.887] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.Y [o.a.x.a.v]
[2025-08-04 21:58:02.888] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.h [o.a.x.a.v]
[2025-08-04 21:58:02.888] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.m [o.a.x.a.v]
[2025-08-04 21:58:02.904] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.m$a [o.a.x.a.v]
[2025-08-04 21:58:02.904] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.l [o.a.x.a.v]
[2025-08-04 21:58:02.905] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.k [o.a.x.a.v]
[2025-08-04 21:58:02.905] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.n [o.a.x.a.v]
[2025-08-04 21:58:02.906] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.m [o.a.x.a.v]
[2025-08-04 21:58:02.907] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.d [o.a.x.a.v]
[2025-08-04 21:58:02.907] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.i [o.a.x.a.v]
[2025-08-04 21:58:02.908] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.h [o.a.x.a.v]
[2025-08-04 21:58:02.908] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.e [o.a.x.a.v]
[2025-08-04 21:58:02.908] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:18, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:02.908] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.i [o.a.x.a.v]
[2025-08-04 21:58:02.917] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.b [o.a.x.a.v]
[2025-08-04 21:58:02.918] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.q [o.a.x.a.v]
[2025-08-04 21:58:02.918] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.a.v [o.a.x.a.v]
[2025-08-04 21:58:02.919] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.j [o.a.x.a.v]
[2025-08-04 21:58:02.919] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.f [o.a.x.a.v]
[2025-08-04 21:58:02.920] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.r [o.a.x.a.v]
[2025-08-04 21:58:02.920] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.n [o.a.x.a.v]
[2025-08-04 21:58:02.920] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.a.i [o.a.x.a.v]
[2025-08-04 21:58:02.920] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.a.a [o.a.x.a.v]
[2025-08-04 21:58:02.921] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.a.b [o.a.x.a.v]
[2025-08-04 21:58:02.921] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.a.t [o.a.x.a.v]
[2025-08-04 21:58:02.922] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.l [o.a.x.a.v]
[2025-08-04 21:58:02.923] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.h [o.a.x.a.v]
[2025-08-04 21:58:02.923] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.g [o.a.x.a.v]
[2025-08-04 21:58:02.924] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.p [o.a.x.a.v]
[2025-08-04 21:58:02.924] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.a.u [o.a.x.a.v]
[2025-08-04 21:58:02.924] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.a.o [o.a.x.a.v]
[2025-08-04 21:58:02.924] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.a.k [o.a.x.a.v]
[2025-08-04 21:58:02.925] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.a.n [o.a.x.a.v]
[2025-08-04 21:58:02.925] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.a.s [o.a.x.a.v]
[2025-08-04 21:58:02.925] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.a.f [o.a.x.a.v]
[2025-08-04 21:58:02.925] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.a.e [o.a.x.a.v]
[2025-08-04 21:58:02.925] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.a.l [o.a.x.a.v]
[2025-08-04 21:58:02.926] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.a.g [o.a.x.a.v]
[2025-08-04 21:58:02.926] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.a.r [o.a.x.a.v]
[2025-08-04 21:58:02.927] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.j$a [o.a.x.a.v]
[2025-08-04 21:58:02.927] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.a [o.a.x.a.v]
[2025-08-04 21:58:02.928] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:20, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:02.936] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.s [o.a.x.a.v]
[2025-08-04 21:58:02.936] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.k [o.a.x.a.v]
[2025-08-04 21:58:02.936] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.c [o.a.x.a.v]
[2025-08-04 21:58:02.938] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.m$d [o.a.x.a.v]
[2025-08-04 21:58:02.938] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.ab [o.a.x.a.v]
[2025-08-04 21:58:02.938] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.b.b [o.a.x.a.v]
[2025-08-04 21:58:02.939] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.b.c [o.a.x.a.v]
[2025-08-04 21:58:02.939] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.b.e [o.a.x.a.v]
[2025-08-04 21:58:02.940] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.b.f [o.a.x.a.v]
[2025-08-04 21:58:02.941] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.b.a [o.a.x.a.v]
[2025-08-04 21:58:02.942] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.b.f$a [o.a.x.a.v]
[2025-08-04 21:58:02.942] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.b.a$c [o.a.x.a.v]
[2025-08-04 21:58:02.942] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.b.a$d [o.a.x.a.v]
[2025-08-04 21:58:02.943] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.b.d [o.a.x.a.v]
[2025-08-04 21:58:02.943] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.b.d$d [o.a.x.a.v]
[2025-08-04 21:58:02.943] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.b.d$e [o.a.x.a.v]
[2025-08-04 21:58:02.943] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.b.d$j [o.a.x.a.v]
[2025-08-04 21:58:02.943] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.b.d$a [o.a.x.a.v]
[2025-08-04 21:58:02.943] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.b.d$h [o.a.x.a.v]
[2025-08-04 21:58:02.944] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.b.d$g [o.a.x.a.v]
[2025-08-04 21:58:02.944] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.ac$h [o.a.x.a.v]
[2025-08-04 21:58:02.944] [WARN] [XCodeMap-Trans-0] - Change loadClass by javassist for classname:org.springframework.context.support.ContextTypeMatchClassLoader [o.a.x.a.i]
[2025-08-04 21:58:02.955] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:27, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:02.980] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:25, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:02.998] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:18, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:03.014] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:16, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:03.030] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:15, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:03.046] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:16, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:03.065] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:19, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:03.082] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:17, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:03.096] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:14, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:03.108] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:11, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:03.122] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:14, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:03.136] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:14, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:03.152] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:16, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:03.169] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:16, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:03.181] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:12, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:03.198] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:17, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:03.213] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:15, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:03.228] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:15, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:03.244] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:14, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:03.260] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:16, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:03.277] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:17, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:03.291] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:14, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:03.306] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:15, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:03.324] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:18, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:03.343] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:19, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:03.363] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:19, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:03.381] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:18, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:03.403] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:22, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:03.422] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:19, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:03.437] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:15, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:03.454] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:17, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:03.482] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:28, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:03.506] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:24, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:03.524] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:18, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:03.550] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:26, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:03.571] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:20, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:03.592] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:21, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:03.611] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:18, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:03.629] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:18, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:03.651] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:22, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:03.670] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:19, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:03.693] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:22, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:03.714] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:21, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:03.734] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:20, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:03.748] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.U [o.a.x.a.v]
[2025-08-04 21:58:03.751] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:16, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:03.769] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:18, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:03.792] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:23, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:03.809] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:17, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:03.821] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:12, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:03.836] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:15, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:03.853] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:17, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:03.870] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:17, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:03.883] [WARN] [XCodeMap-Trans-6] - Method is not accessible:jakarta/servlet/http/HttpServlet.<init>(Ljakarta/servlet/http/HttpServlet$NoBodyResponse;)V [o.a.x.a.u]
[2025-08-04 21:58:03.887] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:17, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:03.901] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:14, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:03.913] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:12, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:03.926] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:13, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:03.939] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:12, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:03.955] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:16, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:03.969] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:14, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:03.988] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:18, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:04.511] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: org.springframework.web.servlet.HttpServletBean:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> jakarta.servlet.Servlet:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef [o.a.x.a.x.X]
[2025-08-04 21:58:04.511] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: jakarta.servlet.http.HttpServlet:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> jakarta.servlet.Servlet:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef [o.a.x.a.x.X]
[2025-08-04 21:58:04.511] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: jakarta.servlet.GenericServlet:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> jakarta.servlet.Servlet:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef [o.a.x.a.x.X]
[2025-08-04 21:58:04.511] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: org.springframework.web.servlet.FrameworkServlet:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> jakarta.servlet.Servlet:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef [o.a.x.a.x.X]
[2025-08-04 21:58:04.511] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: org.springframework.web.servlet.DispatcherServlet:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> jakarta.servlet.Servlet:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef [o.a.x.a.x.X]
[2025-08-04 21:58:04.517] [WARN] [XCodeMap-Trans-3] - Skip clinit method of interface, classname:oracle/jdbc/OracleConnection method:<clinit> desc:()V [o.a.x.a.u]
[2025-08-04 21:58:04.788] [WARN] [XCodeMap-Trans-6] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.af [o.a.x.a.v]
[2025-08-04 21:58:04.804] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:14, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:04.862] [WARN] [XCodeMap-Trans-3] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.t [o.a.x.a.v]
[2025-08-04 21:58:04.988] [WARN] [XCodeMap-Trans-7] - Method is not accessible:com/aizuda/snailjob/server/job/task/support/generator/task/MapReduceTaskGenerator.ordinal()I [o.a.x.a.u]
[2025-08-04 21:58:05.239] [WARN] [XCodeMap-Trans-8] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.org.a.a.c$a [o.a.x.a.v]
[2025-08-04 21:58:05.240] [WARN] [XCodeMap-Trans-8] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.org.a.a.l [o.a.x.a.v]
[2025-08-04 21:58:05.256] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:15, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:05.515] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: oracle.jdbc.OracleConnection:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> java.sql.Connection:jdk.internal.loader.ClassLoaders$PlatformClassLoader@7fe8ea47 [o.a.x.a.x.X]
[2025-08-04 21:58:05.515] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: oracle.jdbc.OracleCommonConnection:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> java.sql.Connection:jdk.internal.loader.ClassLoaders$PlatformClassLoader@7fe8ea47 [o.a.x.a.x.X]
[2025-08-04 21:58:05.516] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: java.sql.Connection:jdk.internal.loader.ClassLoaders$PlatformClassLoader@7fe8ea47 -> java.sql.Connection:jdk.internal.loader.ClassLoaders$PlatformClassLoader@7fe8ea47 [o.a.x.a.x.X]
[2025-08-04 21:58:05.521] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: oracle.jdbc.OracleConnection:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> java.sql.Connection:jdk.internal.loader.ClassLoaders$PlatformClassLoader@7fe8ea47 [o.a.x.a.x.X]
[2025-08-04 21:58:05.521] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: oracle.jdbc.OracleCommonConnection:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> java.sql.Connection:jdk.internal.loader.ClassLoaders$PlatformClassLoader@7fe8ea47 [o.a.x.a.x.X]
[2025-08-04 21:58:05.521] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: java.sql.Connection:jdk.internal.loader.ClassLoaders$PlatformClassLoader@7fe8ea47 -> java.sql.Connection:jdk.internal.loader.ClassLoaders$PlatformClassLoader@7fe8ea47 [o.a.x.a.x.X]
[2025-08-04 21:58:06.921] [WARN] [XCodeMap-Trans-7] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.ac$b [o.a.x.a.v]
[2025-08-04 21:58:06.921] [WARN] [XCodeMap-Trans-7] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.ac$g [o.a.x.a.v]
[2025-08-04 21:58:06.922] [WARN] [XCodeMap-Trans-7] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.ac$d [o.a.x.a.v]
[2025-08-04 21:58:06.923] [WARN] [XCodeMap-Trans-7] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.b.a$a [o.a.x.a.v]
[2025-08-04 21:58:06.924] [WARN] [XCodeMap-Trans-7] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.b.d$f [o.a.x.a.v]
[2025-08-04 21:58:06.939] [WARN] [XCodeMap-Trans-7] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.b.d$b [o.a.x.a.v]
[2025-08-04 21:58:06.939] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:15, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:06.955] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:16, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:06.977] [WARN] [XCodeMap-Trans-7] - Change loadClass by javassist for classname:org.apache.catalina.loader.WebappClassLoaderBase [o.a.x.a.i]
[2025-08-04 21:58:06.983] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:27, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:07.003] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:19, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:07.003] [WARN] [XCodeMap-Trans-5] - Change loadClass by javassist for classname:org.springframework.boot.web.embedded.tomcat.TomcatEmbeddedWebappClassLoader [o.a.x.a.i]
[2025-08-04 21:58:07.022] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:19, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:07.041] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:19, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:08.573] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: java.sql.ResultSet:jdk.internal.loader.ClassLoaders$PlatformClassLoader@7fe8ea47 -> java.sql.ResultSet:jdk.internal.loader.ClassLoaders$PlatformClassLoader@7fe8ea47 [o.a.x.a.x.X]
[2025-08-04 21:58:08.577] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: java.sql.ResultSet:jdk.internal.loader.ClassLoaders$PlatformClassLoader@7fe8ea47 -> java.sql.ResultSet:jdk.internal.loader.ClassLoaders$PlatformClassLoader@7fe8ea47 [o.a.x.a.x.X]
[2025-08-04 21:58:11.618] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: java.sql.Statement:jdk.internal.loader.ClassLoaders$PlatformClassLoader@7fe8ea47 -> java.sql.Statement:jdk.internal.loader.ClassLoaders$PlatformClassLoader@7fe8ea47 [o.a.x.a.x.X]
[2025-08-04 21:58:11.622] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: java.sql.Statement:jdk.internal.loader.ClassLoaders$PlatformClassLoader@7fe8ea47 -> java.sql.Statement:jdk.internal.loader.ClassLoaders$PlatformClassLoader@7fe8ea47 [o.a.x.a.x.X]
[2025-08-04 21:58:11.622] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: java.sql.CallableStatement:jdk.internal.loader.ClassLoaders$PlatformClassLoader@7fe8ea47 -> java.sql.Statement:jdk.internal.loader.ClassLoaders$PlatformClassLoader@7fe8ea47 [o.a.x.a.x.X]
[2025-08-04 21:58:11.623] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: java.sql.PreparedStatement:jdk.internal.loader.ClassLoaders$PlatformClassLoader@7fe8ea47 -> java.sql.Statement:jdk.internal.loader.ClassLoaders$PlatformClassLoader@7fe8ea47 [o.a.x.a.x.X]
[2025-08-04 21:58:17.714] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: oracle.jdbc.driver.PhysicalConnection:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> java.sql.Connection:jdk.internal.loader.ClassLoaders$PlatformClassLoader@7fe8ea47 [o.a.x.a.x.X]
[2025-08-04 21:58:17.714] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: oracle.jdbc.driver.GeneratedPhysicalConnection:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> java.sql.Connection:jdk.internal.loader.ClassLoaders$PlatformClassLoader@7fe8ea47 [o.a.x.a.x.X]
[2025-08-04 21:58:17.714] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: oracle.jdbc.internal.OracleConnection:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> java.sql.Connection:jdk.internal.loader.ClassLoaders$PlatformClassLoader@7fe8ea47 [o.a.x.a.x.X]
[2025-08-04 21:58:17.714] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: oracle.jdbc.driver.OracleConnection:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> java.sql.Connection:jdk.internal.loader.ClassLoaders$PlatformClassLoader@7fe8ea47 [o.a.x.a.x.X]
[2025-08-04 21:58:17.714] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: oracle.jdbc.OracleConnectionWrapper:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> java.sql.Connection:jdk.internal.loader.ClassLoaders$PlatformClassLoader@7fe8ea47 [o.a.x.a.x.X]
[2025-08-04 21:58:17.714] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: com.mysql.cj.jdbc.JdbcConnection:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> java.sql.Connection:jdk.internal.loader.ClassLoaders$PlatformClassLoader@7fe8ea47 [o.a.x.a.x.X]
[2025-08-04 21:58:18.719] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: com.mysql.cj.jdbc.ConnectionImpl:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> java.sql.Connection:jdk.internal.loader.ClassLoaders$PlatformClassLoader@7fe8ea47 [o.a.x.a.x.X]
[2025-08-04 21:58:18.719] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: com.mysql.cj.jdbc.JdbcStatement:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> java.sql.Statement:jdk.internal.loader.ClassLoaders$PlatformClassLoader@7fe8ea47 [o.a.x.a.x.X]
[2025-08-04 21:58:21.731] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: com.mysql.cj.jdbc.JdbcPreparedStatement:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> java.sql.Statement:jdk.internal.loader.ClassLoaders$PlatformClassLoader@7fe8ea47 [o.a.x.a.x.X]
[2025-08-04 21:58:21.731] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: com.mysql.cj.jdbc.StatementImpl:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> java.sql.Statement:jdk.internal.loader.ClassLoaders$PlatformClassLoader@7fe8ea47 [o.a.x.a.x.X]
[2025-08-04 21:58:22.167] [WARN] [XCodeMap-Trans-1] - Method is not accessible:com/mysql/cj/jdbc/result/ResultSetImpl.ordinal()I [o.a.x.a.u]
[2025-08-04 21:58:22.170] [WARN] [XCodeMap-Trans-1] - Method is not accessible:com/mysql/cj/jdbc/result/ResultSetImpl.ordinal()I [o.a.x.a.u]
[2025-08-04 21:58:22.624] [WARN] [XCodeMap-Trans-1] - Method is not accessible:com/mysql/cj/jdbc/result/UpdatableResultSet.ordinal()I [o.a.x.a.u]
[2025-08-04 21:58:22.628] [WARN] [XCodeMap-Trans-1] - Method is not accessible:com/mysql/cj/jdbc/result/UpdatableResultSet.ordinal()I [o.a.x.a.u]
[2025-08-04 21:58:22.736] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: com.mysql.cj.jdbc.ClientPreparedStatement:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> java.sql.Statement:jdk.internal.loader.ClassLoaders$PlatformClassLoader@7fe8ea47 [o.a.x.a.x.X]
[2025-08-04 21:58:22.736] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: com.mysql.cj.jdbc.result.ResultSetInternalMethods:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> java.sql.ResultSet:jdk.internal.loader.ClassLoaders$PlatformClassLoader@7fe8ea47 [o.a.x.a.x.X]
[2025-08-04 21:58:22.736] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: com.mysql.cj.jdbc.result.ResultSetImpl:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> java.sql.ResultSet:jdk.internal.loader.ClassLoaders$PlatformClassLoader@7fe8ea47 [o.a.x.a.x.X]
[2025-08-04 21:58:22.736] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: com.mysql.cj.jdbc.ServerPreparedStatement:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> java.sql.Statement:jdk.internal.loader.ClassLoaders$PlatformClassLoader@7fe8ea47 [o.a.x.a.x.X]
[2025-08-04 21:58:23.545] [WARN] [XCodeMap-Trans-9] - Skip clinit method of interface, classname:com/aizuda/snailjob/server/common/convert/RegisterNodeInfoConverter method:<clinit> desc:()V [o.a.x.a.u]
[2025-08-04 21:58:23.711] [WARN] [XCodeMap-Trans-9] - Skip clinit method of interface, classname:com/aizuda/snailjob/server/job/task/support/JobTaskConverter method:<clinit> desc:()V [o.a.x.a.u]
[2025-08-04 21:58:23.753] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: com.zaxxer.hikari.pool.HikariProxyPreparedStatement:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> java.sql.Statement:jdk.internal.loader.ClassLoaders$PlatformClassLoader@7fe8ea47 [o.a.x.a.x.X]
[2025-08-04 21:58:23.753] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: com.zaxxer.hikari.pool.HikariProxyResultSet:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> java.sql.ResultSet:jdk.internal.loader.ClassLoaders$PlatformClassLoader@7fe8ea47 [o.a.x.a.x.X]
[2025-08-04 21:58:23.753] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: jdk.proxy3.$Proxy174:jdk.internal.loader.ClassLoaders$PlatformClassLoader@7fe8ea47 -> java.sql.Connection:jdk.internal.loader.ClassLoaders$PlatformClassLoader@7fe8ea47 [o.a.x.a.x.X]
[2025-08-04 21:58:23.753] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: com.zaxxer.hikari.pool.ProxyConnection:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> java.sql.Connection:jdk.internal.loader.ClassLoaders$PlatformClassLoader@7fe8ea47 [o.a.x.a.x.X]
[2025-08-04 21:58:23.753] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: com.zaxxer.hikari.pool.ProxyStatement:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> java.sql.Statement:jdk.internal.loader.ClassLoaders$PlatformClassLoader@7fe8ea47 [o.a.x.a.x.X]
[2025-08-04 21:58:23.753] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: com.zaxxer.hikari.pool.ProxyResultSet:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> java.sql.ResultSet:jdk.internal.loader.ClassLoaders$PlatformClassLoader@7fe8ea47 [o.a.x.a.x.X]
[2025-08-04 21:58:23.753] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: com.zaxxer.hikari.pool.ProxyPreparedStatement:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> java.sql.Statement:jdk.internal.loader.ClassLoaders$PlatformClassLoader@7fe8ea47 [o.a.x.a.x.X]
[2025-08-04 21:58:23.753] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: com.zaxxer.hikari.pool.HikariProxyConnection:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> java.sql.Connection:jdk.internal.loader.ClassLoaders$PlatformClassLoader@7fe8ea47 [o.a.x.a.x.X]
[2025-08-04 21:58:23.753] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: com.mysql.cj.jdbc.result.UpdatableResultSet:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> java.sql.ResultSet:jdk.internal.loader.ClassLoaders$PlatformClassLoader@7fe8ea47 [o.a.x.a.x.X]
[2025-08-04 21:58:23.780] [WARN] [XCodeMap-Trans-7] - Skip clinit method of interface, classname:com/aizuda/snailjob/server/retry/task/support/RetryTaskConverter method:<clinit> desc:()V [o.a.x.a.u]
[2025-08-04 21:58:23.890] [WARN] [XCodeMap-Trans-5] - Skip clinit method of interface, classname:com/aizuda/snailjob/server/retry/task/support/RetryTaskLogConverter method:<clinit> desc:()V [o.a.x.a.u]
[2025-08-04 21:58:46.640] [WARN] [XCodeMap-Trans-5] - Skip clinit method of interface, classname:com/aizuda/snailjob/server/job/task/support/WorkflowTaskConverter method:<clinit> desc:()V [o.a.x.a.u]
[2025-08-04 22:15:20.753] [WARN] [XCodeMap-Trans-7] - Skip clinit method of interface, classname:com/aizuda/snailjob/server/common/convert/JobConverter method:<clinit> desc:()V [o.a.x.a.u]
[2025-08-05 02:46:10.416] [WARN] [Thread-4] - Graceful shutdown..., metrics totalClassNum:0	transClassNum:591	transLoadedNum:0	objectExtractNum:0	objectTravelNum:0	objectExtractErrorNum:0	diffObjectNum:0	totalFuncNum:0	transFuncNum:8307	errorTransNum:0	instanceNum:0	totalFuncCallNum:0	storedFuncCallNum:0	totalObjectNum:0	conflictedObjectNum:0	totalObjectChangeNum:0	totalArrayChangeNum:0	totalPutFieldNum:0	totalSysSetNum:0	totalArrayCopyNum:0	totalColChangeNum:0	totalAtomicChangeNum:0	totalGetFieldNum:0	storedPutFieldNum:0	storedGetFieldNum:0	invokeMethodPosNum:0	putFieldPosNum:0	maxLocalsValue:0	maxStacksValue:0	stackNodeNum:0	transformTimeNano:0	 [o.a.x.a.x.X]
