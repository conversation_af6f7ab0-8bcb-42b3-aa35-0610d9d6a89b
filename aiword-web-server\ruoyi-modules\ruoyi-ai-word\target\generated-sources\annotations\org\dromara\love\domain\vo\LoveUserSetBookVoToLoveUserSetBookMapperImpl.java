package org.dromara.love.domain.vo;

import javax.annotation.processing.Generated;
import org.dromara.love.domain.LoveUserSetBook;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-07T01:55:47+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (Microsoft)"
)
@Component
public class LoveUserSetBookVoToLoveUserSetBookMapperImpl implements LoveUserSetBookVoToLoveUserSetBookMapper {

    @Override
    public LoveUserSetBook convert(LoveUserSetBookVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        LoveUserSetBook loveUserSetBook = new LoveUserSetBook();

        loveUserSetBook.setId( arg0.getId() );
        loveUserSetBook.setUserId( arg0.getUserId() );
        loveUserSetBook.setBookStudyFieldId( arg0.getBookStudyFieldId() );
        loveUserSetBook.setBookStudyFieldName( arg0.getBookStudyFieldName() );
        loveUserSetBook.setBookTypeId( arg0.getBookTypeId() );
        loveUserSetBook.setBookTypeName( arg0.getBookTypeName() );
        loveUserSetBook.setBookTextBookId( arg0.getBookTextBookId() );
        loveUserSetBook.setBookTextBookName( arg0.getBookTextBookName() );

        return loveUserSetBook;
    }

    @Override
    public LoveUserSetBook convert(LoveUserSetBookVo arg0, LoveUserSetBook arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setBookStudyFieldId( arg0.getBookStudyFieldId() );
        arg1.setBookStudyFieldName( arg0.getBookStudyFieldName() );
        arg1.setBookTypeId( arg0.getBookTypeId() );
        arg1.setBookTypeName( arg0.getBookTypeName() );
        arg1.setBookTextBookId( arg0.getBookTextBookId() );
        arg1.setBookTextBookName( arg0.getBookTextBookName() );

        return arg1;
    }
}
