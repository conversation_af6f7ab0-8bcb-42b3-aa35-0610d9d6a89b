[2025-08-04 21:58:03.055] [WARN] [main] - 检测到 JRebel，发现相关类: org.zeroturnaround.javarebel.integration.cxf.CXFPlugin [o.a.x.a.x.X]
[2025-08-04 21:58:03.058] [WARN] [main] - [XCodeMap] 检测到 JRebel，发现相关类: org.zeroturnaround.javarebel.integration.cxf.CXFPlugin
 [o.a.x.a.p]
[2025-08-04 21:58:03.059] [WARN] [main] - [XCodeMap] 开始启动, pid:25864, xcodemap.executionId:6ce95e01-53be-4e5b-8099-757f3d140f88, version:1.16.0
 [o.a.x.a.p]
[2025-08-04 21:58:03.065] [WARN] [main] - workDir:D:\MyApplication\love-word\aiword-web-server\.xcodemap agentMain:false [o.a.x.a.p]
[2025-08-04 21:58:03.261] [WARN] [main] - AsmSysChangeData 加载完成，共加载 58 个集合类，67 个系统设置方法，147 个集合变更方法 [o.a.x.a.x.X]
[2025-08-04 21:58:03.262] [WARN] [XCodeMap-Install] - [XCodeMap] Install Agent, version:1.16.0
 [o.a.x.a.p]
[2025-08-04 21:58:03.263] [WARN] [XCodeMap-Install] - Start retransformAllClasses [o.a.x.a.x.X]
[2025-08-04 21:58:03.273] [WARN] [XCodeMap-Install] - ReTransform agentmain metrics other classNum:0 [o.a.x.a.x.X]
[2025-08-04 21:58:03.274] [WARN] [XCodeMap-Install] - [XCodeMap] ReTransform class num:0
 [o.a.x.a.p]
[2025-08-04 21:58:03.274] [WARN] [XCodeMap-Install] - [XCodeMap] All 0 batches completed, total time: 0ms
 [o.a.x.a.p]
[2025-08-04 21:58:03.274] [WARN] [XCodeMap-Install] - End retransformAllClasses, cost 11 ms [o.a.x.a.x.X]
[2025-08-04 21:58:03.274] [WARN] [XCodeMap-Install] - [XCodeMap] Install Agent finished, cost 12 ms, version:1.16.0
 [o.a.x.a.p]
[2025-08-04 21:58:03.371] [WARN] [main] - [XCodeMap] 启动完成, cost 365 ms, version:1.16.0
 [o.a.x.a.p]
[2025-08-04 21:58:03.517] [WARN] [XCodeMap-Trans-4] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.B [o.a.x.a.v]
[2025-08-04 21:58:03.518] [WARN] [XCodeMap-Trans-4] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.C [o.a.x.a.v]
[2025-08-04 21:58:03.519] [WARN] [XCodeMap-Trans-4] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.A [o.a.x.a.v]
[2025-08-04 21:58:03.527] [WARN] [XCodeMap-Trans-4] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.q [o.a.x.a.v]
[2025-08-04 21:58:03.527] [WARN] [XCodeMap-Trans-4] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.y [o.a.x.a.v]
[2025-08-04 21:58:03.528] [WARN] [XCodeMap-Trans-4] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.V [o.a.x.a.v]
[2025-08-04 21:58:03.530] [WARN] [XCodeMap-Trans-4] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.P [o.a.x.a.v]
[2025-08-04 21:58:03.531] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:12, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:03.542] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:11, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:03.551] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:8, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:03.561] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:10, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:03.571] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:9, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:03.581] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:9, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:03.643] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:61, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:03.912] [WARN] [XCodeMap-Trans-3] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.org.a.a.o [o.a.x.a.v]
[2025-08-04 21:58:03.912] [WARN] [XCodeMap-Trans-3] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.org.a.a.n [o.a.x.a.v]
[2025-08-04 21:58:03.926] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:12, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:03.966] [WARN] [XCodeMap-Trans-9] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.Y [o.a.x.a.v]
[2025-08-04 21:58:04.174] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: jakarta.servlet.Servlet:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> jakarta.servlet.Servlet:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef [o.a.x.a.x.X]
[2025-08-04 21:58:04.177] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: jakarta.servlet.http.HttpServlet:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> jakarta.servlet.Servlet:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef [o.a.x.a.x.X]
[2025-08-04 21:58:04.177] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: jakarta.servlet.GenericServlet:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> jakarta.servlet.Servlet:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef [o.a.x.a.x.X]
[2025-08-04 21:58:04.177] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: org.springframework.web.servlet.DispatcherServlet:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> jakarta.servlet.Servlet:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef [o.a.x.a.x.X]
[2025-08-04 21:58:04.177] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: org.springframework.web.servlet.FrameworkServlet:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> jakarta.servlet.Servlet:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef [o.a.x.a.x.X]
[2025-08-04 21:58:04.177] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: org.springframework.web.servlet.HttpServletBean:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> jakarta.servlet.Servlet:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef [o.a.x.a.x.X]
[2025-08-04 21:58:04.177] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: jakarta.servlet.Servlet:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> jakarta.servlet.Servlet:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef [o.a.x.a.x.X]
[2025-08-04 21:58:05.437] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.h [o.a.x.a.v]
[2025-08-04 21:58:05.439] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.m [o.a.x.a.v]
[2025-08-04 21:58:05.455] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.m$a [o.a.x.a.v]
[2025-08-04 21:58:05.455] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.l [o.a.x.a.v]
[2025-08-04 21:58:05.456] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.k [o.a.x.a.v]
[2025-08-04 21:58:05.456] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:16, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:05.456] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.n [o.a.x.a.v]
[2025-08-04 21:58:05.467] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.m [o.a.x.a.v]
[2025-08-04 21:58:05.469] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.d [o.a.x.a.v]
[2025-08-04 21:58:05.469] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.i [o.a.x.a.v]
[2025-08-04 21:58:05.470] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.h [o.a.x.a.v]
[2025-08-04 21:58:05.470] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.e [o.a.x.a.v]
[2025-08-04 21:58:05.472] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.i [o.a.x.a.v]
[2025-08-04 21:58:05.472] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:16, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:05.486] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.b [o.a.x.a.v]
[2025-08-04 21:58:05.488] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:16, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:05.498] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.q [o.a.x.a.v]
[2025-08-04 21:58:05.499] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.a.v [o.a.x.a.v]
[2025-08-04 21:58:05.500] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.j [o.a.x.a.v]
[2025-08-04 21:58:05.502] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:14, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:05.502] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.f [o.a.x.a.v]
[2025-08-04 21:58:05.516] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.r [o.a.x.a.v]
[2025-08-04 21:58:05.517] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.n [o.a.x.a.v]
[2025-08-04 21:58:05.518] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.a.i [o.a.x.a.v]
[2025-08-04 21:58:05.518] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.a.a [o.a.x.a.v]
[2025-08-04 21:58:05.519] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:17, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:05.519] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.a.b [o.a.x.a.v]
[2025-08-04 21:58:05.519] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.a.t [o.a.x.a.v]
[2025-08-04 21:58:05.531] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.l [o.a.x.a.v]
[2025-08-04 21:58:05.532] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.h [o.a.x.a.v]
[2025-08-04 21:58:05.533] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.g [o.a.x.a.v]
[2025-08-04 21:58:05.534] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.p [o.a.x.a.v]
[2025-08-04 21:58:05.535] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.a.u [o.a.x.a.v]
[2025-08-04 21:58:05.535] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:16, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:05.535] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.a.o [o.a.x.a.v]
[2025-08-04 21:58:05.535] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.a.k [o.a.x.a.v]
[2025-08-04 21:58:05.535] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.a.n [o.a.x.a.v]
[2025-08-04 21:58:05.548] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.a.s [o.a.x.a.v]
[2025-08-04 21:58:05.549] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.a.f [o.a.x.a.v]
[2025-08-04 21:58:05.549] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.a.e [o.a.x.a.v]
[2025-08-04 21:58:05.549] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.a.l [o.a.x.a.v]
[2025-08-04 21:58:05.550] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.a.g [o.a.x.a.v]
[2025-08-04 21:58:05.550] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.a.r [o.a.x.a.v]
[2025-08-04 21:58:05.551] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.j$a [o.a.x.a.v]
[2025-08-04 21:58:05.552] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.a [o.a.x.a.v]
[2025-08-04 21:58:05.553] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:18, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:05.553] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.s [o.a.x.a.v]
[2025-08-04 21:58:05.554] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.k [o.a.x.a.v]
[2025-08-04 21:58:05.554] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.c [o.a.x.a.v]
[2025-08-04 21:58:05.565] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.m$d [o.a.x.a.v]
[2025-08-04 21:58:05.565] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.ab [o.a.x.a.v]
[2025-08-04 21:58:05.565] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.b.b [o.a.x.a.v]
[2025-08-04 21:58:05.568] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:15, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:05.568] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.b.c [o.a.x.a.v]
[2025-08-04 21:58:05.582] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.b.e [o.a.x.a.v]
[2025-08-04 21:58:05.582] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.b.f [o.a.x.a.v]
[2025-08-04 21:58:05.582] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:14, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:05.583] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.b.a [o.a.x.a.v]
[2025-08-04 21:58:05.583] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.b.f$a [o.a.x.a.v]
[2025-08-04 21:58:05.584] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.b.a$c [o.a.x.a.v]
[2025-08-04 21:58:05.595] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.b.a$d [o.a.x.a.v]
[2025-08-04 21:58:05.595] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.b.d [o.a.x.a.v]
[2025-08-04 21:58:05.596] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.b.d$d [o.a.x.a.v]
[2025-08-04 21:58:05.596] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.b.d$e [o.a.x.a.v]
[2025-08-04 21:58:05.597] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.b.d$j [o.a.x.a.v]
[2025-08-04 21:58:05.597] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.b.d$a [o.a.x.a.v]
[2025-08-04 21:58:05.597] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.b.d$h [o.a.x.a.v]
[2025-08-04 21:58:05.599] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.b.d$g [o.a.x.a.v]
[2025-08-04 21:58:05.600] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.ac$h [o.a.x.a.v]
[2025-08-04 21:58:05.602] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:20, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:05.602] [WARN] [XCodeMap-Trans-0] - Change loadClass by javassist for classname:org.springframework.context.support.ContextTypeMatchClassLoader [o.a.x.a.i]
[2025-08-04 21:58:05.620] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:18, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:05.646] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:26, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:05.669] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:22, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:05.689] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:20, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:05.706] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:16, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:05.727] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:21, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:05.749] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:22, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:05.763] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:14, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:05.778] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:14, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:05.792] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:14, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:05.808] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:16, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:05.820] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:12, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:05.836] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:15, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:05.852] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:16, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:05.866] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:14, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:05.881] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:15, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:05.899] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:16, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:05.912] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:13, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:05.929] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:17, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:05.944] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:15, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:05.960] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:16, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:05.975] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:15, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:05.990] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:15, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:06.003] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:13, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:06.017] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:14, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:06.030] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:13, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:06.044] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:12, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:06.057] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:13, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:06.077] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:20, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:06.090] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:13, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:06.105] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:15, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:06.118] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:13, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:06.133] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:15, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:06.153] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:20, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:06.173] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:19, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:06.185] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:12, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:06.206] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:21, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:06.226] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:20, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:06.236] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:10, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:06.250] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:14, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:06.263] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:13, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:06.275] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:12, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:06.287] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:12, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:06.300] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:12, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:06.311] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:11, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:06.323] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:12, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:06.335] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:11, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:06.349] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:14, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:06.361] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:12, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:08.849] [WARN] [XCodeMap-Trans-4] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.af [o.a.x.a.v]
[2025-08-04 21:58:08.865] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:15, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:09.133] [WARN] [XCodeMap-Trans-5] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.t [o.a.x.a.v]
[2025-08-04 21:58:09.150] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:16, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:09.795] [WARN] [XCodeMap-Trans-1] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.org.a.a.c$a [o.a.x.a.v]
[2025-08-04 21:58:09.796] [WARN] [XCodeMap-Trans-1] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.org.a.a.l [o.a.x.a.v]
[2025-08-04 21:58:09.813] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:16, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:09.828] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:15, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:10.070] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.U [o.a.x.a.v]
[2025-08-04 21:58:10.071] [WARN] [XCodeMap-Trans-0] - Method is not accessible:org/dromara/demo/service/impl/ExportExcelServiceImpl.setPData(Lorg/dromara/demo/service/impl/ExportExcelServiceImpl$DemoCityData;)V [o.a.x.a.u]
[2025-08-04 21:58:10.086] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:14, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:10.359] [WARN] [XCodeMap-Trans-1] - Method is not accessible:org/dromara/love/handler/DisturbOptionsHandler.ordinal()I [o.a.x.a.u]
[2025-08-04 21:58:10.359] [WARN] [XCodeMap-Trans-1] - Method is not accessible:org/dromara/love/handler/DisturbOptionsHandler.ordinal()I [o.a.x.a.u]
[2025-08-04 21:58:10.360] [WARN] [XCodeMap-Trans-1] - Method is not accessible:org/dromara/love/handler/DisturbOptionsHandler.ordinal()I [o.a.x.a.u]
[2025-08-04 21:58:10.388] [WARN] [XCodeMap-Trans-1] - Trigger loading new class from xcodemap trans thread group, java.nio.file.StandardCopyOption [o.a.x.a.v]
[2025-08-04 21:58:10.409] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:19, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:12.056] [WARN] [XCodeMap-Trans-6] - Method is not accessible:org/dromara/love/service/impl/LoveUserTestBookWordsRecordServiceImpl.calculateSuitableDifficultyLevel(Lorg/dromara/love/service/impl/LoveUserTestBookWordsRecordServiceImpl$TestScoreResult;Lorg/dromara/love/service/impl/LoveUserTestBookWordsRecordServiceImpl$TestScoreResult;Lorg/dromara/love/service/impl/LoveUserTestBookWordsRecordServiceImpl$TestScoreResult;)J [o.a.x.a.u]
[2025-08-04 21:58:12.598] [WARN] [XCodeMap-Trans-1] - Method is not accessible:org/dromara/workflow/service/impl/FlwCommonServiceImpl.ordinal()I [o.a.x.a.u]
[2025-08-04 21:58:12.692] [WARN] [XCodeMap-Trans-8] - Method is not accessible:org/dromara/workflow/service/impl/FlwTaskAssigneeServiceImpl.ordinal()I [o.a.x.a.u]
[2025-08-04 21:58:12.693] [WARN] [XCodeMap-Trans-8] - Method is not accessible:org/dromara/workflow/service/impl/FlwTaskAssigneeServiceImpl.ordinal()I [o.a.x.a.u]
[2025-08-04 21:58:14.912] [WARN] [XCodeMap-Trans-3] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.b [o.a.x.a.v]
[2025-08-04 21:58:14.930] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:15, classes:1 [o.a.x.a.x.X]
[2025-08-04 21:58:14.986] [WARN] [XCodeMap-Trans-7] - Method is not accessible:org/dromara/common/log/aspect/LogAspect.ordinal()I [o.a.x.a.u]
[2025-08-04 21:58:14.987] [WARN] [XCodeMap-Trans-7] - Method is not accessible:org/dromara/common/log/aspect/LogAspect.ordinal()I [o.a.x.a.u]
[2025-08-04 21:58:14.988] [WARN] [XCodeMap-Trans-7] - Method is not accessible:org/dromara/common/log/aspect/LogAspect.ordinal()I [o.a.x.a.u]
[2025-08-04 21:58:14.988] [WARN] [XCodeMap-Trans-7] - Method is not accessible:org/dromara/common/log/aspect/LogAspect.ordinal()I [o.a.x.a.u]
[2025-08-04 21:58:16.465] [WARN] [XCodeMap-Trans-6] - Skip clinit method of interface, classname:org/dromara/common/mybatis/core/mapper/BaseMapperPlus method:<clinit> desc:()V [o.a.x.a.u]
[2025-08-04 21:58:17.357] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: java.sql.Connection:jdk.internal.loader.ClassLoaders$PlatformClassLoader@3cc2931c -> java.sql.Connection:jdk.internal.loader.ClassLoaders$PlatformClassLoader@3cc2931c [o.a.x.a.x.X]
[2025-08-04 21:58:17.360] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: java.sql.Connection:jdk.internal.loader.ClassLoaders$PlatformClassLoader@3cc2931c -> java.sql.Connection:jdk.internal.loader.ClassLoaders$PlatformClassLoader@3cc2931c [o.a.x.a.x.X]
[2025-08-04 21:58:20.409] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: org.zeroturnaround.javarebel.integration.fileservlet.JakartaFileServlet:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> jakarta.servlet.Servlet:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef [o.a.x.a.x.X]
[2025-08-04 21:58:20.409] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: com.mysql.cj.jdbc.JdbcConnection:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> java.sql.Connection:jdk.internal.loader.ClassLoaders$PlatformClassLoader@3cc2931c [o.a.x.a.x.X]
[2025-08-04 21:58:20.409] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: io.undertow.servlet.handlers.DefaultServlet:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> jakarta.servlet.Servlet:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef [o.a.x.a.x.X]
[2025-08-04 21:58:22.436] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: com.mysql.cj.jdbc.ConnectionImpl:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> java.sql.Connection:jdk.internal.loader.ClassLoaders$PlatformClassLoader@3cc2931c [o.a.x.a.x.X]
[2025-08-04 21:58:22.436] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: java.sql.Statement:jdk.internal.loader.ClassLoaders$PlatformClassLoader@3cc2931c -> java.sql.Statement:jdk.internal.loader.ClassLoaders$PlatformClassLoader@3cc2931c [o.a.x.a.x.X]
[2025-08-04 21:58:22.436] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: com.mysql.cj.jdbc.JdbcStatement:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> java.sql.Statement:jdk.internal.loader.ClassLoaders$PlatformClassLoader@3cc2931c [o.a.x.a.x.X]
[2025-08-04 21:58:22.440] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: java.sql.PreparedStatement:jdk.internal.loader.ClassLoaders$PlatformClassLoader@3cc2931c -> java.sql.Statement:jdk.internal.loader.ClassLoaders$PlatformClassLoader@3cc2931c [o.a.x.a.x.X]
[2025-08-04 21:58:22.440] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: java.sql.Statement:jdk.internal.loader.ClassLoaders$PlatformClassLoader@3cc2931c -> java.sql.Statement:jdk.internal.loader.ClassLoaders$PlatformClassLoader@3cc2931c [o.a.x.a.x.X]
[2025-08-04 21:58:22.440] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: com.mysql.cj.jdbc.JdbcStatement:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> java.sql.Statement:jdk.internal.loader.ClassLoaders$PlatformClassLoader@3cc2931c [o.a.x.a.x.X]
[2025-08-04 21:58:22.440] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: java.sql.CallableStatement:jdk.internal.loader.ClassLoaders$PlatformClassLoader@3cc2931c -> java.sql.Statement:jdk.internal.loader.ClassLoaders$PlatformClassLoader@3cc2931c [o.a.x.a.x.X]
[2025-08-04 21:58:23.459] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: com.mysql.cj.jdbc.JdbcPreparedStatement:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> java.sql.Statement:jdk.internal.loader.ClassLoaders$PlatformClassLoader@3cc2931c [o.a.x.a.x.X]
[2025-08-04 21:58:23.459] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: com.mysql.cj.jdbc.StatementImpl:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> java.sql.Statement:jdk.internal.loader.ClassLoaders$PlatformClassLoader@3cc2931c [o.a.x.a.x.X]
[2025-08-04 21:58:24.429] [WARN] [XCodeMap-Trans-1] - Method is not accessible:com/mysql/cj/jdbc/result/ResultSetImpl.ordinal()I [o.a.x.a.u]
[2025-08-04 21:58:24.431] [WARN] [XCodeMap-Trans-1] - Method is not accessible:com/mysql/cj/jdbc/result/ResultSetImpl.ordinal()I [o.a.x.a.u]
[2025-08-04 21:58:24.474] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: com.mysql.cj.jdbc.ServerPreparedStatement:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> java.sql.Statement:jdk.internal.loader.ClassLoaders$PlatformClassLoader@3cc2931c [o.a.x.a.x.X]
[2025-08-04 21:58:24.474] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: com.mysql.cj.jdbc.ClientPreparedStatement:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> java.sql.Statement:jdk.internal.loader.ClassLoaders$PlatformClassLoader@3cc2931c [o.a.x.a.x.X]
[2025-08-04 21:58:24.474] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: com.mysql.cj.jdbc.result.ResultSetInternalMethods:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> java.sql.ResultSet:jdk.internal.loader.ClassLoaders$PlatformClassLoader@3cc2931c [o.a.x.a.x.X]
[2025-08-04 21:58:24.474] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: java.sql.ResultSet:jdk.internal.loader.ClassLoaders$PlatformClassLoader@3cc2931c -> java.sql.ResultSet:jdk.internal.loader.ClassLoaders$PlatformClassLoader@3cc2931c [o.a.x.a.x.X]
[2025-08-04 21:58:24.478] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: com.mysql.cj.jdbc.result.ResultSetInternalMethods:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> java.sql.ResultSet:jdk.internal.loader.ClassLoaders$PlatformClassLoader@3cc2931c [o.a.x.a.x.X]
[2025-08-04 21:58:24.478] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: java.sql.ResultSet:jdk.internal.loader.ClassLoaders$PlatformClassLoader@3cc2931c -> java.sql.ResultSet:jdk.internal.loader.ClassLoaders$PlatformClassLoader@3cc2931c [o.a.x.a.x.X]
[2025-08-04 21:58:24.809] [WARN] [XCodeMap-Trans-1] - Method is not accessible:com/mysql/cj/jdbc/result/UpdatableResultSet.ordinal()I [o.a.x.a.u]
[2025-08-04 21:58:24.813] [WARN] [XCodeMap-Trans-1] - Method is not accessible:com/mysql/cj/jdbc/result/UpdatableResultSet.ordinal()I [o.a.x.a.u]
[2025-08-04 21:58:25.494] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: com.mysql.cj.jdbc.result.ResultSetImpl:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> java.sql.ResultSet:jdk.internal.loader.ClassLoaders$PlatformClassLoader@3cc2931c [o.a.x.a.x.X]
[2025-08-04 21:58:25.494] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: com.mysql.cj.jdbc.result.UpdatableResultSet:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> java.sql.ResultSet:jdk.internal.loader.ClassLoaders$PlatformClassLoader@3cc2931c [o.a.x.a.x.X]
[2025-08-04 21:59:05.594] [WARN] [XCodeMap-Trans-9] - Error getting class modifier for not found class:org/noear/snack/core/Feature [o.a.x.a.u]
[2025-08-04 21:59:05.594] [WARN] [XCodeMap-Trans-9] - Method is not accessible:org/dromara/warm/plugin/json/JsonConvertSnack.add([Lorg/noear/snack/core/Feature;)Lorg/noear/snack/core/Options; [o.a.x.a.u]
[2025-08-04 21:59:05.594] [WARN] [XCodeMap-Trans-9] - Error getting class modifier for not found class:org/noear/snack/core/Options [o.a.x.a.u]
[2025-08-04 21:59:05.595] [WARN] [XCodeMap-Trans-9] - Method is not accessible:org/dromara/warm/plugin/json/JsonConvertSnack.stringify(Ljava/lang/Object;Lorg/noear/snack/core/Options;)Ljava/lang/String; [o.a.x.a.u]
[2025-08-04 21:59:05.606] [WARN] [XCodeMap-Trans-5] - Skip clinit method of interface, classname:org/dromara/warm/flow/core/condition/ConditionStrategy method:<clinit> desc:()V [o.a.x.a.u]
[2025-08-04 21:59:05.627] [WARN] [XCodeMap-Trans-1] - Skip clinit method of interface, classname:org/dromara/warm/flow/core/variable/VariableStrategy method:<clinit> desc:()V [o.a.x.a.u]
[2025-08-04 21:59:05.634] [WARN] [XCodeMap-Trans-3] - Skip clinit method of interface, classname:org/dromara/warm/flow/core/listener/ListenerStrategy method:<clinit> desc:()V [o.a.x.a.u]
[2025-08-04 21:59:08.006] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: com.zaxxer.hikari.pool.HikariProxyPreparedStatement:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> java.sql.Statement:jdk.internal.loader.ClassLoaders$PlatformClassLoader@3cc2931c [o.a.x.a.x.X]
[2025-08-04 21:59:08.006] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: com.p6spy.engine.wrapper.ResultSetWrapper:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> java.sql.ResultSet:jdk.internal.loader.ClassLoaders$PlatformClassLoader@3cc2931c [o.a.x.a.x.X]
[2025-08-04 21:59:08.006] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: com.zaxxer.hikari.pool.HikariProxyConnection:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> java.sql.Connection:jdk.internal.loader.ClassLoaders$PlatformClassLoader@3cc2931c [o.a.x.a.x.X]
[2025-08-04 21:59:08.006] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: com.zaxxer.hikari.pool.ProxyConnection:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> java.sql.Connection:jdk.internal.loader.ClassLoaders$PlatformClassLoader@3cc2931c [o.a.x.a.x.X]
[2025-08-04 21:59:08.006] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: com.zaxxer.hikari.pool.HikariProxyResultSet:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> java.sql.ResultSet:jdk.internal.loader.ClassLoaders$PlatformClassLoader@3cc2931c [o.a.x.a.x.X]
[2025-08-04 21:59:08.006] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: com.p6spy.engine.wrapper.PreparedStatementWrapper:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> java.sql.Statement:jdk.internal.loader.ClassLoaders$PlatformClassLoader@3cc2931c [o.a.x.a.x.X]
[2025-08-04 21:59:08.006] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: com.zaxxer.hikari.pool.ProxyStatement:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> java.sql.Statement:jdk.internal.loader.ClassLoaders$PlatformClassLoader@3cc2931c [o.a.x.a.x.X]
[2025-08-04 21:59:08.007] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: com.zaxxer.hikari.pool.ProxyPreparedStatement:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> java.sql.Statement:jdk.internal.loader.ClassLoaders$PlatformClassLoader@3cc2931c [o.a.x.a.x.X]
[2025-08-04 21:59:08.007] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: com.zaxxer.hikari.pool.ProxyResultSet:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> java.sql.ResultSet:jdk.internal.loader.ClassLoaders$PlatformClassLoader@3cc2931c [o.a.x.a.x.X]
[2025-08-04 21:59:08.007] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: jdk.proxy3.$Proxy277:jdk.internal.loader.ClassLoaders$PlatformClassLoader@3cc2931c -> java.sql.Connection:jdk.internal.loader.ClassLoaders$PlatformClassLoader@3cc2931c [o.a.x.a.x.X]
[2025-08-04 21:59:08.007] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: com.p6spy.engine.wrapper.ConnectionWrapper:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> java.sql.Connection:jdk.internal.loader.ClassLoaders$PlatformClassLoader@3cc2931c [o.a.x.a.x.X]
[2025-08-04 21:59:08.007] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: com.p6spy.engine.wrapper.StatementWrapper:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> java.sql.Statement:jdk.internal.loader.ClassLoaders$PlatformClassLoader@3cc2931c [o.a.x.a.x.X]
[2025-08-04 22:06:38.258] [WARN] [XCodeMap-Trans-7] - Skip clinit method of interface, classname:org/dromara/common/core/constant/Constants method:<clinit> desc:()V [o.a.x.a.u]
[2025-08-04 22:06:51.261] [WARN] [XCodeMap-Trans-0] - Skip clinit method of interface, classname:org/dromara/common/core/constant/SystemConstants method:<clinit> desc:()V [o.a.x.a.u]
[2025-08-04 22:06:51.485] [WARN] [XCodeMap-Trans-4] - Method is not accessible:org/dromara/common/core/utils/DateUtils.ordinal()I [o.a.x.a.u]
[2025-08-04 22:08:23.749] [WARN] [rebel-change-detector-thread] - There is other thread to re-transform the loaded class, org.dromara.common.web.core.BaseController [o.a.x.a.v]
[2025-08-04 22:08:23.757] [WARN] [rebel-change-detector-thread] - There is other thread to re-transform the loaded class, org.springframework.aop.TargetClassAware [o.a.x.a.v]
[2025-08-04 22:08:23.761] [WARN] [rebel-change-detector-thread] - There is other thread to re-transform the loaded class, org.dromara.love.controller.admin.LoveBookStudyFieldController [o.a.x.a.v]
[2025-08-04 22:08:23.773] [WARN] [rebel-change-detector-thread] - There is other thread to re-transform the loaded class, org.springframework.aop.SpringProxy [o.a.x.a.v]
[2025-08-04 22:08:23.774] [WARN] [rebel-change-detector-thread] - There is other thread to re-transform the loaded class, org.dromara.love.controller.admin.LoveBookStudyFieldController$$SpringCGLIB$$0 [o.a.x.a.v]
[2025-08-04 22:08:23.831] [WARN] [rebel-change-detector-thread] - There is other thread to re-transform the loaded class, org.springframework.cglib.proxy.Factory [o.a.x.a.v]
[2025-08-04 22:08:23.832] [WARN] [rebel-change-detector-thread] - There is other thread to re-transform the loaded class, org.springframework.aop.framework.Advised [o.a.x.a.v]
[2025-08-04 22:08:23.907] [WARN] [rebel-change-detector-thread] - This is maybe a redefine class, originalJavaClassName:org.dromara.love.controller.admin.LoveBookStudyFieldController$$M$_jr_02F7298B29FC812C_1 actualJavaClassName:org.dromara.love.controller.admin.LoveBookStudyFieldController classBeingRedefined:null [o.a.x.a.v]
[2025-08-04 22:09:02.242] [WARN] [XNIO-1 task-3] - This is maybe a redefine class, originalJavaClassName:org.dromara.love.controller.admin.LoveBookStudyFieldController$$SpringCGLIB$$0$$M$_jr_02F7298B29FC812C_1 actualJavaClassName:org.dromara.love.controller.admin.LoveBookStudyFieldController$$SpringCGLIB$$0 classBeingRedefined:null [o.a.x.a.v]
[2025-08-04 22:11:50.108] [WARN] [rebel-change-detector-thread] - This is maybe a redefine class, originalJavaClassName:org.dromara.love.controller.admin.LoveBookStudyFieldController$$M$_jr_02F7298B29FC812C_2 actualJavaClassName:org.dromara.love.controller.admin.LoveBookStudyFieldController classBeingRedefined:null [o.a.x.a.v]
[2025-08-04 22:11:59.281] [WARN] [XNIO-1 task-6] - This is maybe a redefine class, originalJavaClassName:org.dromara.love.controller.admin.LoveBookStudyFieldController$$SpringCGLIB$$0$$M$_jr_02F7298B29FC812C_2 actualJavaClassName:org.dromara.love.controller.admin.LoveBookStudyFieldController$$SpringCGLIB$$0 classBeingRedefined:null [o.a.x.a.v]
[2025-08-04 22:12:59.439] [WARN] [rebel-change-detector-thread] - This is maybe a redefine class, originalJavaClassName:org.dromara.love.controller.admin.LoveBookStudyFieldController$$M$_jr_02F7298B29FC812C_3 actualJavaClassName:org.dromara.love.controller.admin.LoveBookStudyFieldController classBeingRedefined:null [o.a.x.a.v]
[2025-08-04 22:13:38.293] [WARN] [XNIO-1 task-6] - This is maybe a redefine class, originalJavaClassName:org.dromara.love.controller.admin.LoveBookStudyFieldController$$SpringCGLIB$$0$$M$_jr_02F7298B29FC812C_3 actualJavaClassName:org.dromara.love.controller.admin.LoveBookStudyFieldController$$SpringCGLIB$$0 classBeingRedefined:null [o.a.x.a.v]
[2025-08-04 22:14:53.764] [WARN] [XCodeMap-Trans-0] - Method is not accessible:org/dromara/love/handler/DisturbOptionsHandler$1.ordinal()I [o.a.x.a.u]
[2025-08-04 22:14:53.764] [WARN] [XCodeMap-Trans-0] - Method is not accessible:org/dromara/love/handler/DisturbOptionsHandler$1.ordinal()I [o.a.x.a.u]
[2025-08-05 00:20:25.643] [WARN] [Thread-5] - Graceful shutdown..., metrics totalClassNum:0	transClassNum:1286	transLoadedNum:3	objectExtractNum:0	objectTravelNum:0	objectExtractErrorNum:0	diffObjectNum:0	totalFuncNum:0	transFuncNum:19878	errorTransNum:0	instanceNum:0	totalFuncCallNum:0	storedFuncCallNum:0	totalObjectNum:0	conflictedObjectNum:0	totalObjectChangeNum:0	totalArrayChangeNum:0	totalPutFieldNum:0	totalSysSetNum:0	totalArrayCopyNum:0	totalColChangeNum:0	totalAtomicChangeNum:0	totalGetFieldNum:0	storedPutFieldNum:0	storedGetFieldNum:0	invokeMethodPosNum:0	putFieldPosNum:0	maxLocalsValue:0	maxStacksValue:0	stackNodeNum:0	transformTimeNano:0	 [o.a.x.a.x.X]
