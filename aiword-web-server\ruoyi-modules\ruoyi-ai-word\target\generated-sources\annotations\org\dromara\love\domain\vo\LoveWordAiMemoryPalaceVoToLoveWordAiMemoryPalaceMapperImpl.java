package org.dromara.love.domain.vo;

import javax.annotation.processing.Generated;
import org.dromara.love.domain.LoveWordAiMemoryPalace;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-07T01:55:47+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (Microsoft)"
)
@Component
public class LoveWordAiMemoryPalaceVoToLoveWordAiMemoryPalaceMapperImpl implements LoveWordAiMemoryPalaceVoToLoveWordAiMemoryPalaceMapper {

    @Override
    public LoveWordAiMemoryPalace convert(LoveWordAiMemoryPalaceVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        LoveWordAiMemoryPalace loveWordAiMemoryPalace = new LoveWordAiMemoryPalace();

        loveWordAiMemoryPalace.setId( arg0.getId() );
        loveWordAiMemoryPalace.setUserId( arg0.getUserId() );
        loveWordAiMemoryPalace.setBookId( arg0.getBookId() );
        loveWordAiMemoryPalace.setUnitId( arg0.getUnitId() );
        loveWordAiMemoryPalace.setTitle( arg0.getTitle() );
        loveWordAiMemoryPalace.setTheme( arg0.getTheme() );
        loveWordAiMemoryPalace.setSelectedWords( arg0.getSelectedWords() );
        loveWordAiMemoryPalace.setStoryContent( arg0.getStoryContent() );
        loveWordAiMemoryPalace.setGenerationParams( arg0.getGenerationParams() );

        return loveWordAiMemoryPalace;
    }

    @Override
    public LoveWordAiMemoryPalace convert(LoveWordAiMemoryPalaceVo arg0, LoveWordAiMemoryPalace arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setBookId( arg0.getBookId() );
        arg1.setUnitId( arg0.getUnitId() );
        arg1.setTitle( arg0.getTitle() );
        arg1.setTheme( arg0.getTheme() );
        arg1.setSelectedWords( arg0.getSelectedWords() );
        arg1.setStoryContent( arg0.getStoryContent() );
        arg1.setGenerationParams( arg0.getGenerationParams() );

        return arg1;
    }
}
