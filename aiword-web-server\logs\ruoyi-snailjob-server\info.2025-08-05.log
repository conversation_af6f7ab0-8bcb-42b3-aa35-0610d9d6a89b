2025-08-05 00:22:06 [instance-timeout-check-thread] INFO  c.a.s.s.c.handler.InstanceManager - Node **********:28080 is offline. Removing...
2025-08-05 02:44:26 [instance-timeout-check-thread] INFO  c.a.s.s.c.handler.InstanceManager - Node **********:28080 is offline. Removing...
2025-08-05 02:46:10 [SpringApplicationShutdownHook] INFO  c.a.s.s.starter.listener.EndListener - snail-job client about to shutdown v1.6.0-beta1
2025-08-05 02:46:10 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.cache.CacheConsumerGroup - CacheRegisterTable stop
2025-08-05 02:46:10 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.cache.CacheGroupScanActor - CacheGroupScanActor stop
2025-08-05 02:46:10 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.c.CacheNotifyRateLimiter - CacheNotifyRateLimiter stop
2025-08-05 02:46:10 [SpringApplicationShutdownHook] INFO  c.a.s.server.common.cache.CacheToken - CacheToken stop
2025-08-05 02:46:10 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.handler.ServerNodeBalance - ServerNodeBalance start. 
2025-08-05 02:46:10 [config-version-sync] INFO  c.a.s.s.c.h.ConfigVersionSyncHandler - [config-version-sync] thread stop.
2025-08-05 02:46:10 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.handler.ServerNodeBalance - delete node success. [1952368460654854144]
2025-08-05 02:46:10 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.handler.ServerNodeBalance - ServerNodeBalance close complete
2025-08-05 02:46:10 [SpringApplicationShutdownHook] INFO  c.a.s.s.c.register.ServerRegister - ServerRegister close
2025-08-05 02:46:10 [SpringApplicationShutdownHook] INFO  c.a.s.s.r.t.s.c.CacheGroupRateLimiter - CacheGroupRateLimiter stop
2025-08-05 02:46:10 [SpringApplicationShutdownHook] INFO  c.a.s.s.starter.listener.EndListener - snail-job client closed successfully v1.6.0-beta1
2025-08-05 02:46:10 [config-version-sync] INFO  c.a.s.s.w.s.h.SyncConfigHandler - [config-version-sync] thread stop.
2025-08-05 02:46:10 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.tomcat.GracefulShutdown - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-05 02:46:10 [tomcat-shutdown] INFO  o.s.b.w.e.tomcat.GracefulShutdown - Graceful shutdown complete
2025-08-05 02:46:11 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-08-05 02:46:11 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-08-05 02:47:25 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-08-05 02:47:25 [main] INFO  o.d.s.SnailJobServerApplication - Starting SnailJobServerApplication using Java 17.0.15 with PID 17432 (D:\MyApplication\love-word\aiword-web-server\ruoyi-extend\ruoyi-snailjob-server\target\classes started by Administrator in D:\MyApplication\love-word\aiword-web-server)
2025-08-05 02:47:25 [main] INFO  o.d.s.SnailJobServerApplication - The following 1 profile is active: "dev"
2025-08-05 02:47:28 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8800 (http)
2025-08-05 02:47:28 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8800"]
2025-08-05 02:47:28 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-05 02:47:28 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.39]
2025-08-05 02:47:28 [main] INFO  o.a.c.c.C.[.[localhost].[/snail-job] - Initializing Spring embedded WebApplicationContext
2025-08-05 02:47:28 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2639 ms
2025-08-05 02:47:30 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [admin/index.html]
2025-08-05 02:47:31 [COMMON_ACTOR_SYSTEM-pekko.actor.default-dispatcher-4] INFO  o.a.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-08-05 02:47:32 [NETTY_ACTOR_SYSTEM-pekko.actor.default-dispatcher-5] INFO  o.a.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-08-05 02:47:32 [RETRY_ACTOR_SYSTEM-pekko.actor.default-dispatcher-5] INFO  o.a.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-08-05 02:47:32 [JOB_ACTOR_SYSTEM-pekko.actor.default-dispatcher-5] INFO  o.a.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-08-05 02:47:32 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 15 endpoints beneath base path '/actuator'
2025-08-05 02:47:32 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8800"]
2025-08-05 02:47:32 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8800 (http) with context path '/snail-job'
2025-08-05 02:47:32 [main] INFO  c.a.s.s.s.listener.StartListener - snail-job server is preparing to start... v1.6.0-beta1
2025-08-05 02:47:32 [main] INFO  c.a.s.s.common.cache.CacheLockRecord - CacheLockRecord start
2025-08-05 02:47:33 [main] INFO  c.a.s.s.c.rpc.server.grpc.GrpcServer - ------> snail-job remoting server start success, grpc = com.aizuda.snailjob.server.common.rpc.server.grpc.GrpcServer, port = 17888
2025-08-05 02:47:33 [main] INFO  c.a.s.s.c.cache.CacheConsumerGroup - CacheRegisterTable start
2025-08-05 02:47:33 [main] INFO  c.a.s.s.c.cache.CacheGroupScanActor - CacheGroupScanActor start
2025-08-05 02:47:33 [main] INFO  c.a.s.s.c.c.CacheNotifyRateLimiter - CacheNotifyRateLimiter start
2025-08-05 02:47:33 [main] INFO  c.a.s.server.common.cache.CacheToken - CacheToken start
2025-08-05 02:47:33 [main] INFO  c.a.s.s.c.handler.ServerNodeBalance - ServerNodeBalance start
2025-08-05 02:47:33 [main] INFO  c.a.s.s.c.register.ServerRegister - ServerRegister start
2025-08-05 02:47:33 [main] INFO  c.a.s.s.j.t.s.a.l.JobTaskFailAlarmListener - JobTaskFailAlarmListener started
2025-08-05 02:47:33 [main] INFO  c.a.s.s.j.t.s.a.l.WorkflowTaskFailAlarmListener - WorkflowTaskFailAlarmListener started
2025-08-05 02:47:33 [main] INFO  c.a.s.s.r.t.s.c.CacheGroupRateLimiter - CacheGroupRateLimiter start
2025-08-05 02:47:33 [main] INFO  c.a.s.s.r.t.s.l.RetryTaskFailAlarmListener - RetryTaskFailAlarmListener started
2025-08-05 02:47:33 [main] INFO  c.a.s.s.r.t.s.l.RetryTaskFailDeadLetterAlarmListener - RetryTaskFailDeadLetterAlarmListener started
2025-08-05 02:47:33 [main] INFO  c.a.s.s.s.listener.StartListener - snail-job server started successfully v1.6.0-beta1
2025-08-05 02:47:33 [main] INFO  o.d.s.SnailJobServerApplication - Started SnailJobServerApplication in 8.724 seconds (process running for 14.695)
2025-08-05 02:47:33 [snail-job-scheduled-thread-2] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-08-05 02:47:33 [snail-job-scheduled-thread-2] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@4473ab5b
2025-08-05 02:47:33 [snail-job-scheduled-thread-2] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-08-05 02:47:43 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - rebalance start
2025-08-05 02:47:43 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - rebalance complete. allocate:[[0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127]]
2025-08-05 03:12:13 [instance-timeout-check-thread] INFO  c.a.s.s.c.handler.InstanceManager - Node **********:28080 is offline. Removing...
2025-08-05 03:21:03 [instance-timeout-check-thread] INFO  c.a.s.s.c.handler.InstanceManager - Node **********:28080 is offline. Removing...
2025-08-05 18:16:40 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-08-05 18:16:40 [main] INFO  o.d.s.SnailJobServerApplication - Starting SnailJobServerApplication using Java 17.0.15 with PID 32068 (D:\MyApplication\love-word\aiword-web-server\ruoyi-extend\ruoyi-snailjob-server\target\classes started by Administrator in D:\MyApplication\love-word\aiword-web-server)
2025-08-05 18:16:40 [main] INFO  o.d.s.SnailJobServerApplication - The following 1 profile is active: "dev"
2025-08-05 18:16:43 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8800 (http)
2025-08-05 18:16:43 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8800"]
2025-08-05 18:16:43 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-05 18:16:43 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.39]
2025-08-05 18:16:43 [main] INFO  o.a.c.c.C.[.[localhost].[/snail-job] - Initializing Spring embedded WebApplicationContext
2025-08-05 18:16:43 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2583 ms
2025-08-05 18:16:45 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [admin/index.html]
2025-08-05 18:16:47 [COMMON_ACTOR_SYSTEM-pekko.actor.default-dispatcher-5] INFO  o.a.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-08-05 18:16:47 [NETTY_ACTOR_SYSTEM-pekko.actor.default-dispatcher-5] INFO  o.a.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-08-05 18:16:47 [RETRY_ACTOR_SYSTEM-pekko.actor.default-dispatcher-5] INFO  o.a.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-08-05 18:16:47 [JOB_ACTOR_SYSTEM-pekko.actor.default-dispatcher-5] INFO  o.a.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-08-05 18:16:47 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 15 endpoints beneath base path '/actuator'
2025-08-05 18:16:47 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8800"]
2025-08-05 18:16:47 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8800 (http) with context path '/snail-job'
2025-08-05 18:16:47 [main] INFO  c.a.s.s.s.listener.StartListener - snail-job server is preparing to start... v1.6.0-beta1
2025-08-05 18:16:47 [main] INFO  c.a.s.s.common.cache.CacheLockRecord - CacheLockRecord start
2025-08-05 18:16:48 [main] INFO  c.a.s.s.c.rpc.server.grpc.GrpcServer - ------> snail-job remoting server start success, grpc = com.aizuda.snailjob.server.common.rpc.server.grpc.GrpcServer, port = 17888
2025-08-05 18:16:48 [main] INFO  c.a.s.s.c.cache.CacheConsumerGroup - CacheRegisterTable start
2025-08-05 18:16:48 [main] INFO  c.a.s.s.c.cache.CacheGroupScanActor - CacheGroupScanActor start
2025-08-05 18:16:48 [main] INFO  c.a.s.s.c.c.CacheNotifyRateLimiter - CacheNotifyRateLimiter start
2025-08-05 18:16:48 [main] INFO  c.a.s.server.common.cache.CacheToken - CacheToken start
2025-08-05 18:16:48 [main] INFO  c.a.s.s.c.handler.ServerNodeBalance - ServerNodeBalance start
2025-08-05 18:16:48 [main] INFO  c.a.s.s.c.register.ServerRegister - ServerRegister start
2025-08-05 18:16:48 [main] INFO  c.a.s.s.j.t.s.a.l.JobTaskFailAlarmListener - JobTaskFailAlarmListener started
2025-08-05 18:16:48 [main] INFO  c.a.s.s.j.t.s.a.l.WorkflowTaskFailAlarmListener - WorkflowTaskFailAlarmListener started
2025-08-05 18:16:48 [main] INFO  c.a.s.s.r.t.s.c.CacheGroupRateLimiter - CacheGroupRateLimiter start
2025-08-05 18:16:48 [main] INFO  c.a.s.s.r.t.s.l.RetryTaskFailAlarmListener - RetryTaskFailAlarmListener started
2025-08-05 18:16:48 [main] INFO  c.a.s.s.r.t.s.l.RetryTaskFailDeadLetterAlarmListener - RetryTaskFailDeadLetterAlarmListener started
2025-08-05 18:16:48 [main] INFO  c.a.s.s.s.listener.StartListener - snail-job server started successfully v1.6.0-beta1
2025-08-05 18:16:48 [main] INFO  o.d.s.SnailJobServerApplication - Started SnailJobServerApplication in 8.681 seconds (process running for 14.267)
2025-08-05 18:16:48 [snail-job-scheduled-thread-4] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-08-05 18:16:49 [snail-job-scheduled-thread-4] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@7e41be0b
2025-08-05 18:16:49 [snail-job-scheduled-thread-4] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-08-05 18:16:58 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - rebalance start
2025-08-05 18:16:58 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - rebalance complete. allocate:[[0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127]]
2025-08-05 18:17:18 [JOB_ACTOR_SYSTEM-pekko.actor.job-task-executor-call-client-dispatcher-9] INFO  c.a.s.s.j.t.s.e.j.RequestClientActor - Task ID:[624] Task scheduled successfully.
