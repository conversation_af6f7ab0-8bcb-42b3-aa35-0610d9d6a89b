package org.dromara.love.domain;

import javax.annotation.processing.Generated;
import org.dromara.love.domain.vo.LoveUserStudySessionDetailVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-07T01:55:47+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (Microsoft)"
)
@Component
public class LoveUserStudySessionDetailToLoveUserStudySessionDetailVoMapperImpl implements LoveUserStudySessionDetailToLoveUserStudySessionDetailVoMapper {

    @Override
    public LoveUserStudySessionDetailVo convert(LoveUserStudySessionDetail arg0) {
        if ( arg0 == null ) {
            return null;
        }

        LoveUserStudySessionDetailVo loveUserStudySessionDetailVo = new LoveUserStudySessionDetailVo();

        loveUserStudySessionDetailVo.setId( arg0.getId() );
        loveUserStudySessionDetailVo.setSessionId( arg0.getSessionId() );
        loveUserStudySessionDetailVo.setUserId( arg0.getUserId() );
        loveUserStudySessionDetailVo.setWordId( arg0.getWordId() );
        if ( arg0.getActionType() != null ) {
            loveUserStudySessionDetailVo.setActionType( arg0.getActionType().longValue() );
        }
        if ( arg0.getComponentType() != null ) {
            loveUserStudySessionDetailVo.setComponentType( arg0.getComponentType().longValue() );
        }
        if ( arg0.getIsCorrect() != null ) {
            loveUserStudySessionDetailVo.setIsCorrect( arg0.getIsCorrect().longValue() );
        }
        loveUserStudySessionDetailVo.setResponseTime( arg0.getResponseTime() );
        if ( arg0.getStatusBefore() != null ) {
            loveUserStudySessionDetailVo.setStatusBefore( arg0.getStatusBefore().longValue() );
        }
        if ( arg0.getStatusAfter() != null ) {
            loveUserStudySessionDetailVo.setStatusAfter( arg0.getStatusAfter().longValue() );
        }
        loveUserStudySessionDetailVo.setActionTime( arg0.getActionTime() );

        return loveUserStudySessionDetailVo;
    }

    @Override
    public LoveUserStudySessionDetailVo convert(LoveUserStudySessionDetail arg0, LoveUserStudySessionDetailVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setSessionId( arg0.getSessionId() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setWordId( arg0.getWordId() );
        if ( arg0.getActionType() != null ) {
            arg1.setActionType( arg0.getActionType().longValue() );
        }
        else {
            arg1.setActionType( null );
        }
        if ( arg0.getComponentType() != null ) {
            arg1.setComponentType( arg0.getComponentType().longValue() );
        }
        else {
            arg1.setComponentType( null );
        }
        if ( arg0.getIsCorrect() != null ) {
            arg1.setIsCorrect( arg0.getIsCorrect().longValue() );
        }
        else {
            arg1.setIsCorrect( null );
        }
        arg1.setResponseTime( arg0.getResponseTime() );
        if ( arg0.getStatusBefore() != null ) {
            arg1.setStatusBefore( arg0.getStatusBefore().longValue() );
        }
        else {
            arg1.setStatusBefore( null );
        }
        if ( arg0.getStatusAfter() != null ) {
            arg1.setStatusAfter( arg0.getStatusAfter().longValue() );
        }
        else {
            arg1.setStatusAfter( null );
        }
        arg1.setActionTime( arg0.getActionTime() );

        return arg1;
    }
}
