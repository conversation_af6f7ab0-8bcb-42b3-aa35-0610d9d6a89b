package org.dromara.love.domain;

import io.github.linpeilie.AutoMapperConfig__787;
import io.github.linpeilie.BaseMapper;
import org.dromara.love.domain.bo.LoveWordAiDetailsBoToLoveWordAiDetailsMapper;
import org.dromara.love.domain.vo.LoveWordAiDetailsVo;
import org.dromara.love.domain.vo.LoveWordAiDetailsVoToLoveWordAiDetailsMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__787.class,
    uses = {LoveWordAiDetailsBoToLoveWordAiDetailsMapper.class,LoveWordAiDetailsVoToLoveWordAiDetailsMapper.class},
    imports = {}
)
public interface LoveWordAiDetailsToLoveWordAiDetailsVoMapper extends BaseMapper<LoveWordAiDetails, LoveWordAiDetailsVo> {
}
