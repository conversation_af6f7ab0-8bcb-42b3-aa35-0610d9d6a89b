package org.dromara.love.domain.vo;

import io.github.linpeilie.AutoMapperConfig__787;
import io.github.linpeilie.BaseMapper;
import org.dromara.love.domain.LoveUserTestBookWordsRecord;
import org.dromara.love.domain.LoveUserTestBookWordsRecordToLoveUserTestBookWordsRecordVoMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__787.class,
    uses = {LoveUserTestBookWordsRecordToLoveUserTestBookWordsRecordVoMapper.class},
    imports = {}
)
public interface LoveUserTestBookWordsRecordVoToLoveUserTestBookWordsRecordMapper extends BaseMapper<LoveUserTestBookWordsRecordVo, LoveUserTestBookWordsRecord> {
}
