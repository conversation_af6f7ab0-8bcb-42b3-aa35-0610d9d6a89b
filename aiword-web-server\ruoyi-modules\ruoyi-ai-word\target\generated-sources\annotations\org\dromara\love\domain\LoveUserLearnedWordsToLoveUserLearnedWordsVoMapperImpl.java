package org.dromara.love.domain;

import javax.annotation.processing.Generated;
import org.dromara.love.domain.vo.LoveUserLearnedWordsVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-07T01:55:47+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (Microsoft)"
)
@Component
public class LoveUserLearnedWordsToLoveUserLearnedWordsVoMapperImpl implements LoveUserLearnedWordsToLoveUserLearnedWordsVoMapper {

    @Override
    public LoveUserLearnedWordsVo convert(LoveUserLearnedWords arg0) {
        if ( arg0 == null ) {
            return null;
        }

        LoveUserLearnedWordsVo loveUserLearnedWordsVo = new LoveUserLearnedWordsVo();

        loveUserLearnedWordsVo.setId( arg0.getId() );
        loveUserLearnedWordsVo.setUserId( arg0.getUserId() );
        loveUserLearnedWordsVo.setBookId( arg0.getBookId() );
        loveUserLearnedWordsVo.setUnitId( arg0.getUnitId() );
        loveUserLearnedWordsVo.setWordId( arg0.getWordId() );
        loveUserLearnedWordsVo.setFirstLearnTime( arg0.getFirstLearnTime() );
        loveUserLearnedWordsVo.setLastLearnTime( arg0.getLastLearnTime() );
        loveUserLearnedWordsVo.setReviewLevel( arg0.getReviewLevel() );
        loveUserLearnedWordsVo.setNextReviewDate( arg0.getNextReviewDate() );
        loveUserLearnedWordsVo.setLastReviewTime( arg0.getLastReviewTime() );
        loveUserLearnedWordsVo.setReviewCount( arg0.getReviewCount() );
        loveUserLearnedWordsVo.setCorrectReviewCount( arg0.getCorrectReviewCount() );

        return loveUserLearnedWordsVo;
    }

    @Override
    public LoveUserLearnedWordsVo convert(LoveUserLearnedWords arg0, LoveUserLearnedWordsVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setBookId( arg0.getBookId() );
        arg1.setUnitId( arg0.getUnitId() );
        arg1.setWordId( arg0.getWordId() );
        arg1.setFirstLearnTime( arg0.getFirstLearnTime() );
        arg1.setLastLearnTime( arg0.getLastLearnTime() );
        arg1.setReviewLevel( arg0.getReviewLevel() );
        arg1.setNextReviewDate( arg0.getNextReviewDate() );
        arg1.setLastReviewTime( arg0.getLastReviewTime() );
        arg1.setReviewCount( arg0.getReviewCount() );
        arg1.setCorrectReviewCount( arg0.getCorrectReviewCount() );

        return arg1;
    }
}
