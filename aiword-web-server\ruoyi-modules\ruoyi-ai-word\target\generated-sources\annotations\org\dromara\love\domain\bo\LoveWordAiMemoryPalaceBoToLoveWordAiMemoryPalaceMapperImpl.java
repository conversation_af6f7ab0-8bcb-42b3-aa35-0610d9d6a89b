package org.dromara.love.domain.bo;

import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.dromara.love.domain.LoveWordAiMemoryPalace;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-07T01:55:47+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (Microsoft)"
)
@Component
public class LoveWordAiMemoryPalaceBoToLoveWordAiMemoryPalaceMapperImpl implements LoveWordAiMemoryPalaceBoToLoveWordAiMemoryPalaceMapper {

    @Override
    public LoveWordAiMemoryPalace convert(LoveWordAiMemoryPalaceBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        LoveWordAiMemoryPalace loveWordAiMemoryPalace = new LoveWordAiMemoryPalace();

        loveWordAiMemoryPalace.setSearchValue( arg0.getSearchValue() );
        loveWordAiMemoryPalace.setCreateDept( arg0.getCreateDept() );
        loveWordAiMemoryPalace.setCreateBy( arg0.getCreateBy() );
        loveWordAiMemoryPalace.setCreateTime( arg0.getCreateTime() );
        loveWordAiMemoryPalace.setUpdateBy( arg0.getUpdateBy() );
        loveWordAiMemoryPalace.setUpdateTime( arg0.getUpdateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            loveWordAiMemoryPalace.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        loveWordAiMemoryPalace.setId( arg0.getId() );
        loveWordAiMemoryPalace.setUserId( arg0.getUserId() );
        loveWordAiMemoryPalace.setBookId( arg0.getBookId() );
        loveWordAiMemoryPalace.setUnitId( arg0.getUnitId() );
        loveWordAiMemoryPalace.setTitle( arg0.getTitle() );
        loveWordAiMemoryPalace.setTheme( arg0.getTheme() );
        loveWordAiMemoryPalace.setSelectedWords( arg0.getSelectedWords() );
        loveWordAiMemoryPalace.setStoryContent( arg0.getStoryContent() );
        loveWordAiMemoryPalace.setGenerationParams( arg0.getGenerationParams() );

        return loveWordAiMemoryPalace;
    }

    @Override
    public LoveWordAiMemoryPalace convert(LoveWordAiMemoryPalaceBo arg0, LoveWordAiMemoryPalace arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setId( arg0.getId() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setBookId( arg0.getBookId() );
        arg1.setUnitId( arg0.getUnitId() );
        arg1.setTitle( arg0.getTitle() );
        arg1.setTheme( arg0.getTheme() );
        arg1.setSelectedWords( arg0.getSelectedWords() );
        arg1.setStoryContent( arg0.getStoryContent() );
        arg1.setGenerationParams( arg0.getGenerationParams() );

        return arg1;
    }
}
