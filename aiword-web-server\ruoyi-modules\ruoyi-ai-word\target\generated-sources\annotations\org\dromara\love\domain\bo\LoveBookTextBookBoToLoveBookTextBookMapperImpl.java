package org.dromara.love.domain.bo;

import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.dromara.love.domain.LoveBookTextBook;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-07T01:55:47+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (Microsoft)"
)
@Component
public class LoveBookTextBookBoToLoveBookTextBookMapperImpl implements LoveBookTextBookBoToLoveBookTextBookMapper {

    @Override
    public LoveBookTextBook convert(LoveBookTextBookBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        LoveBookTextBook loveBookTextBook = new LoveBookTextBook();

        loveBookTextBook.setSearchValue( arg0.getSearchValue() );
        loveBookTextBook.setCreateDept( arg0.getCreateDept() );
        loveBookTextBook.setCreateBy( arg0.getCreateBy() );
        loveBookTextBook.setCreateTime( arg0.getCreateTime() );
        loveBookTextBook.setUpdateBy( arg0.getUpdateBy() );
        loveBookTextBook.setUpdateTime( arg0.getUpdateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            loveBookTextBook.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        loveBookTextBook.setId( arg0.getId() );
        loveBookTextBook.setOId( arg0.getOId() );
        loveBookTextBook.setWordBookTypeOId( arg0.getWordBookTypeOId() );
        if ( arg0.getWordBookTypeId() != null ) {
            loveBookTextBook.setWordBookTypeId( Long.parseLong( arg0.getWordBookTypeId() ) );
        }
        if ( arg0.getWordBookFieldId() != null ) {
            loveBookTextBook.setWordBookFieldId( Long.parseLong( arg0.getWordBookFieldId() ) );
        }
        loveBookTextBook.setWordBookFieldCode( arg0.getWordBookFieldCode() );
        loveBookTextBook.setName( arg0.getName() );

        return loveBookTextBook;
    }

    @Override
    public LoveBookTextBook convert(LoveBookTextBookBo arg0, LoveBookTextBook arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setId( arg0.getId() );
        arg1.setOId( arg0.getOId() );
        arg1.setWordBookTypeOId( arg0.getWordBookTypeOId() );
        if ( arg0.getWordBookTypeId() != null ) {
            arg1.setWordBookTypeId( Long.parseLong( arg0.getWordBookTypeId() ) );
        }
        else {
            arg1.setWordBookTypeId( null );
        }
        if ( arg0.getWordBookFieldId() != null ) {
            arg1.setWordBookFieldId( Long.parseLong( arg0.getWordBookFieldId() ) );
        }
        else {
            arg1.setWordBookFieldId( null );
        }
        arg1.setWordBookFieldCode( arg0.getWordBookFieldCode() );
        arg1.setName( arg0.getName() );

        return arg1;
    }
}
