package org.dromara.love.domain.vo;

import io.github.linpeilie.AutoMapperConfig__787;
import io.github.linpeilie.BaseMapper;
import org.dromara.love.domain.LoveUserStudySession;
import org.dromara.love.domain.LoveUserStudySessionToLoveUserStudySessionVoMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__787.class,
    uses = {LoveUserStudySessionToLoveUserStudySessionVoMapper.class},
    imports = {}
)
public interface LoveUserStudySessionVoToLoveUserStudySessionMapper extends BaseMapper<LoveUserStudySessionVo, LoveUserStudySession> {
}
