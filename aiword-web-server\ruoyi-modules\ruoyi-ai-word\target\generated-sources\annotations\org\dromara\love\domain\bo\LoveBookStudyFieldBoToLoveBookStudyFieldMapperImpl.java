package org.dromara.love.domain.bo;

import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.dromara.love.domain.LoveBookStudyField;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-07T01:55:47+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (Microsoft)"
)
@Component
public class LoveBookStudyFieldBoToLoveBookStudyFieldMapperImpl implements LoveBookStudyFieldBoToLoveBookStudyFieldMapper {

    @Override
    public LoveBookStudyField convert(LoveBookStudyFieldBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        LoveBookStudyField loveBookStudyField = new LoveBookStudyField();

        loveBookStudyField.setSearchValue( arg0.getSearchValue() );
        loveBookStudyField.setCreateDept( arg0.getCreateDept() );
        loveBookStudyField.setCreateBy( arg0.getCreateBy() );
        loveBookStudyField.setCreateTime( arg0.getCreateTime() );
        loveBookStudyField.setUpdateBy( arg0.getUpdateBy() );
        loveBookStudyField.setUpdateTime( arg0.getUpdateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            loveBookStudyField.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        loveBookStudyField.setId( arg0.getId() );
        loveBookStudyField.setName( arg0.getName() );
        loveBookStudyField.setCode( arg0.getCode() );
        loveBookStudyField.setStatus( arg0.getStatus() );

        return loveBookStudyField;
    }

    @Override
    public LoveBookStudyField convert(LoveBookStudyFieldBo arg0, LoveBookStudyField arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setId( arg0.getId() );
        arg1.setName( arg0.getName() );
        arg1.setCode( arg0.getCode() );
        arg1.setStatus( arg0.getStatus() );

        return arg1;
    }
}
