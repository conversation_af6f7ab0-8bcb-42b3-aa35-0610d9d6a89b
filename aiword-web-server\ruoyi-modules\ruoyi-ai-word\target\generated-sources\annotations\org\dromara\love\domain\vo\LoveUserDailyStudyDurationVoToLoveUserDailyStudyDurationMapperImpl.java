package org.dromara.love.domain.vo;

import javax.annotation.processing.Generated;
import org.dromara.love.domain.LoveUserDailyStudyDuration;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-07T01:55:47+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (Microsoft)"
)
@Component
public class LoveUserDailyStudyDurationVoToLoveUserDailyStudyDurationMapperImpl implements LoveUserDailyStudyDurationVoToLoveUserDailyStudyDurationMapper {

    @Override
    public LoveUserDailyStudyDuration convert(LoveUserDailyStudyDurationVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        LoveUserDailyStudyDuration loveUserDailyStudyDuration = new LoveUserDailyStudyDuration();

        loveUserDailyStudyDuration.setUserId( arg0.getUserId() );
        loveUserDailyStudyDuration.setStudyDate( arg0.getStudyDate() );
        loveUserDailyStudyDuration.setLastUpdateTime( arg0.getLastUpdateTime() );

        return loveUserDailyStudyDuration;
    }

    @Override
    public LoveUserDailyStudyDuration convert(LoveUserDailyStudyDurationVo arg0, LoveUserDailyStudyDuration arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setUserId( arg0.getUserId() );
        arg1.setStudyDate( arg0.getStudyDate() );
        arg1.setLastUpdateTime( arg0.getLastUpdateTime() );

        return arg1;
    }
}
