package org.dromara.love.domain;

import io.github.linpeilie.AutoMapperConfig__787;
import io.github.linpeilie.BaseMapper;
import org.dromara.love.domain.bo.LoveUserSetBookBoToLoveUserSetBookMapper;
import org.dromara.love.domain.vo.LoveUserSetBookVo;
import org.dromara.love.domain.vo.LoveUserSetBookVoToLoveUserSetBookMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__787.class,
    uses = {LoveUserSetBookVoToLoveUserSetBookMapper.class,LoveUserSetBookBoToLoveUserSetBookMapper.class},
    imports = {}
)
public interface LoveUserSetBookToLoveUserSetBookVoMapper extends BaseMapper<LoveUserSetBook, LoveUserSetBookVo> {
}
