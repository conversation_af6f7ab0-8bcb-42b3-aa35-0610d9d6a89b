package org.dromara.love.domain.vo;

import javax.annotation.processing.Generated;
import org.dromara.love.domain.LoveWordAiDetails;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-07T01:55:47+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (Microsoft)"
)
@Component
public class LoveWordAiDetailsVoToLoveWordAiDetailsMapperImpl implements LoveWordAiDetailsVoToLoveWordAiDetailsMapper {

    @Override
    public LoveWordAiDetails convert(LoveWordAiDetailsVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        LoveWordAiDetails loveWordAiDetails = new LoveWordAiDetails();

        loveWordAiDetails.setId( arg0.getId() );
        loveWordAiDetails.setWordId( arg0.getWordId() );
        loveWordAiDetails.setAiDetails( arg0.getAiDetails() );

        return loveWordAiDetails;
    }

    @Override
    public LoveWordAiDetails convert(LoveWordAiDetailsVo arg0, LoveWordAiDetails arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setWordId( arg0.getWordId() );
        arg1.setAiDetails( arg0.getAiDetails() );

        return arg1;
    }
}
