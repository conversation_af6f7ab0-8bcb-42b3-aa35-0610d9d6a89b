package org.dromara.love.domain.vo;

import io.github.linpeilie.AutoMapperConfig__787;
import io.github.linpeilie.BaseMapper;
import org.dromara.love.domain.LoveWordAiMemoryPalace;
import org.dromara.love.domain.LoveWordAiMemoryPalaceToLoveWordAiMemoryPalaceVoMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__787.class,
    uses = {LoveWordAiMemoryPalaceToLoveWordAiMemoryPalaceVoMapper.class},
    imports = {}
)
public interface LoveWordAiMemoryPalaceVoToLoveWordAiMemoryPalaceMapper extends BaseMapper<LoveWordAiMemoryPalaceVo, LoveWordAiMemoryPalace> {
}
