package org.dromara.love.domain.vo;

import javax.annotation.processing.Generated;
import org.dromara.love.domain.LoveBookType;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-07T01:55:47+0800",
    comments = "version: 1.5.5.Final, compiler: java<PERSON>, environment: Java 17.0.15 (Microsoft)"
)
@Component
public class LoveBookTypeVoToLoveBookTypeMapperImpl implements LoveBookTypeVoToLoveBookTypeMapper {

    @Override
    public LoveBookType convert(LoveBookTypeVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        LoveBookType loveBookType = new LoveBookType();

        loveBookType.setId( arg0.getId() );
        loveBookType.setName( arg0.getName() );

        return loveBookType;
    }

    @Override
    public LoveBookType convert(LoveBookTypeVo arg0, LoveBookType arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setName( arg0.getName() );

        return arg1;
    }
}
