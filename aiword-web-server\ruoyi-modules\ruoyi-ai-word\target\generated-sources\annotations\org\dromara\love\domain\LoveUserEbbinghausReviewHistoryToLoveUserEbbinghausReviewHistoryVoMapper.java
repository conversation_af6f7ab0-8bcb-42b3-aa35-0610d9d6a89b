package org.dromara.love.domain;

import io.github.linpeilie.AutoMapperConfig__787;
import io.github.linpeilie.BaseMapper;
import org.dromara.love.domain.bo.LoveUserEbbinghausReviewHistoryBoToLoveUserEbbinghausReviewHistoryMapper;
import org.dromara.love.domain.vo.LoveUserEbbinghausReviewHistoryVo;
import org.dromara.love.domain.vo.LoveUserEbbinghausReviewHistoryVoToLoveUserEbbinghausReviewHistoryMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__787.class,
    uses = {LoveUserEbbinghausReviewHistoryBoToLoveUserEbbinghausReviewHistoryMapper.class,LoveUserEbbinghausReviewHistoryVoToLoveUserEbbinghausReviewHistoryMapper.class},
    imports = {}
)
public interface LoveUserEbbinghausReviewHistoryToLoveUserEbbinghausReviewHistoryVoMapper extends BaseMapper<LoveUserEbbinghausReviewHistory, LoveUserEbbinghausReviewHistoryVo> {
}
