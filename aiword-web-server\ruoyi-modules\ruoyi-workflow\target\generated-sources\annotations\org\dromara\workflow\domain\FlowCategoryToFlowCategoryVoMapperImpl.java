package org.dromara.workflow.domain;

import java.util.List;
import javax.annotation.processing.Generated;
import org.dromara.workflow.domain.vo.FlowCategoryVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-07T01:54:48+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (Microsoft)"
)
@Component
public class FlowCategoryToFlowCategoryVoMapperImpl implements FlowCategoryToFlowCategoryVoMapper {

    @Override
    public FlowCategoryVo convert(FlowCategory arg0) {
        if ( arg0 == null ) {
            return null;
        }

        FlowCategoryVo flowCategoryVo = new FlowCategoryVo();

        flowCategoryVo.setCategoryId( arg0.getCategoryId() );
        flowCategoryVo.setParentId( arg0.getParentId() );
        flowCategoryVo.setAncestors( arg0.getAncestors() );
        flowCategoryVo.setCategoryName( arg0.getCategoryName() );
        flowCategoryVo.setOrderNum( arg0.getOrderNum() );
        flowCategoryVo.setCreateTime( arg0.getCreateTime() );
        flowCategoryVo.setChildren( convert( arg0.getChildren() ) );

        return flowCategoryVo;
    }

    @Override
    public FlowCategoryVo convert(FlowCategory arg0, FlowCategoryVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setCategoryId( arg0.getCategoryId() );
        arg1.setParentId( arg0.getParentId() );
        arg1.setAncestors( arg0.getAncestors() );
        arg1.setCategoryName( arg0.getCategoryName() );
        arg1.setOrderNum( arg0.getOrderNum() );
        arg1.setCreateTime( arg0.getCreateTime() );
        if ( arg1.getChildren() != null ) {
            List<FlowCategoryVo> list = convert( arg0.getChildren() );
            if ( list != null ) {
                arg1.getChildren().clear();
                arg1.getChildren().addAll( list );
            }
            else {
                arg1.setChildren( null );
            }
        }
        else {
            List<FlowCategoryVo> list = convert( arg0.getChildren() );
            if ( list != null ) {
                arg1.setChildren( list );
            }
        }

        return arg1;
    }
}
