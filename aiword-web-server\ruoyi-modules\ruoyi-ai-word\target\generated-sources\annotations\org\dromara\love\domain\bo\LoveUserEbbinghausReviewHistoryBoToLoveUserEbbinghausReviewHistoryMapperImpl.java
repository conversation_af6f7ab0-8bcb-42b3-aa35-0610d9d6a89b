package org.dromara.love.domain.bo;

import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.dromara.love.domain.LoveUserEbbinghausReviewHistory;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-07T01:55:47+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (Microsoft)"
)
@Component
public class LoveUserEbbinghausReviewHistoryBoToLoveUserEbbinghausReviewHistoryMapperImpl implements LoveUserEbbinghausReviewHistoryBoToLoveUserEbbinghausReviewHistoryMapper {

    @Override
    public LoveUserEbbinghausReviewHistory convert(LoveUserEbbinghausReviewHistoryBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        LoveUserEbbinghausReviewHistory loveUserEbbinghausReviewHistory = new LoveUserEbbinghausReviewHistory();

        loveUserEbbinghausReviewHistory.setSearchValue( arg0.getSearchValue() );
        loveUserEbbinghausReviewHistory.setCreateDept( arg0.getCreateDept() );
        loveUserEbbinghausReviewHistory.setCreateBy( arg0.getCreateBy() );
        loveUserEbbinghausReviewHistory.setCreateTime( arg0.getCreateTime() );
        loveUserEbbinghausReviewHistory.setUpdateBy( arg0.getUpdateBy() );
        loveUserEbbinghausReviewHistory.setUpdateTime( arg0.getUpdateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            loveUserEbbinghausReviewHistory.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        loveUserEbbinghausReviewHistory.setId( arg0.getId() );
        loveUserEbbinghausReviewHistory.setUserId( arg0.getUserId() );
        loveUserEbbinghausReviewHistory.setWordId( arg0.getWordId() );
        loveUserEbbinghausReviewHistory.setLearnedWordId( arg0.getLearnedWordId() );
        loveUserEbbinghausReviewHistory.setStudySessionId( arg0.getStudySessionId() );
        loveUserEbbinghausReviewHistory.setReviewLevel( arg0.getReviewLevel() );
        loveUserEbbinghausReviewHistory.setExpectedReviewDate( arg0.getExpectedReviewDate() );
        loveUserEbbinghausReviewHistory.setActualReviewDate( arg0.getActualReviewDate() );
        loveUserEbbinghausReviewHistory.setIsOnTime( arg0.getIsOnTime() );
        loveUserEbbinghausReviewHistory.setDelayHours( arg0.getDelayHours() );

        return loveUserEbbinghausReviewHistory;
    }

    @Override
    public LoveUserEbbinghausReviewHistory convert(LoveUserEbbinghausReviewHistoryBo arg0, LoveUserEbbinghausReviewHistory arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setId( arg0.getId() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setWordId( arg0.getWordId() );
        arg1.setLearnedWordId( arg0.getLearnedWordId() );
        arg1.setStudySessionId( arg0.getStudySessionId() );
        arg1.setReviewLevel( arg0.getReviewLevel() );
        arg1.setExpectedReviewDate( arg0.getExpectedReviewDate() );
        arg1.setActualReviewDate( arg0.getActualReviewDate() );
        arg1.setIsOnTime( arg0.getIsOnTime() );
        arg1.setDelayHours( arg0.getDelayHours() );

        return arg1;
    }
}
