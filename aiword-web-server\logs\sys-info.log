2025-08-07 00:00:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 00:00:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 00:00:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[13]毫秒
2025-08-07 00:01:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 00:01:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 00:01:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 00:02:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 00:02:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 00:02:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 00:03:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 00:03:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 00:03:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 00:04:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 00:04:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 00:04:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 00:05:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 00:05:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 00:05:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 00:06:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 00:06:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 00:06:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 00:07:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 00:07:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 00:07:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 00:08:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 00:08:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 00:08:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 00:09:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 00:09:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 00:09:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 00:10:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 00:10:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 00:10:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[8]毫秒
2025-08-07 00:11:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 00:11:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 00:11:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[9]毫秒
2025-08-07 00:12:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 00:12:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 00:12:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 00:13:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 00:13:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 00:13:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[8]毫秒
2025-08-07 00:14:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 00:14:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 00:14:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 00:15:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 00:15:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 00:15:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 00:16:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 00:16:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 00:16:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[9]毫秒
2025-08-07 00:17:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 00:17:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 00:17:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[5]毫秒
2025-08-07 00:18:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 00:18:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 00:18:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 00:19:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 00:19:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 00:19:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[9]毫秒
2025-08-07 00:20:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 00:20:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 00:20:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 00:21:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 00:21:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 00:21:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[5]毫秒
2025-08-07 00:22:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 00:22:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 00:22:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[5]毫秒
2025-08-07 00:23:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 00:23:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 00:23:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[5]毫秒
2025-08-07 00:24:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 00:24:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 00:24:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 00:25:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 00:25:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 00:25:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 00:26:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 00:26:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 00:26:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 00:27:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 00:27:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 00:27:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 00:28:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 00:28:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 00:28:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[5]毫秒
2025-08-07 00:29:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 00:29:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 00:29:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 00:30:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 00:30:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 00:30:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[5]毫秒
2025-08-07 00:31:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 00:31:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 00:31:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 00:32:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 00:32:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 00:32:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 00:33:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 00:33:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 00:33:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 00:34:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 00:34:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 00:34:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[5]毫秒
2025-08-07 00:35:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 00:35:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 00:35:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 00:36:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 00:36:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 00:36:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 00:37:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 00:37:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 00:37:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 00:38:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 00:38:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 00:38:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 00:39:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 00:39:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 00:39:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 00:40:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 00:40:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 00:40:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 00:41:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 00:41:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 00:41:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 00:42:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 00:42:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 00:42:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[5]毫秒
2025-08-07 00:43:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 00:43:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 00:43:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 00:44:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 00:44:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 00:44:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 00:45:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 00:45:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 00:45:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[5]毫秒
2025-08-07 00:46:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 00:46:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 00:46:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 00:47:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 00:47:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 00:47:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 00:48:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 00:48:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 00:48:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 00:49:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 00:49:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 00:49:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 00:50:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 00:50:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 00:50:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 00:51:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 00:51:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 00:51:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[5]毫秒
2025-08-07 00:52:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 00:52:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 00:52:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 00:53:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 00:53:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 00:53:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 00:54:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 00:54:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 00:54:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[4]毫秒
2025-08-07 00:55:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 00:55:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 00:55:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 00:56:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 00:56:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 00:56:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[4]毫秒
2025-08-07 00:57:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 00:57:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 00:57:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 00:58:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 00:58:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 00:58:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[4]毫秒
2025-08-07 00:59:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 00:59:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 00:59:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 01:00:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 01:00:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 01:00:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[4]毫秒
2025-08-07 01:01:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 01:01:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 01:01:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[8]毫秒
2025-08-07 01:02:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 01:02:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 01:02:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 01:03:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 01:03:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 01:03:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[5]毫秒
2025-08-07 01:04:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 01:04:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 01:04:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[4]毫秒
2025-08-07 01:05:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 01:05:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 01:05:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[4]毫秒
2025-08-07 01:06:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 01:06:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 01:06:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[4]毫秒
2025-08-07 01:07:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 01:07:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 01:07:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 01:08:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 01:08:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 01:08:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[5]毫秒
2025-08-07 01:09:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 01:09:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 01:09:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 01:10:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 01:10:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 01:10:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 01:11:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 01:11:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 01:11:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 01:12:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 01:12:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 01:12:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[4]毫秒
2025-08-07 01:13:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 01:13:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 01:13:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[5]毫秒
2025-08-07 01:14:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 01:14:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 01:14:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[5]毫秒
2025-08-07 01:15:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 01:15:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 01:15:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[5]毫秒
2025-08-07 01:16:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 01:16:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 01:16:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[4]毫秒
2025-08-07 01:17:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 01:17:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 01:17:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[4]毫秒
2025-08-07 01:18:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 01:18:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 01:18:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[5]毫秒
2025-08-07 01:19:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 01:19:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 01:19:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 01:20:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 01:20:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 01:20:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[5]毫秒
2025-08-07 01:21:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 01:21:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 01:21:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[5]毫秒
2025-08-07 01:22:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 01:22:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 01:22:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[5]毫秒
2025-08-07 01:23:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 01:23:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 01:23:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 01:24:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 01:24:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 01:24:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 01:25:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 01:25:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 01:25:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 01:26:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 01:26:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 01:26:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[5]毫秒
2025-08-07 01:27:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 01:27:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 01:27:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 01:28:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 01:28:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 01:28:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[4]毫秒
2025-08-07 01:29:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 01:29:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 01:29:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 01:30:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 01:30:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 01:30:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[5]毫秒
2025-08-07 01:31:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 01:31:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 01:31:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[5]毫秒
2025-08-07 01:32:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 01:32:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 01:32:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[4]毫秒
2025-08-07 01:33:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 01:33:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 01:33:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 01:34:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 01:34:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 01:34:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[4]毫秒
2025-08-07 01:35:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 01:35:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 01:35:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 01:36:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 01:36:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 01:36:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 01:37:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 01:37:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 01:37:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 01:38:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 01:38:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 01:38:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 01:39:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 01:39:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 01:39:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 01:40:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 01:40:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 01:40:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[4]毫秒
2025-08-07 01:41:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 01:41:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 01:41:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[5]毫秒
2025-08-07 01:42:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 01:42:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 01:42:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[5]毫秒
2025-08-07 01:43:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 01:43:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 01:43:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[5]毫秒
2025-08-07 01:44:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 01:44:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 01:44:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[4]毫秒
2025-08-07 01:45:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 01:45:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 01:45:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[4]毫秒
2025-08-07 01:46:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 01:46:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 01:46:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 01:47:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 01:47:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 01:47:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 01:48:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 01:48:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 01:48:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[4]毫秒
2025-08-07 01:49:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 01:49:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 01:49:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 01:50:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 01:50:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 01:50:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[4]毫秒
2025-08-07 01:51:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 01:51:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 01:51:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 01:52:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 01:52:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 01:52:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[4]毫秒
2025-08-07 01:53:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 01:53:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 01:53:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 01:54:56 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 01:54:56 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 01:54:56 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[55]毫秒
2025-08-07 01:55:56 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 01:55:56 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 01:55:56 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[32]毫秒
2025-08-07 01:56:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 01:56:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 01:56:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[12]毫秒
2025-08-07 01:57:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 01:57:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 01:57:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[11]毫秒
2025-08-07 01:58:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 01:58:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 01:58:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[19]毫秒
2025-08-07 01:59:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 01:59:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 01:59:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[13]毫秒
2025-08-07 02:00:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 02:00:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 02:00:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[9]毫秒
2025-08-07 02:01:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 02:01:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 02:01:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[17]毫秒
2025-08-07 02:02:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 02:02:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 02:02:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[13]毫秒
2025-08-07 02:03:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 02:03:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 02:03:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[14]毫秒
2025-08-07 02:04:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 02:04:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 02:04:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[11]毫秒
2025-08-07 02:05:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 02:05:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 02:05:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[9]毫秒
2025-08-07 02:06:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 02:06:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 02:06:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[10]毫秒
2025-08-07 02:07:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 02:07:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 02:07:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[12]毫秒
2025-08-07 02:08:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 02:08:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 02:08:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[10]毫秒
2025-08-07 02:09:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 02:09:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 02:09:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[21]毫秒
2025-08-07 02:10:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 02:10:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 02:10:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[17]毫秒
2025-08-07 02:11:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 02:11:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 02:11:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[11]毫秒
2025-08-07 02:12:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 02:12:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 02:12:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[8]毫秒
2025-08-07 02:13:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 02:13:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 02:13:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 02:14:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 02:14:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 02:14:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[12]毫秒
2025-08-07 02:15:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 02:15:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 02:15:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[9]毫秒
2025-08-07 02:16:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 02:16:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 02:16:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 02:17:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 02:17:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 02:17:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 02:18:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 02:18:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 02:18:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 02:19:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 02:19:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 02:19:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[12]毫秒
2025-08-07 02:20:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 02:20:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 02:20:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 02:21:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 02:21:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 02:21:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 02:22:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 02:22:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 02:22:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 02:23:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 02:23:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 02:23:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[9]毫秒
2025-08-07 02:24:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 02:24:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 02:24:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[9]毫秒
2025-08-07 02:25:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 02:25:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 02:25:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[12]毫秒
2025-08-07 02:26:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 02:26:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 02:26:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[12]毫秒
2025-08-07 02:27:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 02:27:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 02:27:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[8]毫秒
2025-08-07 02:28:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 02:28:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 02:28:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 02:29:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 02:29:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 02:29:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[9]毫秒
2025-08-07 02:30:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 02:30:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 02:30:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 02:31:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 02:31:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 02:31:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[8]毫秒
2025-08-07 02:32:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 02:32:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 02:32:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[12]毫秒
2025-08-07 02:33:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 02:33:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 02:33:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[8]毫秒
2025-08-07 02:34:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 02:34:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 02:34:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[8]毫秒
2025-08-07 02:35:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 02:35:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 02:35:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[8]毫秒
2025-08-07 02:36:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 02:36:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 02:36:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[9]毫秒
2025-08-07 02:37:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 02:37:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 02:37:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 02:38:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 02:38:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 02:38:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 02:39:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 02:39:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 02:39:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 02:40:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 02:40:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 02:40:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 02:41:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 02:41:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 02:41:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 02:42:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 02:42:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 02:42:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 02:43:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 02:43:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 02:43:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 02:44:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 02:44:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 02:44:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 02:45:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 02:45:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 02:45:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[9]毫秒
2025-08-07 02:46:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 02:46:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 02:46:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 02:47:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 02:47:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 02:47:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[8]毫秒
2025-08-07 02:48:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 02:48:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 02:48:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 02:49:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 02:49:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 02:49:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 02:50:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 02:50:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 02:50:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 02:51:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 02:51:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 02:51:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[12]毫秒
2025-08-07 02:52:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 02:52:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 02:52:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 02:53:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 02:53:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 02:53:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[8]毫秒
2025-08-07 02:54:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 02:54:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 02:54:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[9]毫秒
2025-08-07 02:55:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 02:55:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 02:55:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 02:56:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 02:56:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 02:56:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[13]毫秒
2025-08-07 02:57:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 02:57:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 02:57:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[28]毫秒
2025-08-07 02:58:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 02:58:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 02:58:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[9]毫秒
2025-08-07 02:59:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 02:59:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 02:59:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 03:00:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 03:00:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 03:00:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 03:01:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 03:01:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 03:01:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 03:02:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 03:02:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 03:02:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 03:03:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 03:03:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 03:03:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[9]毫秒
2025-08-07 03:04:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 03:04:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 03:04:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 03:05:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 03:05:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 03:05:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 03:06:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 03:06:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 03:06:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[12]毫秒
2025-08-07 03:07:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 03:07:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 03:07:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[8]毫秒
2025-08-07 03:08:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 03:08:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 03:08:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 03:09:58 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 03:09:58 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 03:09:58 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 03:10:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 03:10:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 03:10:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 03:11:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 03:11:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 03:11:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 03:12:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 03:12:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 03:12:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[8]毫秒
2025-08-07 03:13:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 03:13:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 03:13:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[8]毫秒
2025-08-07 03:14:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 03:14:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 03:14:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[9]毫秒
2025-08-07 03:15:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 03:15:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 03:15:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[8]毫秒
2025-08-07 03:16:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 03:16:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 03:16:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[8]毫秒
2025-08-07 03:17:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 03:17:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 03:17:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 03:18:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 03:18:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 03:18:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 03:19:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 03:19:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 03:19:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[8]毫秒
2025-08-07 03:20:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 03:20:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 03:20:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 03:21:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 03:21:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 03:21:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 03:22:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 03:22:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 03:22:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[15]毫秒
2025-08-07 03:23:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 03:23:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 03:23:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 03:24:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 03:24:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 03:24:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 03:25:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 03:25:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 03:25:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[9]毫秒
2025-08-07 03:26:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 03:26:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 03:26:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[9]毫秒
2025-08-07 03:27:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 03:27:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 03:27:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[8]毫秒
2025-08-07 03:28:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 03:28:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 03:28:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[9]毫秒
2025-08-07 03:29:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 03:29:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 03:29:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[8]毫秒
2025-08-07 03:30:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 03:30:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 03:30:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[8]毫秒
2025-08-07 03:31:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 03:31:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 03:31:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[9]毫秒
2025-08-07 03:32:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 03:32:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 03:32:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 03:33:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 03:33:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 03:33:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 03:34:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 03:34:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 03:34:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 03:35:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 03:35:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 03:35:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[9]毫秒
2025-08-07 03:36:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 03:36:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 03:36:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[8]毫秒
2025-08-07 03:37:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 03:37:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 03:37:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 03:38:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 03:38:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 03:38:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[5]毫秒
2025-08-07 03:39:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 03:39:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 03:39:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 03:40:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 03:40:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 03:40:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[5]毫秒
2025-08-07 03:41:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 03:41:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 03:41:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 03:42:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 03:42:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 03:42:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[5]毫秒
2025-08-07 03:43:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 03:43:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 03:43:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 03:44:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 03:44:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 03:44:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 03:45:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 03:45:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 03:45:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 03:46:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 03:46:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 03:46:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[9]毫秒
2025-08-07 03:47:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 03:47:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 03:47:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 03:48:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 03:48:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 03:48:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[10]毫秒
2025-08-07 03:49:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 03:49:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 03:49:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[10]毫秒
2025-08-07 03:50:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 03:50:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 03:50:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 03:51:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 03:51:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 03:51:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[8]毫秒
2025-08-07 03:52:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 03:52:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 03:52:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 03:53:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 03:53:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 03:53:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[8]毫秒
2025-08-07 03:54:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 03:54:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 03:54:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[9]毫秒
2025-08-07 03:55:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 03:55:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 03:55:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 03:56:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 03:56:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 03:56:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[8]毫秒
2025-08-07 03:57:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 03:57:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 03:57:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 03:58:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 03:58:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 03:58:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[8]毫秒
2025-08-07 03:59:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 03:59:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 03:59:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[8]毫秒
2025-08-07 04:00:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 04:00:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 04:00:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[12]毫秒
2025-08-07 04:01:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 04:01:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 04:01:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[14]毫秒
2025-08-07 04:02:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 04:02:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 04:02:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 04:03:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 04:03:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 04:03:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 04:04:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 04:04:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 04:04:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 04:05:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 04:05:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 04:05:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[8]毫秒
2025-08-07 04:06:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 04:06:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 04:06:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 04:07:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 04:07:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 04:07:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[8]毫秒
2025-08-07 04:08:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 04:08:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 04:08:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[8]毫秒
2025-08-07 04:09:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 04:09:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 04:09:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[8]毫秒
2025-08-07 04:10:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 04:10:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 04:10:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[8]毫秒
2025-08-07 04:11:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 04:11:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 04:11:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 04:12:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 04:12:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 04:12:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 04:13:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 04:13:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 04:13:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[8]毫秒
2025-08-07 04:14:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 04:14:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 04:14:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[5]毫秒
2025-08-07 04:15:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 04:15:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 04:15:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 04:16:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 04:16:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 04:16:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 04:17:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 04:17:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 04:17:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 04:18:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 04:18:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 04:18:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[5]毫秒
2025-08-07 04:19:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 04:19:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 04:19:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[8]毫秒
2025-08-07 04:20:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 04:20:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 04:20:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 04:21:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 04:21:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 04:21:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[12]毫秒
2025-08-07 04:22:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 04:22:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 04:22:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[9]毫秒
2025-08-07 04:23:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 04:23:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 04:23:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 04:24:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 04:24:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 04:24:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[8]毫秒
2025-08-07 04:25:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 04:25:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 04:25:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 04:26:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 04:26:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 04:26:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[8]毫秒
2025-08-07 04:27:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 04:27:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 04:27:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 04:28:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 04:28:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 04:28:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[9]毫秒
2025-08-07 04:29:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 04:29:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 04:29:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[8]毫秒
2025-08-07 04:30:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 04:30:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 04:30:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[8]毫秒
2025-08-07 04:31:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 04:31:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 04:31:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 04:32:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 04:32:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 04:32:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[8]毫秒
2025-08-07 04:33:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 04:33:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 04:33:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 04:34:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 04:34:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 04:34:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[9]毫秒
2025-08-07 04:35:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 04:35:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 04:35:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 04:36:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 04:36:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 04:36:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[8]毫秒
2025-08-07 04:37:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 04:37:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 04:37:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 04:38:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 04:38:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 04:38:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[9]毫秒
2025-08-07 04:39:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 04:39:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 04:39:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[8]毫秒
2025-08-07 04:40:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 04:40:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 04:40:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[8]毫秒
2025-08-07 04:41:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 04:41:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 04:41:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 04:42:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 04:42:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 04:42:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[8]毫秒
2025-08-07 04:43:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 04:43:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 04:43:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 04:44:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 04:44:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 04:44:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 04:45:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 04:45:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 04:45:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 04:46:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 04:46:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 04:46:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[8]毫秒
2025-08-07 04:47:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 04:47:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 04:47:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 04:48:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 04:48:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 04:48:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[5]毫秒
2025-08-07 04:49:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 04:49:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 04:49:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[8]毫秒
2025-08-07 04:50:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 04:50:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 04:50:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[5]毫秒
2025-08-07 04:51:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 04:51:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 04:51:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[8]毫秒
2025-08-07 04:52:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 04:52:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 04:52:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[10]毫秒
2025-08-07 04:53:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 04:53:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 04:53:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 04:54:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 04:54:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 04:54:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[10]毫秒
2025-08-07 04:55:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 04:55:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 04:55:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 04:56:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 04:56:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 04:56:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[10]毫秒
2025-08-07 04:57:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 04:57:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 04:57:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[10]毫秒
2025-08-07 04:58:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 04:58:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 04:58:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[8]毫秒
2025-08-07 04:59:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 04:59:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 04:59:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[9]毫秒
2025-08-07 05:00:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 05:00:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 05:00:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[9]毫秒
2025-08-07 05:01:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 05:01:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 05:01:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 05:02:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 05:02:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 05:02:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 05:03:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 05:03:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 05:03:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 05:04:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 05:04:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 05:04:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[8]毫秒
2025-08-07 05:05:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 05:05:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 05:05:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 05:06:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 05:06:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 05:06:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[5]毫秒
2025-08-07 05:07:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 05:07:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 05:07:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 05:08:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 05:08:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 05:08:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 05:09:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 05:09:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 05:09:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 05:10:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 05:10:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 05:10:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 05:11:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 05:11:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 05:11:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 05:12:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 05:12:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 05:12:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 05:13:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 05:13:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 05:13:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 05:14:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 05:14:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 05:14:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[5]毫秒
2025-08-07 05:15:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 05:15:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 05:15:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 05:16:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 05:16:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 05:16:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 05:17:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 05:17:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 05:17:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[5]毫秒
2025-08-07 05:18:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 05:18:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 05:18:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 05:19:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 05:19:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 05:19:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[21]毫秒
2025-08-07 05:20:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 05:20:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 05:20:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 05:21:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 05:21:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 05:21:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[8]毫秒
2025-08-07 05:22:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 05:22:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 05:22:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 05:23:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 05:23:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 05:23:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[10]毫秒
2025-08-07 05:24:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 05:24:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 05:24:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 05:25:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 05:25:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 05:25:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 05:26:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 05:26:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 05:26:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 05:27:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 05:27:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 05:27:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 05:28:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 05:28:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 05:28:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 05:29:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 05:29:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 05:29:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[9]毫秒
2025-08-07 05:30:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 05:30:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 05:30:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 05:31:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 05:31:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 05:31:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 05:32:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 05:32:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 05:32:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 05:33:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 05:33:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 05:33:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 05:34:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 05:34:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 05:34:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 05:35:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 05:35:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 05:35:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 05:36:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 05:36:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 05:36:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[8]毫秒
2025-08-07 05:37:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 05:37:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 05:37:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[8]毫秒
2025-08-07 05:38:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 05:38:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 05:38:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[10]毫秒
2025-08-07 05:39:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 05:39:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 05:39:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 05:40:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 05:40:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 05:40:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[8]毫秒
2025-08-07 05:41:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 05:41:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 05:41:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 05:42:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 05:42:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 05:42:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 05:43:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 05:43:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 05:43:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 05:44:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 05:44:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 05:44:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 05:45:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 05:45:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 05:45:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 05:46:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 05:46:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 05:46:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[8]毫秒
2025-08-07 05:47:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 05:47:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 05:47:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 05:48:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 05:48:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 05:48:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 05:49:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 05:49:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 05:49:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[8]毫秒
2025-08-07 05:50:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 05:50:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 05:50:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[9]毫秒
2025-08-07 05:51:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 05:51:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 05:51:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[11]毫秒
2025-08-07 05:52:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 05:52:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 05:52:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 05:53:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 05:53:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 05:53:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 05:54:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 05:54:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 05:54:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[5]毫秒
2025-08-07 05:55:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 05:55:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 05:55:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 05:56:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 05:56:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 05:56:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[5]毫秒
2025-08-07 05:57:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 05:57:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 05:57:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 05:58:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 05:58:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 05:58:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 05:59:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 05:59:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 05:59:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 06:00:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 06:00:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 06:00:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 06:01:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 06:01:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 06:01:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[9]毫秒
2025-08-07 06:02:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 06:02:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 06:02:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 06:03:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 06:03:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 06:03:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 06:04:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 06:04:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 06:04:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 06:05:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 06:05:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 06:05:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 06:06:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 06:06:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 06:06:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 06:07:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 06:07:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 06:07:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[8]毫秒
2025-08-07 06:08:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 06:08:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 06:08:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 06:09:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 06:09:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 06:09:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[11]毫秒
2025-08-07 06:10:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 06:10:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 06:10:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 06:11:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 06:11:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 06:11:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[10]毫秒
2025-08-07 06:12:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 06:12:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 06:12:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[5]毫秒
2025-08-07 06:13:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 06:13:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 06:13:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 06:14:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 06:14:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 06:14:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[5]毫秒
2025-08-07 06:15:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 06:15:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 06:15:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[8]毫秒
2025-08-07 06:16:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 06:16:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 06:16:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 06:17:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 06:17:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 06:17:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 06:18:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 06:18:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 06:18:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 06:19:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 06:19:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 06:19:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 06:20:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 06:20:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 06:20:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 06:21:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 06:21:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 06:21:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[10]毫秒
2025-08-07 06:22:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 06:22:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 06:22:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[5]毫秒
2025-08-07 06:23:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 06:23:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 06:23:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[8]毫秒
2025-08-07 06:24:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 06:24:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 06:24:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[5]毫秒
2025-08-07 06:25:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 06:25:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 06:25:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[10]毫秒
2025-08-07 06:26:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 06:26:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 06:26:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[8]毫秒
2025-08-07 06:27:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 06:27:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 06:27:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[8]毫秒
2025-08-07 06:28:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 06:28:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 06:28:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[5]毫秒
2025-08-07 06:29:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 06:29:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 06:29:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[8]毫秒
2025-08-07 06:30:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 06:30:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 06:30:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[5]毫秒
2025-08-07 06:31:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 06:31:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 06:31:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 06:32:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 06:32:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 06:32:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 06:33:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 06:33:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 06:33:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 06:34:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 06:34:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 06:34:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[5]毫秒
2025-08-07 06:35:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 06:35:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 06:35:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 06:36:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 06:36:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 06:36:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 06:37:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 06:37:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 06:37:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 06:38:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 06:38:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 06:38:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 06:39:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 06:39:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 06:39:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[8]毫秒
2025-08-07 06:40:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 06:40:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 06:40:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[8]毫秒
2025-08-07 06:41:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 06:41:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 06:41:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[8]毫秒
2025-08-07 06:42:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 06:42:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 06:42:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[8]毫秒
2025-08-07 06:43:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 06:43:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 06:43:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[8]毫秒
2025-08-07 06:44:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 06:44:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 06:44:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 06:45:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 06:45:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 06:45:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 06:46:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 06:46:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 06:46:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 06:47:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 06:47:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 06:47:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 06:48:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 06:48:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 06:48:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 06:49:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 06:49:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 06:49:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[8]毫秒
2025-08-07 06:50:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 06:50:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 06:50:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[8]毫秒
2025-08-07 06:51:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 06:51:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 06:51:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 06:52:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 06:52:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 06:52:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 06:53:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 06:53:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 06:53:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 06:54:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 06:54:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 06:54:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 06:55:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 06:55:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 06:55:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[8]毫秒
2025-08-07 06:56:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 06:56:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 06:56:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 06:57:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 06:57:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 06:57:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 06:58:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 06:58:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 06:58:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[8]毫秒
2025-08-07 06:59:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 06:59:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 06:59:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 07:00:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 07:00:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 07:00:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[8]毫秒
2025-08-07 07:01:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 07:01:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 07:01:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 07:02:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 07:02:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 07:02:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 07:03:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 07:03:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 07:03:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 07:04:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 07:04:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 07:04:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[8]毫秒
2025-08-07 07:05:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 07:05:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 07:05:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 07:06:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 07:06:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 07:06:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 07:07:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 07:07:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 07:07:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[9]毫秒
2025-08-07 07:08:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 07:08:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 07:08:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 07:09:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 07:09:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 07:09:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 07:10:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 07:10:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 07:10:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[9]毫秒
2025-08-07 07:11:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 07:11:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 07:11:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 07:12:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 07:12:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 07:12:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 07:13:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 07:13:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 07:13:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 07:14:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 07:14:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 07:14:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[8]毫秒
2025-08-07 07:15:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 07:15:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 07:15:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[9]毫秒
2025-08-07 07:16:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 07:16:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 07:16:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 07:17:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 07:17:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 07:17:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[8]毫秒
2025-08-07 07:18:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 07:18:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 07:18:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 07:19:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 07:19:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 07:19:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 07:20:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 07:20:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 07:20:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 07:21:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 07:21:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 07:21:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 07:22:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 07:22:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 07:22:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 07:23:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 07:23:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 07:23:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 07:24:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 07:24:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 07:24:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[5]毫秒
2025-08-07 07:25:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 07:25:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 07:25:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 07:26:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 07:26:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 07:26:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 07:27:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 07:27:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 07:27:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 07:28:21 [snail-job-grpc-server-executor-5] INFO  c.a.s.c.c.rpc.server.GrpcInterceptor - method invoked: UnaryRequest/unaryRequest cast:1ms
2025-08-07 07:28:21 [snail-grpc-server-3] INFO  c.a.s.c.job.core.client.JobEndPoint -  Task scheduler:[job] Task ID:[473] Task batch:[775] Workflow batch:[null] Task scheduled successfully.
2025-08-07 07:28:21 [snail-job-job-775-1] INFO  o.d.l.s.EbbinghausReviewExecutor - 艾宾浩斯复习任务开始执行. jobArgs:{"jobParams":"{\"reviewLevel\":\"3\",\"taskType\":\"EBBINGHAUS_REVIEW\",\"learnedWordId\":\"1950485429846499328\",\"wordId\":\"1934584922284118017\",\"unitId\":\"1934573476238311426\",\"userId\":\"1\"}","executorInfo":"ebbinghausReviewExecutor","taskBatchId":775,"jobId":473,"wfContext":{},"changeWfContext":{}}
2025-08-07 07:28:21 [snail-job-job-775-1] INFO  o.d.l.s.EbbinghausReviewExecutor - 艾宾浩斯复习任务开始执行. jobArgs:{"jobParams":"{\"reviewLevel\":\"3\",\"taskType\":\"EBBINGHAUS_REVIEW\",\"learnedWordId\":\"1950485429846499328\",\"wordId\":\"1934584922284118017\",\"unitId\":\"1934573476238311426\",\"userId\":\"1\"}","executorInfo":"ebbinghausReviewExecutor","taskBatchId":775,"jobId":473,"wfContext":{},"changeWfContext":{}}
2025-08-07 07:28:21 [snail-job-job-775-1] INFO  o.d.l.s.EbbinghausReviewExecutor - 处理艾宾浩斯复习任务：用户1，单词1934584922284118017，单元1934573476238311426，等级3，学习记录ID1950485429846499328
2025-08-07 07:28:21 [snail-job-job-775-1] INFO  o.d.l.s.EbbinghausReviewExecutor - 处理艾宾浩斯复习任务：用户1，单词1934584922284118017，单元1934573476238311426，等级3
2025-08-07 07:28:21 [snail-job-job-775-1] INFO  o.d.l.s.i.LoveUserLearnedUnitsServiceImpl - 更新用户1单元1934573476238311426学习进度：已学单词数1/24, 进度4%, 状态1(0-未开始,1-学习中,2-复习中,3-已掌握)
2025-08-07 07:28:21 [snail-job-job-775-1] INFO  o.d.l.s.EbbinghausReviewExecutor - 艾宾浩斯复习任务执行成功：用户1，单元1934573476238311426
2025-08-07 07:28:21 [snail-job-job-775-1] INFO  o.d.l.s.EbbinghausReviewExecutor - 艾宾浩斯复习任务执行成功：用户1，单元1934573476238311426
2025-08-07 07:28:21 [snail-job-job-775-1] INFO  c.a.s.c.j.c.e.JobExecutorFutureCallback - Task executed successfully taskBatchId:[775] [{"status":1,"result":"艾宾浩斯复习任务执行成功：用户1，单元1934573476238311426","message":"Task executed successfully"}]
2025-08-07 07:28:26 [snail-job-grpc-client-executor-127.0.0.1-15627] INFO  c.a.s.c.c.l.report.ReportLogListener - Data report log successfully requestId:[15628]
2025-08-07 07:28:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 07:28:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 07:28:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 07:29:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 07:29:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 07:29:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 07:30:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 07:30:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 07:30:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[9]毫秒
2025-08-07 07:31:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 07:31:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 07:31:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 07:32:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 07:32:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 07:32:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 07:33:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 07:33:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 07:33:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 07:34:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 07:34:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 07:34:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[8]毫秒
2025-08-07 07:35:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 07:35:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 07:35:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 07:36:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 07:36:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 07:36:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 07:37:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 07:37:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 07:37:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[15]毫秒
2025-08-07 07:38:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 07:38:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 07:38:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[8]毫秒
2025-08-07 07:39:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 07:39:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 07:39:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 07:40:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 07:40:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 07:40:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 07:41:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 07:41:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 07:41:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 07:42:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 07:42:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 07:42:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 07:43:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 07:43:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 07:43:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 07:44:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 07:44:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 07:44:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[8]毫秒
2025-08-07 07:45:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 07:45:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 07:45:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 07:46:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 07:46:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 07:46:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 07:47:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 07:47:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 07:47:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 07:48:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 07:48:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 07:48:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 07:49:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 07:49:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 07:49:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 07:50:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 07:50:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 07:50:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[8]毫秒
2025-08-07 07:51:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 07:51:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 07:51:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 07:52:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 07:52:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 07:52:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 07:53:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 07:53:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 07:53:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 07:54:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 07:54:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 07:54:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 07:55:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 07:55:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 07:55:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 07:56:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 07:56:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 07:56:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 07:57:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 07:57:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 07:57:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 07:58:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 07:58:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 07:58:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[8]毫秒
2025-08-07 07:59:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 07:59:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 07:59:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 08:00:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 08:00:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 08:00:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[9]毫秒
2025-08-07 08:01:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 08:01:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 08:01:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 08:02:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 08:02:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 08:02:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 08:03:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 08:03:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 08:03:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 08:04:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 08:04:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 08:04:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 08:05:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 08:05:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 08:05:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 08:06:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 08:06:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 08:06:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[8]毫秒
2025-08-07 08:07:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 08:07:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 08:07:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 08:08:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 08:08:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 08:08:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[10]毫秒
2025-08-07 08:09:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 08:09:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 08:09:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 08:10:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 08:10:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 08:10:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[8]毫秒
2025-08-07 08:11:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 08:11:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 08:11:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 08:12:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 08:12:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 08:12:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 08:13:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 08:13:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 08:13:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 08:14:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 08:14:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 08:14:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 08:15:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 08:15:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 08:15:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[9]毫秒
2025-08-07 08:16:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 08:16:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 08:16:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[15]毫秒
2025-08-07 08:17:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 08:17:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 08:17:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[38]毫秒
2025-08-07 08:18:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 08:18:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 08:18:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[10]毫秒
2025-08-07 08:19:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 08:19:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 08:19:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 08:20:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 08:20:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 08:20:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[9]毫秒
2025-08-07 08:21:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 08:21:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 08:21:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 08:22:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 08:22:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 08:22:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 08:23:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 08:23:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 08:23:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 08:24:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 08:24:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 08:24:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 08:25:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 08:25:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 08:25:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 08:26:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 08:26:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 08:26:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 08:27:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 08:27:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 08:27:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 08:28:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 08:28:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 08:28:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[5]毫秒
2025-08-07 08:29:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 08:29:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 08:29:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 08:30:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 08:30:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 08:30:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 08:31:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 08:31:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 08:31:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 08:32:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 08:32:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 08:32:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 08:33:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 08:33:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 08:33:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 08:34:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 08:34:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 08:34:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[5]毫秒
2025-08-07 08:35:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 08:35:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 08:35:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 08:36:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 08:36:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 08:36:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 08:37:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 08:37:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 08:37:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 08:38:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 08:38:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 08:38:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[8]毫秒
2025-08-07 08:39:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 08:39:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 08:39:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[5]毫秒
2025-08-07 08:40:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 08:40:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 08:40:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[5]毫秒
2025-08-07 08:41:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 08:41:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 08:41:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 08:42:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 08:42:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 08:42:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 08:43:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 08:43:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 08:43:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[5]毫秒
2025-08-07 08:44:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 08:44:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 08:44:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[5]毫秒
2025-08-07 08:45:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 08:45:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 08:45:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 08:46:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 08:46:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 08:46:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 08:47:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 08:47:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 08:47:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[5]毫秒
2025-08-07 08:48:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 08:48:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 08:48:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[5]毫秒
2025-08-07 08:49:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 08:49:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 08:49:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[5]毫秒
2025-08-07 08:50:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 08:50:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 08:50:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[5]毫秒
2025-08-07 08:51:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 08:51:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 08:51:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[5]毫秒
2025-08-07 08:52:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 08:52:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 08:52:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 08:53:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 08:53:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 08:53:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 08:54:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 08:54:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 08:54:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 08:55:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 08:55:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 08:55:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 08:56:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 08:56:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 08:56:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 08:57:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 08:57:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 08:57:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 08:58:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 08:58:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 08:58:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[8]毫秒
2025-08-07 08:59:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 08:59:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 08:59:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 09:00:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 09:00:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 09:00:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[5]毫秒
2025-08-07 09:01:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 09:01:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 09:01:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 09:02:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 09:02:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 09:02:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[5]毫秒
2025-08-07 09:03:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 09:03:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 09:03:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 09:04:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 09:04:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 09:04:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[5]毫秒
2025-08-07 09:05:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 09:05:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 09:05:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[5]毫秒
2025-08-07 09:06:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 09:06:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 09:06:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[5]毫秒
2025-08-07 09:07:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 09:07:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 09:07:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 09:08:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 09:08:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 09:08:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[5]毫秒
2025-08-07 09:09:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 09:09:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 09:09:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[5]毫秒
2025-08-07 09:10:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 09:10:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 09:10:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[5]毫秒
2025-08-07 09:11:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 09:11:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 09:11:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 09:12:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 09:12:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 09:12:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 09:13:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 09:13:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 09:13:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 09:14:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 09:14:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 09:14:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 09:15:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 09:15:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 09:15:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 09:16:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 09:16:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 09:16:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[5]毫秒
2025-08-07 09:17:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 09:17:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 09:17:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[5]毫秒
2025-08-07 09:18:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 09:18:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 09:18:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[5]毫秒
2025-08-07 09:19:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 09:19:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 09:19:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[5]毫秒
2025-08-07 09:20:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 09:20:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 09:20:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[4]毫秒
2025-08-07 09:21:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 09:21:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 09:21:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 09:22:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 09:22:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 09:22:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[5]毫秒
2025-08-07 09:23:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 09:23:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 09:23:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 09:24:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 09:24:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 09:24:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[5]毫秒
2025-08-07 09:25:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 09:25:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 09:25:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 09:26:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 09:26:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 09:26:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[4]毫秒
2025-08-07 09:27:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 09:27:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 09:27:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 09:28:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 09:28:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 09:28:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[4]毫秒
2025-08-07 09:29:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 09:29:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 09:29:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[5]毫秒
2025-08-07 09:30:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 09:30:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 09:30:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[4]毫秒
2025-08-07 09:31:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 09:31:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 09:31:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[5]毫秒
2025-08-07 09:32:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 09:32:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 09:32:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 09:33:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 09:33:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 09:33:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[5]毫秒
2025-08-07 09:34:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 09:34:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 09:34:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[5]毫秒
2025-08-07 09:35:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 09:35:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 09:35:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 09:36:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 09:36:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 09:36:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 09:37:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 09:37:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 09:37:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[5]毫秒
2025-08-07 09:38:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 09:38:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 09:38:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 09:39:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 09:39:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 09:39:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 09:40:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 09:40:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 09:40:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 09:41:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 09:41:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 09:41:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[5]毫秒
2025-08-07 09:42:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 09:42:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 09:42:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[5]毫秒
2025-08-07 09:43:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 09:43:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 09:43:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[5]毫秒
2025-08-07 09:44:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 09:44:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 09:44:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[5]毫秒
2025-08-07 09:45:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 09:45:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 09:45:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 09:46:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 09:46:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 09:46:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 09:47:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 09:47:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 09:47:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[5]毫秒
2025-08-07 09:48:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 09:48:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 09:48:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 09:49:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 09:49:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 09:49:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 09:50:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 09:50:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 09:50:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 09:51:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 09:51:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 09:51:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[5]毫秒
2025-08-07 09:52:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 09:52:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 09:52:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 09:53:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 09:53:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 09:53:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[5]毫秒
2025-08-07 09:54:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 09:54:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 09:54:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 09:55:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 09:55:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 09:55:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 09:56:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 09:56:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 09:56:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 09:57:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 09:57:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 09:57:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 09:58:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 09:58:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 09:58:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 09:59:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 09:59:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 09:59:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 10:00:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 10:00:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 10:00:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[5]毫秒
2025-08-07 10:01:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 10:01:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 10:01:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[5]毫秒
2025-08-07 10:02:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 10:02:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 10:02:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[5]毫秒
2025-08-07 10:03:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 10:03:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 10:03:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 10:04:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 10:04:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 10:04:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 10:05:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 10:05:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 10:05:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[5]毫秒
2025-08-07 10:06:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 10:06:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 10:06:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 10:07:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 10:07:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 10:07:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 10:08:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 10:08:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 10:08:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 10:09:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 10:09:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 10:09:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 10:10:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 10:10:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 10:10:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 10:11:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 10:11:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 10:11:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[5]毫秒
2025-08-07 10:12:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 10:12:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 10:12:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 10:13:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 10:13:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 10:13:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 10:14:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 10:14:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 10:14:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[8]毫秒
2025-08-07 10:15:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 10:15:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 10:15:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 10:16:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 10:16:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 10:16:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 10:17:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 10:17:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 10:17:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 10:18:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 10:18:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 10:18:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[5]毫秒
2025-08-07 10:19:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 10:19:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 10:19:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[6]毫秒
2025-08-07 10:20:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 10:20:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 10:20:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[9]毫秒
2025-08-07 10:21:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 10:21:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 10:21:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[5]毫秒
2025-08-07 10:22:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 10:22:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 10:22:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[62]毫秒
2025-08-07 10:23:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 10:23:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 10:23:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[5]毫秒
2025-08-07 10:24:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 10:24:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 10:24:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[25]毫秒
2025-08-07 10:25:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /love/userDailyStudyDuration/heartbeat],无参数
2025-08-07 10:25:55 [XNIO-1 task-2] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 1, 剩余令牌 => 0, 缓存key => 'global:rate_limit:/love/userDailyStudyDuration/heartbeat:1'
2025-08-07 10:25:55 [XNIO-1 task-2] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /love/userDailyStudyDuration/heartbeat],耗时:[7]毫秒
2025-08-07 10:26:21 [Thread-18] INFO  c.a.s.c.c.window.SlidingRingWindow - JVM is about to exit, emitting data in the Window
2025-08-07 10:26:21 [SpringApplicationShutdownHook] INFO  c.a.s.c.c.init.SnailJobCloseListener - snail-job client about to shutdown v1.6.0-beta1
