2025-08-04 00:09:09 [instance-timeout-check-thread] INFO  c.a.s.s.c.handler.InstanceManager - Node 192.168.1.9:28080 is offline. Removing...
2025-08-04 02:17:30 [instance-timeout-check-thread] INFO  c.a.s.s.c.handler.InstanceManager - Node 192.168.1.9:28080 is offline. Removing...
2025-08-04 02:27:10 [instance-timeout-check-thread] INFO  c.a.s.s.c.handler.InstanceManager - Node 192.168.1.9:28080 is offline. Removing...
2025-08-04 03:00:30 [instance-timeout-check-thread] INFO  c.a.s.s.c.handler.InstanceManager - Node 192.168.1.9:28080 is offline. Removing...
2025-08-04 21:58:02 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-08-04 21:58:03 [main] INFO  o.d.s.SnailJobServerApplication - Starting SnailJobServerApplication using Java 17.0.15 with PID 26108 (D:\MyApplication\love-word\aiword-web-server\ruoyi-extend\ruoyi-snailjob-server\target\classes started by Administrator in D:\MyApplication\love-word\aiword-web-server)
2025-08-04 21:58:03 [main] INFO  o.d.s.SnailJobServerApplication - The following 1 profile is active: "dev"
2025-08-04 21:58:07 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8800 (http)
2025-08-04 21:58:07 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8800"]
2025-08-04 21:58:07 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-04 21:58:07 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.39]
2025-08-04 21:58:07 [main] INFO  o.a.c.c.C.[.[localhost].[/snail-job] - Initializing Spring embedded WebApplicationContext
2025-08-04 21:58:07 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4115 ms
2025-08-04 21:58:14 [main] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [admin/index.html]
2025-08-04 21:58:15 [COMMON_ACTOR_SYSTEM-pekko.actor.default-dispatcher-5] INFO  o.a.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-08-04 21:58:15 [NETTY_ACTOR_SYSTEM-pekko.actor.default-dispatcher-5] INFO  o.a.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-08-04 21:58:15 [RETRY_ACTOR_SYSTEM-pekko.actor.default-dispatcher-5] INFO  o.a.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-08-04 21:58:15 [JOB_ACTOR_SYSTEM-pekko.actor.default-dispatcher-5] INFO  o.a.pekko.event.slf4j.Slf4jLogger - Slf4jLogger started
2025-08-04 21:58:15 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 15 endpoints beneath base path '/actuator'
2025-08-04 21:58:16 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8800"]
2025-08-04 21:58:16 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8800 (http) with context path '/snail-job'
2025-08-04 21:58:16 [main] INFO  c.a.s.s.s.listener.StartListener - snail-job server is preparing to start... v1.6.0-beta1
2025-08-04 21:58:16 [main] INFO  c.a.s.s.common.cache.CacheLockRecord - CacheLockRecord start
2025-08-04 21:58:16 [main] INFO  c.a.s.s.c.rpc.server.grpc.GrpcServer - ------> snail-job remoting server start success, grpc = com.aizuda.snailjob.server.common.rpc.server.grpc.GrpcServer, port = 17888
2025-08-04 21:58:16 [main] INFO  c.a.s.s.c.cache.CacheConsumerGroup - CacheRegisterTable start
2025-08-04 21:58:16 [main] INFO  c.a.s.s.c.cache.CacheGroupScanActor - CacheGroupScanActor start
2025-08-04 21:58:16 [main] INFO  c.a.s.s.c.c.CacheNotifyRateLimiter - CacheNotifyRateLimiter start
2025-08-04 21:58:16 [main] INFO  c.a.s.server.common.cache.CacheToken - CacheToken start
2025-08-04 21:58:16 [main] INFO  c.a.s.s.c.handler.ServerNodeBalance - ServerNodeBalance start
2025-08-04 21:58:16 [main] INFO  c.a.s.s.c.register.ServerRegister - ServerRegister start
2025-08-04 21:58:16 [main] INFO  c.a.s.s.j.t.s.a.l.JobTaskFailAlarmListener - JobTaskFailAlarmListener started
2025-08-04 21:58:16 [main] INFO  c.a.s.s.j.t.s.a.l.WorkflowTaskFailAlarmListener - WorkflowTaskFailAlarmListener started
2025-08-04 21:58:16 [main] INFO  c.a.s.s.r.t.s.c.CacheGroupRateLimiter - CacheGroupRateLimiter start
2025-08-04 21:58:16 [main] INFO  c.a.s.s.r.t.s.l.RetryTaskFailAlarmListener - RetryTaskFailAlarmListener started
2025-08-04 21:58:16 [main] INFO  c.a.s.s.r.t.s.l.RetryTaskFailDeadLetterAlarmListener - RetryTaskFailDeadLetterAlarmListener started
2025-08-04 21:58:16 [main] INFO  c.a.s.s.s.listener.StartListener - snail-job server started successfully v1.6.0-beta1
2025-08-04 21:58:16 [main] INFO  o.d.s.SnailJobServerApplication - Started SnailJobServerApplication in 14.41 seconds (process running for 21.213)
2025-08-04 21:58:16 [snail-job-scheduled-thread-3] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-08-04 21:58:23 [snail-job-scheduled-thread-3] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@232b7768
2025-08-04 21:58:23 [snail-job-scheduled-thread-3] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-08-04 21:58:26 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - rebalance start
2025-08-04 21:58:26 [server-node-balance] INFO  c.a.s.s.c.handler.ServerNodeBalance - rebalance complete. allocate:[[0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127]]
