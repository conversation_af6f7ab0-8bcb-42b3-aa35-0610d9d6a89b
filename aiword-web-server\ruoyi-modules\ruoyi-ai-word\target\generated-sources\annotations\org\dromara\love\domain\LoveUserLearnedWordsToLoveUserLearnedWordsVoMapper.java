package org.dromara.love.domain;

import io.github.linpeilie.AutoMapperConfig__787;
import io.github.linpeilie.BaseMapper;
import org.dromara.love.domain.bo.LoveUserLearnedWordsBoToLoveUserLearnedWordsMapper;
import org.dromara.love.domain.vo.LoveUserLearnedWordsVo;
import org.dromara.love.domain.vo.LoveUserLearnedWordsVoToLoveUserLearnedWordsMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__787.class,
    uses = {LoveUserLearnedWordsVoToLoveUserLearnedWordsMapper.class,LoveUserLearnedWordsBoToLoveUserLearnedWordsMapper.class},
    imports = {}
)
public interface LoveUserLearnedWordsToLoveUserLearnedWordsVoMapper extends BaseMapper<LoveUserLearnedWords, LoveUserLearnedWordsVo> {
}
