{"doc": " 用户信息\n\n <AUTHOR> Li\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.system.domain.bo.SysUserBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 获取用户列表\n"}, {"name": "studentList", "paramTypes": ["org.dromara.system.domain.bo.SysUserBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 获取学生列表\n"}, {"name": "studentExperienceList", "paramTypes": ["org.dromara.system.domain.bo.SysUserBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 获取体验学生列表（流程同上，role_key = user_student_experience）\n"}, {"name": "export", "paramTypes": ["org.dromara.system.domain.bo.SysUserBo", "jakarta.servlet.http.HttpServletResponse"], "doc": " 导出用户列表\n"}, {"name": "importData", "paramTypes": ["org.springframework.web.multipart.MultipartFile", "boolean"], "doc": " 导入数据\n\n @param file          导入文件\n @param updateSupport 是否更新已存在数据\n"}, {"name": "importTemplate", "paramTypes": ["jakarta.servlet.http.HttpServletResponse"], "doc": " 获取导入模板\n"}, {"name": "getInfo", "paramTypes": [], "doc": " 获取用户信息\n\n @return 用户信息\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": " 根据用户编号获取详细信息\n\n @param userId 用户ID\n"}, {"name": "add", "paramTypes": ["org.dromara.system.domain.bo.SysUserBo"], "doc": " 新增用户\n"}, {"name": "edit", "paramTypes": ["org.dromara.system.domain.bo.SysUserBo"], "doc": " 修改用户\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": " 删除用户\n\n @param userIds 角色ID串\n"}, {"name": "optionselect", "paramTypes": ["java.lang.Long[]", "java.lang.Long"], "doc": " 根据用户ID串批量获取用户基础信息\n\n @param userIds 用户ID串\n @param deptId  部门ID\n"}, {"name": "resetPwd", "paramTypes": ["org.dromara.system.domain.bo.SysUserBo"], "doc": " 重置密码\n"}, {"name": "changeStatus", "paramTypes": ["org.dromara.system.domain.bo.SysUserBo"], "doc": " 状态修改\n"}, {"name": "authRole", "paramTypes": ["java.lang.Long"], "doc": " 根据用户编号获取授权角色\n\n @param userId 用户ID\n"}, {"name": "insertAuthRole", "paramTypes": ["java.lang.Long", "java.lang.Long[]"], "doc": " 用户授权角色\n\n @param userId  用户Id\n @param roleIds 角色ID串\n"}, {"name": "deptTree", "paramTypes": ["org.dromara.system.domain.bo.SysDeptBo"], "doc": " 获取部门树列表\n"}, {"name": "listByDept", "paramTypes": ["java.lang.Long"], "doc": " 获取部门下的所有用户信息\n"}], "constructors": []}