package org.dromara.love.domain.vo;

import javax.annotation.processing.Generated;
import org.dromara.love.domain.LoveUserLearnedUnits;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-07T01:55:47+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (Microsoft)"
)
@Component
public class LoveUserLearnedUnitsVoToLoveUserLearnedUnitsMapperImpl implements LoveUserLearnedUnitsVoToLoveUserLearnedUnitsMapper {

    @Override
    public LoveUserLearnedUnits convert(LoveUserLearnedUnitsVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        LoveUserLearnedUnits loveUserLearnedUnits = new LoveUserLearnedUnits();

        loveUserLearnedUnits.setId( arg0.getId() );
        loveUserLearnedUnits.setUserId( arg0.getUserId() );
        loveUserLearnedUnits.setBookId( arg0.getBookId() );
        loveUserLearnedUnits.setUnitId( arg0.getUnitId() );
        loveUserLearnedUnits.setUnitName( arg0.getUnitName() );
        loveUserLearnedUnits.setUnitNumber( arg0.getUnitNumber() );
        loveUserLearnedUnits.setTotalWords( arg0.getTotalWords() );
        loveUserLearnedUnits.setLearnedWords( arg0.getLearnedWords() );
        loveUserLearnedUnits.setProgressPercent( arg0.getProgressPercent() );
        loveUserLearnedUnits.setStatus( arg0.getStatus() );
        loveUserLearnedUnits.setFirstLearnTime( arg0.getFirstLearnTime() );
        loveUserLearnedUnits.setLastLearnTime( arg0.getLastLearnTime() );
        loveUserLearnedUnits.setLearnDuration( arg0.getLearnDuration() );
        loveUserLearnedUnits.setReviewCount( arg0.getReviewCount() );

        return loveUserLearnedUnits;
    }

    @Override
    public LoveUserLearnedUnits convert(LoveUserLearnedUnitsVo arg0, LoveUserLearnedUnits arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setBookId( arg0.getBookId() );
        arg1.setUnitId( arg0.getUnitId() );
        arg1.setUnitName( arg0.getUnitName() );
        arg1.setUnitNumber( arg0.getUnitNumber() );
        arg1.setTotalWords( arg0.getTotalWords() );
        arg1.setLearnedWords( arg0.getLearnedWords() );
        arg1.setProgressPercent( arg0.getProgressPercent() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setFirstLearnTime( arg0.getFirstLearnTime() );
        arg1.setLastLearnTime( arg0.getLastLearnTime() );
        arg1.setLearnDuration( arg0.getLearnDuration() );
        arg1.setReviewCount( arg0.getReviewCount() );

        return arg1;
    }
}
