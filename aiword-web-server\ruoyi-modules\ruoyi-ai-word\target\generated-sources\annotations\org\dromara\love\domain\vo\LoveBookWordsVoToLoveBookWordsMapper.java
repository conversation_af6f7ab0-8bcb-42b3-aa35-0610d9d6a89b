package org.dromara.love.domain.vo;

import io.github.linpeilie.AutoMapperConfig__787;
import io.github.linpeilie.BaseMapper;
import org.dromara.love.domain.LoveBookWords;
import org.dromara.love.domain.LoveBookWordsToLoveBookWordsVoMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__787.class,
    uses = {LoveBookWordsToLoveBookWordsVoMapper.class},
    imports = {}
)
public interface LoveBookWordsVoToLoveBookWordsMapper extends BaseMapper<LoveBookWordsVo, LoveBookWords> {
}
