package org.dromara.love.domain.vo;

import io.github.linpeilie.AutoMapperConfig__787;
import io.github.linpeilie.BaseMapper;
import org.dromara.love.domain.LoveUserSetBook;
import org.dromara.love.domain.LoveUserSetBookToLoveUserSetBookVoMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__787.class,
    uses = {LoveUserSetBookToLoveUserSetBookVoMapper.class},
    imports = {}
)
public interface LoveUserSetBookVoToLoveUserSetBookMapper extends BaseMapper<LoveUserSetBookVo, LoveUserSetBook> {
}
