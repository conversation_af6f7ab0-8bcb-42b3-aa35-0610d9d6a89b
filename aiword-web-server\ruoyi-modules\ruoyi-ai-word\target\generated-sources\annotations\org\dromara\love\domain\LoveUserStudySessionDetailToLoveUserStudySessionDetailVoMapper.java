package org.dromara.love.domain;

import io.github.linpeilie.AutoMapperConfig__787;
import io.github.linpeilie.BaseMapper;
import org.dromara.love.domain.bo.LoveUserStudySessionDetailBoToLoveUserStudySessionDetailMapper;
import org.dromara.love.domain.vo.LoveUserStudySessionDetailVo;
import org.dromara.love.domain.vo.LoveUserStudySessionDetailVoToLoveUserStudySessionDetailMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__787.class,
    uses = {LoveUserStudySessionDetailVoToLoveUserStudySessionDetailMapper.class,LoveUserStudySessionDetailBoToLoveUserStudySessionDetailMapper.class},
    imports = {}
)
public interface LoveUserStudySessionDetailToLoveUserStudySessionDetailVoMapper extends BaseMapper<LoveUserStudySessionDetail, LoveUserStudySessionDetailVo> {
}
