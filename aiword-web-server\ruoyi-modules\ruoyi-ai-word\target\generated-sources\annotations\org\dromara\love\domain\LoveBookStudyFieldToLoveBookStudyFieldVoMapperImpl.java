package org.dromara.love.domain;

import javax.annotation.processing.Generated;
import org.dromara.love.domain.vo.LoveBookStudyFieldVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-07T01:55:47+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (Microsoft)"
)
@Component
public class LoveBookStudyFieldToLoveBookStudyFieldVoMapperImpl implements LoveBookStudyFieldToLoveBookStudyFieldVoMapper {

    @Override
    public LoveBookStudyFieldVo convert(LoveBookStudyField arg0) {
        if ( arg0 == null ) {
            return null;
        }

        LoveBookStudyFieldVo loveBookStudyFieldVo = new LoveBookStudyFieldVo();

        loveBookStudyFieldVo.setId( arg0.getId() );
        loveBookStudyFieldVo.setName( arg0.getName() );
        loveBookStudyFieldVo.setCode( arg0.getCode() );
        loveBookStudyFieldVo.setStatus( arg0.getStatus() );

        return loveBookStudyFieldVo;
    }

    @Override
    public LoveBookStudyFieldVo convert(LoveBookStudyField arg0, LoveBookStudyFieldVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setName( arg0.getName() );
        arg1.setCode( arg0.getCode() );
        arg1.setStatus( arg0.getStatus() );

        return arg1;
    }
}
