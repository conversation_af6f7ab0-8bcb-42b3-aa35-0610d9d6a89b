package org.dromara.love.domain.bo;

import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.dromara.love.domain.LoveUserStudySessionDetail;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-07T01:55:47+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (Microsoft)"
)
@Component
public class LoveUserStudySessionDetailBoToLoveUserStudySessionDetailMapperImpl implements LoveUserStudySessionDetailBoToLoveUserStudySessionDetailMapper {

    @Override
    public LoveUserStudySessionDetail convert(LoveUserStudySessionDetailBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        LoveUserStudySessionDetail loveUserStudySessionDetail = new LoveUserStudySessionDetail();

        loveUserStudySessionDetail.setSearchValue( arg0.getSearchValue() );
        loveUserStudySessionDetail.setCreateDept( arg0.getCreateDept() );
        loveUserStudySessionDetail.setCreateBy( arg0.getCreateBy() );
        loveUserStudySessionDetail.setCreateTime( arg0.getCreateTime() );
        loveUserStudySessionDetail.setUpdateBy( arg0.getUpdateBy() );
        loveUserStudySessionDetail.setUpdateTime( arg0.getUpdateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            loveUserStudySessionDetail.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        loveUserStudySessionDetail.setId( arg0.getId() );
        loveUserStudySessionDetail.setSessionId( arg0.getSessionId() );
        loveUserStudySessionDetail.setUserId( arg0.getUserId() );
        loveUserStudySessionDetail.setWordId( arg0.getWordId() );
        if ( arg0.getActionType() != null ) {
            loveUserStudySessionDetail.setActionType( arg0.getActionType().intValue() );
        }
        if ( arg0.getComponentType() != null ) {
            loveUserStudySessionDetail.setComponentType( arg0.getComponentType().intValue() );
        }
        if ( arg0.getIsCorrect() != null ) {
            loveUserStudySessionDetail.setIsCorrect( arg0.getIsCorrect().intValue() );
        }
        loveUserStudySessionDetail.setResponseTime( arg0.getResponseTime() );
        if ( arg0.getStatusBefore() != null ) {
            loveUserStudySessionDetail.setStatusBefore( arg0.getStatusBefore().intValue() );
        }
        if ( arg0.getStatusAfter() != null ) {
            loveUserStudySessionDetail.setStatusAfter( arg0.getStatusAfter().intValue() );
        }
        loveUserStudySessionDetail.setActionTime( arg0.getActionTime() );

        return loveUserStudySessionDetail;
    }

    @Override
    public LoveUserStudySessionDetail convert(LoveUserStudySessionDetailBo arg0, LoveUserStudySessionDetail arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setId( arg0.getId() );
        arg1.setSessionId( arg0.getSessionId() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setWordId( arg0.getWordId() );
        if ( arg0.getActionType() != null ) {
            arg1.setActionType( arg0.getActionType().intValue() );
        }
        else {
            arg1.setActionType( null );
        }
        if ( arg0.getComponentType() != null ) {
            arg1.setComponentType( arg0.getComponentType().intValue() );
        }
        else {
            arg1.setComponentType( null );
        }
        if ( arg0.getIsCorrect() != null ) {
            arg1.setIsCorrect( arg0.getIsCorrect().intValue() );
        }
        else {
            arg1.setIsCorrect( null );
        }
        arg1.setResponseTime( arg0.getResponseTime() );
        if ( arg0.getStatusBefore() != null ) {
            arg1.setStatusBefore( arg0.getStatusBefore().intValue() );
        }
        else {
            arg1.setStatusBefore( null );
        }
        if ( arg0.getStatusAfter() != null ) {
            arg1.setStatusAfter( arg0.getStatusAfter().intValue() );
        }
        else {
            arg1.setStatusAfter( null );
        }
        arg1.setActionTime( arg0.getActionTime() );

        return arg1;
    }
}
