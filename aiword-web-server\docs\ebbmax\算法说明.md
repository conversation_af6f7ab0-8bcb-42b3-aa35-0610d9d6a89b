# 爱词记忆算法说明（EbbMax 事后评分与遗忘曲线）

本说明覆盖：参数含义（大白话）、本次会话评分S的计算、记忆分M的更新、遗忘曲线与下次复习时间、数据落库口径与对接点位。内容对应当前实现阶段：无阶段权重，按事件级（detail）加权。

--------------------------------
## 1. 核心目标（一句话）
每次学习/复习结束后，用本次会话里这个词产生的全部 detail 事件，算出一个表现分 S（0~1），把它转成记忆分变动 ΔM，叠加基础对错步进，更新这个词的记忆分 M（0~100）。再根据 M 推出遗忘速度，计算下一次要复习的时间点 nextReviewAt。

--------------------------------
## 2. 参数大白话

1) M0（新词初始记忆分）
- 干什么：新词第一次出现时，给它一个起步分，就像“默认信用分”。  
- 为什么需要：不至于从0开始太低，也不能太高导致不复习。  
- 建议值：20（可配）

2) S（本次综合表现分，0~1）
- 干什么：这次学/复习的综合表现。  
- 怎么来：把本次会话内该词所有 detail 事件，用组件权重、响应时间修正、错误簇惩罚加权平均得到。  
- 注意：S不是参数，是“算出来”的结果。

3) ΔM（本次记忆分的变动）
- 干什么：把 S 变成分数涨跌。  
- 公式：ΔM = (2S - 1) * 20，并且限制在 ±20。  
- 理解：S=0.5 不加不减；S>0.5 加分；S<0.5 扣分。

4) k_correct / k_wrong（基础推进/回退“底座”）
- 干什么：在 ΔM 之外，系统对“答对/答错”再给一个固定的加/减，保证对错态度稳定。  
- 建议：k_correct=+12，k_wrong=-18（可配）。

5) M（记忆分，0~100）
- 干什么：表示用户对这个词的当前掌握度。  
- 每次更新：M_new = clip(M_old + ΔM + baseStep, 0, 100)，baseStep 取 k_correct 或 k_wrong。

6) tau(level)（遗忘速度的“底盘”）
- 干什么：不同水平/阶段下的基础遗忘时间常数，数值越大越耐忘。  
- 建议档位：A=1.5天，B=2.5天，C=4天（可配）。

7) tauEffective（实际用的遗忘速度）
- 干什么：由 tau(level) 和当前 M 推导出来，M 越高，越耐忘。  
- 建议映射：tauEffective = tau(level) * (1 + 2.5 * M_norm)，M_norm=M/100。

8) R*（目标回忆率阈值）
- 干什么：我们希望下次提醒复习时用户还有多大概率能想起来。  
- 建议：R*=0.85。越高越保守，复习更早。

9) nextReviewAt（下次复习时间）
- 干什么：告诉你“什么时候该复习”。  
- 算法：R(t) = exp(-t / tauEffective)。解 t = tauEffective * ln(1 / R*)，然后 nextReviewAt = now + t。

10) T_new（新学首复间隔）
- 干什么：新词学完后第一次复习建议间隔，避免刚学就刷，也避免拖太久忘光。  
- 建议：8小时（可配）。

11) 短间隔补救
- 干什么：如果两次间隔很短但表现很好，给点小奖励，鼓励“及时补救”。  
- 建议：若间隔 < 30 分钟且 S≥0.7，则额外 +3；否则无奖励（可配）。

12) N_session / N_daily（推送上限）
- 干什么：限制每次会话/每天最多推多少学习/复习任务，避免信息过载。  
- 建议：N_session=25，N_daily=80（可配）。超限则合并或顺延。

13) mergedByLimit / silentInserted（记录标记）
- 干什么：mergedByLimit 表示因超限被合并；silentInserted 表示后台静默记账，不打扰用户。

14) 组件权重、响应时修正、错误簇惩罚（用于算 S）
- 组件权重：拼写1.30、汉选英1.15、英选汉1.10、听词选意1.15、释义0.20、是否认识0.50。  
- 响应时修正：≤2s:+0.08；2~5s:+0.04；5~10s:+0；>10s:-0.04。  
- 错误簇惩罚：连续错误每次 -0.08。  
- 说明：难题权重更高，答得快有微加成，连续错额外扣，避免“瞎蒙”。

--------------------------------
## 3. 本次会话综合表现 S 的计算（无阶段权重，仅事件级）

设本次会话内，该词的 detail 事件序列为 i=1..n。  
每条事件的基础分：
- base_i = 1（正确）或 0（错误）

响应时修正 timeAdj_i：
- 若 ≤2s: +0.08  
- 2~5s: +0.04  
- 5~10s: +0.00  
- >10s: -0.04

错误簇惩罚 errAdj_i：
- 若该条与前一条在同会话、同词且出现“连续错误”，则每次 -0.08；否则 0。

事件分 score_i：
- score_i = base_i + timeAdj_i - errAdj_i

组件权重 w_i（按 component_type）：
- 拼写=1.30、汉选英=1.15、英选汉=1.10、听词选意=1.15、释义=0.20、是否认识=0.50

综合表现 S（截断到[0,1]）：
- S_raw = (Σ w_i * score_i) / (Σ w_i)  
- S = clip(S_raw, 0, 1)

备注：释义/是否认识并无对错，仅作“最低基准权重”的轻微引导。

--------------------------------
## 4. 记忆分更新 M

确定 baseStep：
- 若本次整体表现以“正确”为主（可按 S ≥ 0.5 判断）：baseStep = k_correct（默认 +12）  
- 否则：baseStep = k_wrong（默认 -18）

短间隔补救：
- 若距离上次到本次间隔 < 30 分钟且 S ≥ 0.7，则 extra = +3，否则 extra = 0。

单次变动限制：
- ΔM = clip((2S - 1) * 20, -20, +20)

合并得到新记忆分：
- M_new = clip(M_old + ΔM + baseStep + extra, 0, 100)

持久化前可记录：
- memoryScoreBefore = M_old  
- memoryScoreAfter  = M_new

--------------------------------
## 5. 遗忘曲线与下次复习时间

遗忘曲线（指数衰减）：
- R(t) = exp(-t / tauEffective)

tauEffective 映射（由 M 放大）：
- M_norm = M_new / 100  
- tauEffective = tau(level) * (1 + 2.5 * M_norm)

下次复习时间点：
- t* = tauEffective * ln(1 / R*)（R* 默认 0.85，可配）  
- nextReviewAt = now + t*

新学首复：
- 对“刚从未学习变为已学习”的场景，优先使用 T_new（默认 8 小时）。

--------------------------------
## 6. 数据落库与字段建议

用户-单词表（示例：LoveUserLearnedWords）
- memoryScore int [0..100]
- lastMemoryScore int
- memoryUpdatedAt datetime
- tauEffective decimal(6,3)
- nextReviewAt datetime
- silentInserted tinyint(1)

历史/明细增强（与 session 关联）
- sessionId varchar(64)
- memoryScoreBefore int
- memoryScoreAfter int
- mergedByLimit tinyint(1)
- isSilent tinyint(1)

--------------------------------
## 7. 服务改造与对接点位

后端流程（伪代码）
- 在 [`MemoryModelUtils.calculateSessionWordMemory()`](aiword-web-server/src/main/java/org/dromara/system/service/MemoryModelUtils.java:1) 处实现评分与更新逻辑：
  - 入参：sessionId, wordId, userId
  - 拉取本次会话内该词的全部 detail 事件
  - 计算 S、ΔM、baseStep、extra
  - 生成 M_new、tauEffective、nextReviewAt
  - 更新用户-单词表；写入历史/日志（Before/After、sessionId、silent/merged 标记）

接入点位
- 在 [`saveLearnedWord()`](aiword-web-server/src/main/java/org/dromara/system/service/WordStudyService.java:1) 与 [`saveReviewWord()`](aiword-web-server/src/main/java/org/dromara/system/service/WordStudyService.java:1) 流程尾部调用上述方法，可异步（事件/消息）执行，失败重试。

任务与少推策略
- 生成后续复习任务时检查 N_session/N_daily 上限，超限则合并或顺延，并写 mergedByLimit 标记；静默入库不打扰用户。

--------------------------------
## 8. 前端接口与展示

- 学/复结束接口返回：memoryScoreAfter、nextReviewAt（可选）。  
- 列表显示 memoryScore；详情页展示“记忆曲线”（当前 R(t)、nextReviewAt 标记）。  
- 无需阶段权重字段。

--------------------------------
## 9. 配置与灰度

- 所有参数可在配置中心按全局/租户/用户级覆盖：M0、k_correct、k_wrong、tau(level)、T_new、短间隔补救阈值、N_session、N_daily、R*、ΔM上限等。  
- 提供开关回滚“纯艾宾浩斯”（忽略本次会话 S，只做固定对错推进）。

--------------------------------
## 10. 示例

假设某词本次会话有 4 条 detail：  
- 英选汉：正确，用时 3s → base=1, +0.04, 权重1.10  
- 听词选意：正确，用时 1.5s → base=1, +0.08, 权重1.15  
- 拼写：错误，用时 7s → base=0, +0.00, 权重1.30  
- 拼写（紧接上一次继续错）：错误，用时 6s → base=0, +0.00，错误簇-0.08，权重1.30

按权重求 S，得到 S≈0.47（举例）。  
ΔM=(2×0.47-1)×20≈-2.0；若判定为“错误为主”则 baseStep=-18；短间隔不触发，则 M_new=M_old-20（卡到不低于0）。  
随后由 M_new 推出 tauEffective，再算 nextReviewAt。

--------------------------------
## 11. 版本与变更

- v1.0：无阶段权重，仅事件级加权（当前文档）。  
- 预留演进：支持个性化组件权重学习、响应时自适应分段、错题回退随个体难度调节等。