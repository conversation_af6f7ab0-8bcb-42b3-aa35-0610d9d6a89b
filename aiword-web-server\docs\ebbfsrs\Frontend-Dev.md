# FSRS + 艾宾浩斯 + 生词本 前端开发文档（序号模式）

本开发文档面向前端工程师，采用“序号模式”给出可直接编码的步骤与接口契约。目标是最小改动接入 FSRS，保持艾宾浩斯兜底不变，完善生词本与记忆评分/曲线。

## 1. 页面与模块改动清单

1.1 播放引擎与学习会话（必改）
- 位置参考：
  - 队列工厂：[`LearningModeFactory.createFactoryInstance`](aiword-web-client/src/store/modules/wordPlaylist/LearningModeFactory.ts:62)
  - 会话与作答：[`useWordPlaylistStore`](aiword-web-client/src/store/modules/wordPlaylist/wordPlaylist.ts:46)
- 目标：统一在“词完成前”计算 q（仅 B 段与生词本训练），聚合 responseTime，并提交 saveReviewWord。

1.2 强化复习入口（B 段）
- 在 A 段复习完成页增加“进入强化训练”按钮
- 新增 B 段会话路由或模式切换：setCurrentMode('review')

1.3 生词本页面
- 列表页：unitId 过滤、排序（最近标记优先/记忆强度低优先）
- 训练页：沿用 B 段播放策略，提交 q → 触发 FSRS 更新
- 出库提示：连续两轮 q≥4 或 memory_strength_score≥阈值时显示“已出库”

1.4 记忆评分/曲线展示
- 在“词详情/复盘页”展示 memoryStrengthScore 与 nextReviewAt
- 折线图显示 30 天 R(t) 曲线并标注 nextReviewAt

## 2. 接口约定与请求体

2.1 保存复习
- URL：POST /love/userLearnedWords/saveReviewWord
- A 段（艾宾浩斯推进）：
  - body：
    {
      "userId": 1001,
      "bookId": 2001,
      "unitId": 3001,
      "wordId": 4001,
      "track": "EBB",
      "isCorrect": true,
      "responseTime": 2500,
      "sessionId": "def-456"
    }
- B 段/生词本（FSRS 更新）：
  - body：
    {
      "userId": 1001,
      "bookId": 2001,
      "unitId": 3001,
      "wordId": 4001,
      "track": "FSRS",
      "quality": 3,
      "responseTime": 12450,
      "sessionId": "abc-123"
    }
- 响应（track=FSRS）：
  {
    "nextReviewAt": "2025-08-07T10:30:00Z",
    "memoryStrengthScore": 86
  }

2.2 获取到期清单
- A 段：GET /love/userLearnedWords/getReviewWords?unitId=xx
- B 段：GET /love/userLearnedWords/getFsrsReviewWords?unitId=xx

2.3 记忆评分与曲线
- GET /love/userLearnedWords/memoryScore?wordId=xx
- GET /love/userLearnedWords/memoryCurve?wordId=xx&days=30

2.4 生词本
- GET /love/newWords/list?unitId=xx
- POST /love/newWords/mark
  - {userId, wordId, reason}
- POST /love/newWords/unmark
  - {userId, wordId}

## 3. q 评分计算器（词级 0..5）

3.1 卡片权重（默认）
- IsKnow: 0.2
- Paraphrase: 0.2
- En2Cn: 0.25
- Cn2En: 0.25
- Audio2Cn: 0.1
- Write（可选）：可与 Cn2En 共享 0.25 或单独设置（例如 0.2），其余按比例缩放

3.2 基准与扣分
- 基准 base：IsKnow=认识 → 4；不认识 → 2
- 扣分项：
  - 错误：每错 -1×权重
  - 过慢：单卡 responseMs > slowThreshold(默认 12000 ms) → -1×权重
  - 提示：使用提示/看答案 → -1×权重
  - 额外惩罚：对输出型 Cn2En/Write 的错误额外 -0.5×权重
- 最终：
  - q = clamp(round(base - Σ扣分), 0, 5)
  - responseTime = sum(card.responseMs)

3.3 难/易判定（会话策略与生词本）
- 难词：q < 3 或 总错误 ≥ 2 或 responseTime > 30000
- 易词：q ≥ 4 且 总错误=0 且 responseTime ≤ 10000

3.4 实现建议
- 新增工具模块：wordQualityCalculator.ts
  - export function calcWordQuality(cards: CardStat[], isKnowRecognized: boolean): { q: number; responseTime: number; debug?: object }

卡片统计 CardStat 字段建议：
- type: 'IsKnow' | 'Paraphrase' | 'En2Cn' | 'Cn2En' | 'Audio2Cn' | 'Write'
- wrong: boolean
- responseMs: number
- hinted: boolean

## 4. 播放引擎改造点

4.1 会话内重复策略
- 错题：立即重复 + 后移在第2~3位再插一次相同单词的下一张卡
- 易词：本轮不额外重复
- B 段强化：对输出型卡（Cn2En/Write）可提高出现频率

4.2 统一保存钩子
- 在“词最后一张卡片提交前”，调用 q 计算器（B 段/生词本）
- 组装 body 并调用 saveReviewWord
- 成功后从队列移除该词；失败则重试或标记为待提交

4.3 幂等与重试
- 携带 sessionId；失败后允许重试
- 避免对同一词重复提交：保存完成后在本轮会话标记已提交

## 5. UI 与交互

5.1 强化入口
- A 段会话完成页出现“进入强化训练”按钮
- 点击后读取 B 段清单；若为空提示“已全部掌握，建议学习新词或训练生词本”

5.2 生词本
- 列表：显示标记时间、难词原因、当前记忆强度（若有）
- 训练：与 B 段一致；完成后展示“是否达到出库条件”的提示

5.3 记忆评分/曲线
- memoryScore：以圆形进度条或徽章展示 memoryStrengthScore
- memoryCurve：折线图；nextReviewAt 以垂直线/锚点标注

## 6. 状态管理与类型定义

6.1 Store 扩展
- 在 wordPlaylist store 中增加：
  - 当前模式 currentMode: 'learn' | 'review' | 'newword'
  - 当前会话统计：累计正确/错误/时长
  - 提交缓存：待提交队列（遇网络波动或接口失败时用）

6.2 TypeScript 类型
- 定义 CardStat、WordResultPayload、SaveReviewWordRequest、MemoryScoreResponse、MemoryCurveResponse 接口
- 提供严格的枚举类型 Track: 'EBB' | 'FSRS'

## 7. 埋点方案

7.1 事件
- learn.start, learn.card.submit, learn.word.complete, learn.session.complete
- ebb.fetch, ebb.card.submit, ebb.word.save, ebb.session.complete
- fsrs.fetch, fsrs.card.submit, fsrs.word.save, fsrs.session.complete
- newword.mark, newword.list, newword.train.save, newword.exit
- memory.score.view, memory.curve.view

7.2 字段
- userId, bookId, unitId, wordId, sessionId, timestamp, track, quality, responseTime, nextReviewAt

## 8. 错误处理与边界

8.1 q 缺失
- 若 B 段计算异常未得到 q：不提交 FSRS；前端提示“稍后再试”

8.2 清单为空
- A 段或 B 段清单为空：友好提示并引导到另一个流程（学习/生词本）

8.3 网络波动
- 重试策略：指数退避（最多 3 次）
- 本地缓存待提交（可选）：session 结束时尝试补交

## 9. 验收场景（前端自测与联调）

9.1 学习→初始化 FSRS
- 学会某词后，后端 fsrs_* 字段被初始化，fsrs_next_review_at ≈ now+30min

9.2 A 段→不写 FSRS
- 只推进艾宾浩斯；FSRS 字段保持不变
- 会话内难词多推、易词不重复

9.3 B 段→提交 q
- q 高：nextReviewAt 拉长
- q 低：nextReviewAt 缩短且不低于 1 小时

9.4 生词本→入/出库
- 满足阈值入库；训练两轮达标自动出库

9.5 记忆评分/曲线
- memoryStrengthScore 正常显示
- 曲线 30 个点正常绘制，nextReviewAt 标注正确

## 10. 代码组织与落地顺序

10.1 新增文件建议
- src/store/modules/wordPlaylist/wordQualityCalculator.ts
- src/api/book/userStudySessionDetail/requests.ts（或现有目录下新增 FSRS 请求类型）
- src/views/newWords/index.vue（生词本列表）
- src/views/newWords/train.vue（生词本训练）
- src/views/wordDetail/memory.vue（记忆评分与曲线）

10.2 开发顺序（两天可联调）
1) Day1 上午：q 计算器 + 保存钩子改造（B 段/生词本）
2) Day1 下午：B 段入口与清单请求；A 段策略复用（仅前端队列）
3) Day2 上午：生词本页与训练；记忆评分条
4) Day2 下午：记忆曲线页；埋点接通；整体联调与自测

## 11. 与后端契合点

11.1 入参/出参严格对齐
- track=EBB | FSRS；quality 仅 FSRS 需要
- responseTime 统一为 ms

11.2 幂等
- sessionId 建议采用 `${userId}-${timestamp}-${random}`；同一轮提交复用相同值

11.3 时间处理
- 所有展示时间以本地时区格式化；提交与存储统一 ISO 字符串或时间戳

## 12. 可配置项（前端常量）

- slowThresholdMs = 12000
- longResponseThresholdMs = 30000
- easyWordThresholdMs = 10000
- qualityWeights = { IsKnow:0.2, Paraphrase:0.2, En2Cn:0.25, Cn2En:0.25, Audio2Cn:0.1, Write:0.25 }
- newWordExitScore = 80（memory_strength_score 出库阈值）