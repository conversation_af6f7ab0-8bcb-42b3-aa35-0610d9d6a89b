package org.dromara.love.domain.bo;

import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.dromara.love.domain.LoveBookType;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-07T01:55:47+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (Microsoft)"
)
@Component
public class LoveBookTypeBoToLoveBookTypeMapperImpl implements LoveBookTypeBoToLoveBookTypeMapper {

    @Override
    public LoveBookType convert(LoveBookTypeBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        LoveBookType loveBookType = new LoveBookType();

        loveBookType.setSearchValue( arg0.getSearchValue() );
        loveBookType.setCreateDept( arg0.getCreateDept() );
        loveBookType.setCreateBy( arg0.getCreateBy() );
        loveBookType.setCreateTime( arg0.getCreateTime() );
        loveBookType.setUpdateBy( arg0.getUpdateBy() );
        loveBookType.setUpdateTime( arg0.getUpdateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            loveBookType.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        loveBookType.setId( arg0.getId() );
        loveBookType.setName( arg0.getName() );
        loveBookType.setBookStudyFieldName( arg0.getBookStudyFieldName() );
        loveBookType.setBookStudyFieldId( arg0.getBookStudyFieldId() );

        return loveBookType;
    }

    @Override
    public LoveBookType convert(LoveBookTypeBo arg0, LoveBookType arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setId( arg0.getId() );
        arg1.setName( arg0.getName() );
        arg1.setBookStudyFieldName( arg0.getBookStudyFieldName() );
        arg1.setBookStudyFieldId( arg0.getBookStudyFieldId() );

        return arg1;
    }
}
