package org.dromara.love.domain.bo;

import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.dromara.love.domain.LoveUserLearnedUnits;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-07T01:55:47+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (Microsoft)"
)
@Component
public class LoveUserLearnedUnitsBoToLoveUserLearnedUnitsMapperImpl implements LoveUserLearnedUnitsBoToLoveUserLearnedUnitsMapper {

    @Override
    public LoveUserLearnedUnits convert(LoveUserLearnedUnitsBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        LoveUserLearnedUnits loveUserLearnedUnits = new LoveUserLearnedUnits();

        loveUserLearnedUnits.setSearchValue( arg0.getSearchValue() );
        loveUserLearnedUnits.setCreateDept( arg0.getCreateDept() );
        loveUserLearnedUnits.setCreateBy( arg0.getCreateBy() );
        loveUserLearnedUnits.setCreateTime( arg0.getCreateTime() );
        loveUserLearnedUnits.setUpdateBy( arg0.getUpdateBy() );
        loveUserLearnedUnits.setUpdateTime( arg0.getUpdateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            loveUserLearnedUnits.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        loveUserLearnedUnits.setId( arg0.getId() );
        loveUserLearnedUnits.setUserId( arg0.getUserId() );
        loveUserLearnedUnits.setBookId( arg0.getBookId() );
        loveUserLearnedUnits.setUnitId( arg0.getUnitId() );
        if ( arg0.getUnitName() != null ) {
            loveUserLearnedUnits.setUnitName( String.valueOf( arg0.getUnitName() ) );
        }
        loveUserLearnedUnits.setUnitNumber( arg0.getUnitNumber() );
        loveUserLearnedUnits.setTotalWords( arg0.getTotalWords() );
        loveUserLearnedUnits.setLearnedWords( arg0.getLearnedWords() );
        loveUserLearnedUnits.setProgressPercent( arg0.getProgressPercent() );
        loveUserLearnedUnits.setStatus( arg0.getStatus() );
        loveUserLearnedUnits.setFirstLearnTime( arg0.getFirstLearnTime() );
        loveUserLearnedUnits.setLastLearnTime( arg0.getLastLearnTime() );
        loveUserLearnedUnits.setLearnDuration( arg0.getLearnDuration() );
        loveUserLearnedUnits.setReviewCount( arg0.getReviewCount() );

        return loveUserLearnedUnits;
    }

    @Override
    public LoveUserLearnedUnits convert(LoveUserLearnedUnitsBo arg0, LoveUserLearnedUnits arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setId( arg0.getId() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setBookId( arg0.getBookId() );
        arg1.setUnitId( arg0.getUnitId() );
        if ( arg0.getUnitName() != null ) {
            arg1.setUnitName( String.valueOf( arg0.getUnitName() ) );
        }
        else {
            arg1.setUnitName( null );
        }
        arg1.setUnitNumber( arg0.getUnitNumber() );
        arg1.setTotalWords( arg0.getTotalWords() );
        arg1.setLearnedWords( arg0.getLearnedWords() );
        arg1.setProgressPercent( arg0.getProgressPercent() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setFirstLearnTime( arg0.getFirstLearnTime() );
        arg1.setLastLearnTime( arg0.getLastLearnTime() );
        arg1.setLearnDuration( arg0.getLearnDuration() );
        arg1.setReviewCount( arg0.getReviewCount() );

        return arg1;
    }
}
