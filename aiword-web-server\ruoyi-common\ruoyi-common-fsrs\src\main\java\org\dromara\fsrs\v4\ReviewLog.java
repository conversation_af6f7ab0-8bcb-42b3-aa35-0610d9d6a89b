package org.dromara.fsrs.v4;

import java.time.ZonedDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 1:1 复刻 py-fsrs fsrs.review_log.ReviewLog 与 Rating
 */
public final class ReviewLog {

    public enum Rating {
        Again(1),
        Hard(2),
        Good(3),
        Easy(4);

        public final int value;

        Rating(int value) {
            this.value = value;
        }

        public static Rating fromValue(int v) {
            for (Rating r : values()) {
                if (r.value == v) return r;
            }
            throw new IllegalArgumentException("Unknown Rating value: " + v);
        }
    }

    public long cardId;
    public Rating rating;
    public ZonedDateTime reviewDatetime; // ISO 8601, UTC
    public Integer reviewDuration; // 毫秒，可为 null

    public ReviewLog(long cardId, Rating rating, ZonedDateTime reviewDatetime, Integer reviewDuration) {
        this.cardId = cardId;
        this.rating = Objects.requireNonNull(rating, "rating");
        this.reviewDatetime = Objects.requireNonNull(reviewDatetime, "reviewDatetime");
        this.reviewDuration = reviewDuration;
    }

    public Map<String, Object> toMap() {
        Map<String, Object> m = new HashMap<>();
        m.put("card_id", this.cardId);
        m.put("rating", this.rating.value);
        m.put("review_datetime", this.reviewDatetime.toOffsetDateTime().toString());
        m.put("review_duration", this.reviewDuration);
        return m;
    }

    public static ReviewLog fromMap(Map<String, Object> source) {
        Objects.requireNonNull(source, "source map is null");

        long cardId = Long.parseLong(String.valueOf(source.get("card_id")));
        Rating rating = Rating.fromValue(Integer.parseInt(String.valueOf(source.get("rating"))));
        ZonedDateTime reviewDatetime = ZonedDateTime.parse(String.valueOf(source.get("review_datetime")));
        Integer reviewDuration = source.get("review_duration") == null ? null : Integer.valueOf(String.valueOf(source.get("review_duration")));
        return new ReviewLog(cardId, rating, reviewDatetime, reviewDuration);
    }
}