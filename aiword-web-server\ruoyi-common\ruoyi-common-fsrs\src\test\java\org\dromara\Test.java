package org.dromara;

import org.dromara.fsrs.v4.Card;
import org.dromara.fsrs.v4.ReviewLog;
import org.dromara.fsrs.v4.Scheduler;

import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 需求：重写 Test，完成两次 Easy 评审，第二次评审时间要基于第一次计算出的 due 日期。
 * 说明：
 * - 使用 v4 包下实现：Card、Scheduler、ReviewLog.Rating
 * - 时间一律使用 UTC（与 Python 版本保持一致）
 */
public class Test {

    private static final DateTimeFormatter F = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ssXXX");

    public static void main(String[] args) {
        // 1) 初始化调度器（采用默认配置）
        Scheduler scheduler = new Scheduler();

        // 2) 新建卡片（默认 Learning，due=now）
        Card card = new Card();

        // 3) 第一次评审：Easy，在指定的 UTC 时间进行
        ZonedDateTime firstReviewTime = ZonedDateTime.now(ZoneOffset.UTC);
        var firstResult = scheduler.reviewCard(card, ReviewLog.Rating.Easy, firstReviewTime, null);
        Card afterFirst = firstResult.getKey();

        System.out.println("第一次评审 = Easy");
        System.out.println("评审时间(UTC): " + F.format(firstReviewTime));
        System.out.println("计算得到的下次到期(due, UTC): " + F.format(afterFirst.due));
        System.out.println("卡片状态: " + afterFirst.state + ", step=" + afterFirst.step + ", stability=" + afterFirst.stability + ", difficulty=" + afterFirst.difficulty);
        System.out.println();

        // 4) 第二次评审：Easy，评审时间设为“第一次的 due”
        ZonedDateTime secondReviewTime = afterFirst.due;
        var secondResult = scheduler.reviewCard(afterFirst, ReviewLog.Rating.Easy, secondReviewTime, null);
        Card afterSecond = secondResult.getKey();

        System.out.println("第二次评审 = Easy（基于第一次的 due）");
        System.out.println("评审时间(UTC): " + F.format(secondReviewTime));
        System.out.println("计算得到的下次到期(due, UTC): " + F.format(afterSecond.due));
        System.out.println("卡片状态: " + afterSecond.state + ", step=" + afterSecond.step + ", stability=" + afterSecond.stability + ", difficulty=" + afterSecond.difficulty);
    }
}
