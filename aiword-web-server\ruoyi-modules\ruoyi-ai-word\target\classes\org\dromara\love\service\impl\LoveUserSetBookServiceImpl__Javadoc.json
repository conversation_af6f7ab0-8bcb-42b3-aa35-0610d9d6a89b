{"doc": " 学习-用户设置书籍Service业务层处理\n\n <AUTHOR>\n @date 2025-06-16\n", "fields": [], "enumConstants": [], "methods": [{"name": "queryById", "paramTypes": ["java.lang.Long"], "doc": " 查询学习-用户设置书籍\n\n @param id 主键\n @return 学习-用户设置书籍\n"}, {"name": "queryPageList", "paramTypes": ["org.dromara.love.domain.bo.LoveUserSetBookBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 分页查询学习-用户设置书籍列表\n\n @param bo        查询条件\n @param pageQuery 分页参数\n @return 学习-用户设置书籍分页列表\n"}, {"name": "queryList", "paramTypes": ["org.dromara.love.domain.bo.LoveUserSetBookBo"], "doc": " 查询符合条件的学习-用户设置书籍列表\n\n @param bo 查询条件\n @return 学习-用户设置书籍列表\n"}, {"name": "insertByBo", "paramTypes": ["org.dromara.love.domain.bo.LoveUserSetBookBo"], "doc": " 新增学习-用户设置书籍\n\n @param bo 学习-用户设置书籍\n @return 是否新增成功\n"}, {"name": "updateByBo", "paramTypes": ["org.dromara.love.domain.bo.LoveUserSetBookBo"], "doc": " 修改学习-用户设置书籍\n\n @param bo 学习-用户设置书籍\n @return 是否修改成功\n"}, {"name": "validEntityBeforeSave", "paramTypes": ["org.dromara.love.domain.LoveUserSetBook"], "doc": " 保存前的数据校验\n"}, {"name": "deleteWithValidByIds", "paramTypes": ["java.util.Collection", "java.lang.Bo<PERSON>an"], "doc": " 校验并批量删除学习-用户设置书籍信息\n\n @param ids     待删除的主键集合\n @param isValid 是否进行有效性校验\n @return 是否删除成功\n"}, {"name": "getInfoByUserId", "paramTypes": ["java.lang.Long"], "doc": " 按用户 ID 获取已设置书籍信息（按record_type分组）\n\n @param userId 用户 ID\n @return {@link UserBookCollectionVo} 按record_type分组的书籍集合\n"}], "constructors": []}