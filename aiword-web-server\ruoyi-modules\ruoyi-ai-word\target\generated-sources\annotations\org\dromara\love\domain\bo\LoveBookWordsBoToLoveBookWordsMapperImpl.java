package org.dromara.love.domain.bo;

import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.dromara.love.domain.LoveBookWords;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-07T01:55:47+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (Microsoft)"
)
@Component
public class LoveBookWordsBoToLoveBookWordsMapperImpl implements LoveBookWordsBoToLoveBookWordsMapper {

    @Override
    public LoveBookWords convert(LoveBookWordsBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        LoveBookWords loveBookWords = new LoveBookWords();

        loveBookWords.setSearchValue( arg0.getSearchValue() );
        loveBookWords.setCreateDept( arg0.getCreateDept() );
        loveBookWords.setCreateBy( arg0.getCreateBy() );
        loveBookWords.setCreateTime( arg0.getCreateTime() );
        loveBookWords.setUpdateBy( arg0.getUpdateBy() );
        loveBookWords.setUpdateTime( arg0.getUpdateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            loveBookWords.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        loveBookWords.setId( arg0.getId() );
        loveBookWords.setOId( arg0.getOId() );
        if ( arg0.getWordBookId() != null ) {
            loveBookWords.setWordBookId( Long.parseLong( arg0.getWordBookId() ) );
        }
        loveBookWords.setWordBookOId( arg0.getWordBookOId() );
        loveBookWords.setWordBookOUnitId( arg0.getWordBookOUnitId() );
        if ( arg0.getWordBookUnitId() != null ) {
            loveBookWords.setWordBookUnitId( Long.parseLong( arg0.getWordBookUnitId() ) );
        }
        loveBookWords.setName( arg0.getName() );
        loveBookWords.setPhoneticSymbol( arg0.getPhoneticSymbol() );
        loveBookWords.setVowelSeparation( arg0.getVowelSeparation() );
        loveBookWords.setPath( arg0.getPath() );
        loveBookWords.setParaphrase( arg0.getParaphrase() );

        return loveBookWords;
    }

    @Override
    public LoveBookWords convert(LoveBookWordsBo arg0, LoveBookWords arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setId( arg0.getId() );
        arg1.setOId( arg0.getOId() );
        if ( arg0.getWordBookId() != null ) {
            arg1.setWordBookId( Long.parseLong( arg0.getWordBookId() ) );
        }
        else {
            arg1.setWordBookId( null );
        }
        arg1.setWordBookOId( arg0.getWordBookOId() );
        arg1.setWordBookOUnitId( arg0.getWordBookOUnitId() );
        if ( arg0.getWordBookUnitId() != null ) {
            arg1.setWordBookUnitId( Long.parseLong( arg0.getWordBookUnitId() ) );
        }
        else {
            arg1.setWordBookUnitId( null );
        }
        arg1.setName( arg0.getName() );
        arg1.setPhoneticSymbol( arg0.getPhoneticSymbol() );
        arg1.setVowelSeparation( arg0.getVowelSeparation() );
        arg1.setPath( arg0.getPath() );
        arg1.setParaphrase( arg0.getParaphrase() );

        return arg1;
    }
}
