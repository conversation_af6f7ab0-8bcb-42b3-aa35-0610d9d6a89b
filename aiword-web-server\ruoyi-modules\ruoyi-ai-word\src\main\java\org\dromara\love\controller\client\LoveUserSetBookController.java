package org.dromara.love.controller.client;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.web.core.BaseController;
import org.dromara.love.domain.LoveUserSetBook;
import org.dromara.love.domain.vo.LoveUserSetBookVo;
import org.dromara.love.domain.vo.UserBookCollectionVo;
import org.dromara.love.service.ILoveUserSetBookService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 学习-用户设置书籍
 *
 * <AUTHOR> Li
 * @date 2025-06-16
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/love/userSetBook")
public class LoveUserSetBookController extends BaseController {

    private final ILoveUserSetBookService loveUserSetBookService;


    /**
     * 获取学习-用户设置书籍详细信息（按record_type分组）
     *
     * @param userId 用户ID
     */
    @SaCheckPermission(orRole = "student")
    @GetMapping("/{userId}")
    public R<UserBookCollectionVo> getInfoByUserId(@NotNull(message = "参数不能为空")
                                               @PathVariable Long userId) {
        return R.ok(loveUserSetBookService.getInfoByUserId(userId));
    }

    /**
     * 用户设置书籍
     * 根据bookTextBookId判断是否存在，存在则更新isActive=1，不存在则插入
     */
    @SaCheckPermission(orRole = "student")
    @Log(title = "学习-用户设置书籍", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/setBook")
    public R<Void> setBook(@Validated(AddGroup.class) @RequestBody LoveUserSetBook loveUserSetBook) {
        // 先检查该用户是否已经设置过这本书
        LambdaQueryWrapper<LoveUserSetBook> wrapper = new LambdaQueryWrapper<LoveUserSetBook>()
            .eq(LoveUserSetBook::getUserId, loveUserSetBook.getUserId())
            .eq(LoveUserSetBook::getBookTextBookId, loveUserSetBook.getBookTextBookId())
            .eq(LoveUserSetBook::getRecordType, loveUserSetBook.getRecordType())
            .eq(LoveUserSetBook::getDelFlag, 0);

        LoveUserSetBook existing = loveUserSetBookService.getOne(wrapper);

        if (existing != null) {
            // 已存在，更新为激活状态
            existing.setIsActive(true);
            loveUserSetBookService.updateById(existing);
        } else {
            // 不存在，插入新记录
            loveUserSetBook.setIsActive(true);
            loveUserSetBookService.save(loveUserSetBook);
        }

        // 将该用户该类型的其他书籍设为非激活
        LambdaQueryWrapper<LoveUserSetBook> updateWrapper = new LambdaQueryWrapper<LoveUserSetBook>()
            .eq(LoveUserSetBook::getUserId, loveUserSetBook.getUserId())
            .eq(LoveUserSetBook::getRecordType, loveUserSetBook.getRecordType())
            .ne(LoveUserSetBook::getBookTextBookId, loveUserSetBook.getBookTextBookId())
            .eq(LoveUserSetBook::getDelFlag, 0);

        LoveUserSetBook updateEntity = new LoveUserSetBook();
        updateEntity.setIsActive(false);
        loveUserSetBookService.update(updateEntity, updateWrapper);

        return R.ok();
    }


}
