package org.dromara.love.domain.bo;

import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.dromara.love.domain.LoveUserLearnedWords;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-07T01:55:47+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (Microsoft)"
)
@Component
public class LoveUserLearnedWordsBoToLoveUserLearnedWordsMapperImpl implements LoveUserLearnedWordsBoToLoveUserLearnedWordsMapper {

    @Override
    public LoveUserLearnedWords convert(LoveUserLearnedWordsBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        LoveUserLearnedWords loveUserLearnedWords = new LoveUserLearnedWords();

        loveUserLearnedWords.setSearchValue( arg0.getSearchValue() );
        loveUserLearnedWords.setCreateDept( arg0.getCreateDept() );
        loveUserLearnedWords.setCreateBy( arg0.getCreateBy() );
        loveUserLearnedWords.setCreateTime( arg0.getCreateTime() );
        loveUserLearnedWords.setUpdateBy( arg0.getUpdateBy() );
        loveUserLearnedWords.setUpdateTime( arg0.getUpdateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            loveUserLearnedWords.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        loveUserLearnedWords.setId( arg0.getId() );
        loveUserLearnedWords.setUserId( arg0.getUserId() );
        loveUserLearnedWords.setBookId( arg0.getBookId() );
        loveUserLearnedWords.setUnitId( arg0.getUnitId() );
        loveUserLearnedWords.setWordId( arg0.getWordId() );
        loveUserLearnedWords.setFirstLearnTime( arg0.getFirstLearnTime() );
        loveUserLearnedWords.setLastLearnTime( arg0.getLastLearnTime() );
        loveUserLearnedWords.setReviewLevel( arg0.getReviewLevel() );
        loveUserLearnedWords.setNextReviewDate( arg0.getNextReviewDate() );
        loveUserLearnedWords.setLastReviewTime( arg0.getLastReviewTime() );
        loveUserLearnedWords.setReviewCount( arg0.getReviewCount() );
        loveUserLearnedWords.setCorrectReviewCount( arg0.getCorrectReviewCount() );

        return loveUserLearnedWords;
    }

    @Override
    public LoveUserLearnedWords convert(LoveUserLearnedWordsBo arg0, LoveUserLearnedWords arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setId( arg0.getId() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setBookId( arg0.getBookId() );
        arg1.setUnitId( arg0.getUnitId() );
        arg1.setWordId( arg0.getWordId() );
        arg1.setFirstLearnTime( arg0.getFirstLearnTime() );
        arg1.setLastLearnTime( arg0.getLastLearnTime() );
        arg1.setReviewLevel( arg0.getReviewLevel() );
        arg1.setNextReviewDate( arg0.getNextReviewDate() );
        arg1.setLastReviewTime( arg0.getLastReviewTime() );
        arg1.setReviewCount( arg0.getReviewCount() );
        arg1.setCorrectReviewCount( arg0.getCorrectReviewCount() );

        return arg1;
    }
}
