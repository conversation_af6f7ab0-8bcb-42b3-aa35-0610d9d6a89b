package org.dromara.love.domain.vo;

import io.github.linpeilie.AutoMapperConfig__787;
import io.github.linpeilie.BaseMapper;
import org.dromara.love.domain.LoveBookType;
import org.dromara.love.domain.LoveBookTypeToLoveBookTypeVoMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__787.class,
    uses = {LoveBookTypeToLoveBookTypeVoMapper.class},
    imports = {}
)
public interface LoveBookTypeVoToLoveBookTypeMapper extends BaseMapper<LoveBookTypeVo, LoveBookType> {
}
