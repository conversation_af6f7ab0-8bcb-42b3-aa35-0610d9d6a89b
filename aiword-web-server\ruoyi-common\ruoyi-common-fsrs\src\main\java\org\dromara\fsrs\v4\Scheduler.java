package org.dromara.fsrs.v4;

import java.time.Duration;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.util.*;

/**
 * 1:1 复刻 py-fsrs fsrs.scheduler.Scheduler 与常量
 * - 参数、边界、计算流程严格对齐
 * - 时间统一使用 UTC ZonedDateTime
 * - 学习/再学习步骤、最大间隔、fuzz 逻辑一致
 */
public final class Scheduler {

    // DEFAULT_PARAMETERS
    public static final double[] DEFAULT_PARAMETERS = new double[]{
            0.2172,
            1.1771,
            3.2602,
            16.1507,
            7.0114,
            0.57,
            2.0966,
            0.0069,
            1.5261,
            0.112,
            1.0178,
            1.849,
            0.1133,
            0.3127,
            2.2934,
            0.2191,
            3.0004,
            0.7536,
            0.3332,
            0.1437,
            0.2
    };

    public static final double STABILITY_MIN = 0.001;

    // LOWER_BOUNDS_PARAMETERS
    public static final double[] LOWER_BOUNDS_PARAMETERS = new double[]{
            STABILITY_MIN,
            STABILITY_MIN,
            STABILITY_MIN,
            STABILITY_MIN,
            1.0,
            0.001,
            0.001,
            0.001,
            0.0,
            0.0,
            0.001,
            0.001,
            0.001,
            0.001,
            0.0,
            0.0,
            1.0,
            0.0,
            0.0,
            0.0,
            0.1
    };

    public static final double INITIAL_STABILITY_MAX = 100.0;

    // UPPER_BOUNDS_PARAMETERS
    public static final double[] UPPER_BOUNDS_PARAMETERS = new double[]{
            INITIAL_STABILITY_MAX,
            INITIAL_STABILITY_MAX,
            INITIAL_STABILITY_MAX,
            INITIAL_STABILITY_MAX,
            10.0,
            4.0,
            4.0,
            0.75,
            4.5,
            0.8,
            3.5,
            5.0,
            0.25,
            0.9,
            4.0,
            1.0,
            6.0,
            2.0,
            2.0,
            0.8,
            0.8
    };

    public static final double MIN_DIFFICULTY = 1.0;
    public static final double MAX_DIFFICULTY = 10.0;

    private static final class FuzzRange {
        final double start;
        final double end;
        final double factor;
        FuzzRange(double start, double end, double factor) {
            this.start = start;
            this.end = end;
            this.factor = factor;
        }
    }

    public static final List<FuzzRange> FUZZ_RANGES = List.of(
            new FuzzRange(2.5, 7.0, 0.15),
            new FuzzRange(7.0, 20.0, 0.1),
            new FuzzRange(20.0, Double.POSITIVE_INFINITY, 0.05)
    );

    // 成员
    public final double[] parameters;                  // 长度固定为 21
    public final double desiredRetention;              // 默认 0.9
    public final List<Duration> learningSteps;         // 默认 [1min, 10min]
    public final List<Duration> relearningSteps;       // 默认 [10min]
    public final int maximumInterval;                  // 默认 36500
    public final boolean enableFuzzing;                // 默认 true

    // 派生常量
    private final double _DECAY;   // -parameters[20]
    private final double _FACTOR;  // 0.9 ** (1 / _DECAY) - 1

    public Scheduler() {
        this(DEFAULT_PARAMETERS, 0.9,
                List.of(Duration.ofMinutes(1), Duration.ofMinutes(10)),
                List.of(Duration.ofMinutes(10)),
                36500,
                true);
    }

    public Scheduler(double[] parameters,
                     double desiredRetention,
                     List<Duration> learningSteps,
                     List<Duration> relearningSteps,
                     int maximumInterval,
                     boolean enableFuzzing) {
        validateParameters(parameters);
        this.parameters = Arrays.copyOf(parameters, parameters.length);
        this.desiredRetention = desiredRetention;
        this.learningSteps = List.copyOf(learningSteps);
        this.relearningSteps = List.copyOf(relearningSteps);
        this.maximumInterval = maximumInterval;
        this.enableFuzzing = enableFuzzing;

        this._DECAY = -this.parameters[20];
        this._FACTOR = Math.pow(0.9, 1.0 / this._DECAY) - 1.0;
    }

    private static void validateParameters(double[] parameters) {
        if (parameters == null || parameters.length != LOWER_BOUNDS_PARAMETERS.length) {
            throw new IllegalArgumentException(
                    "Expected " + LOWER_BOUNDS_PARAMETERS.length + " parameters, got " + (parameters == null ? 0 : parameters.length));
        }
        List<String> errors = new ArrayList<>();
        for (int i = 0; i < parameters.length; i++) {
            double p = parameters[i];
            if (!(p >= LOWER_BOUNDS_PARAMETERS[i] && p <= UPPER_BOUNDS_PARAMETERS[i])) {
                errors.add("parameters[" + i + "] = " + p + " is out of bounds: (" + LOWER_BOUNDS_PARAMETERS[i] + ", " + UPPER_BOUNDS_PARAMETERS[i] + ")");
            }
        }
        if (!errors.isEmpty()) {
            throw new IllegalArgumentException("One or more parameters are out of bounds:\n" + String.join("\n", errors));
        }
    }

    // to_dict
    public Map<String, Object> toMap() {
        Map<String, Object> m = new HashMap<>();
        double[] ps = Arrays.copyOf(this.parameters, this.parameters.length);
        List<Double> plist = new ArrayList<>(ps.length);
        for (double v : ps) plist.add(v);
        m.put("parameters", plist);
        m.put("desired_retention", this.desiredRetention);

        List<Long> lSteps = new ArrayList<>(this.learningSteps.size());
        for (Duration d : this.learningSteps) lSteps.add(d.getSeconds());
        m.put("learning_steps", lSteps);

        List<Long> rSteps = new ArrayList<>(this.relearningSteps.size());
        for (Duration d : this.relearningSteps) rSteps.add(d.getSeconds());
        m.put("relearning_steps", rSteps);

        m.put("maximum_interval", this.maximumInterval);
        m.put("enable_fuzzing", this.enableFuzzing);
        return m;
    }

    // from_dict
    @SuppressWarnings("unchecked")
    public static Scheduler fromMap(Map<String, Object> source) {
        Objects.requireNonNull(source, "source map is null");

        List<Object> paramsList = (List<Object>) source.get("parameters");
        double[] params = new double[paramsList.size()];
        for (int i = 0; i < paramsList.size(); i++) {
            params[i] = Double.parseDouble(String.valueOf(paramsList.get(i)));
        }

        double desiredRetention = Double.parseDouble(String.valueOf(source.get("desired_retention")));

        List<Object> lStepsSec = (List<Object>) source.get("learning_steps");
        List<Duration> learning = new ArrayList<>(lStepsSec.size());
        for (Object o : lStepsSec) {
            learning.add(Duration.ofSeconds(Long.parseLong(String.valueOf(o))));
        }

        List<Object> rStepsSec = (List<Object>) source.get("relearning_steps");
        List<Duration> relearning = new ArrayList<>(rStepsSec.size());
        for (Object o : rStepsSec) {
            relearning.add(Duration.ofSeconds(Long.parseLong(String.valueOf(o))));
        }

        int maximumInterval = Integer.parseInt(String.valueOf(source.get("maximum_interval")));
        boolean enableFuzzing = Boolean.parseBoolean(String.valueOf(source.get("enable_fuzzing")));

        return new Scheduler(params, desiredRetention, learning, relearning, maximumInterval, enableFuzzing);
    }

    // 计算卡片可回忆率
    public double getCardRetrievability(Card card, ZonedDateTime currentDatetime) {
        if (card.lastReview == null) {
            return 0.0;
        }
        if (currentDatetime == null) {
            currentDatetime = ZonedDateTime.now(ZoneOffset.UTC);
        }
        long elapsedDays = Math.max(0, (long) java.time.Duration.between(card.lastReview, currentDatetime).toDays());
        return Math.pow(1 + this._FACTOR * (elapsedDays / card.stability), this._DECAY);
    }

    // 复习卡片
    public Map.Entry<Card, ReviewLog> reviewCard(Card inputCard,
                                                  ReviewLog.Rating rating,
                                                  ZonedDateTime reviewDatetime,
                                                  Integer reviewDuration) {
        if (reviewDatetime != null) {
            if (reviewDatetime.getOffset() == null || !reviewDatetime.getOffset().equals(ZoneOffset.UTC)) {
                throw new IllegalArgumentException("datetime must be timezone-aware and set to UTC");
            }
        }

        Card card = cloneCard(inputCard);

        if (reviewDatetime == null) {
            reviewDatetime = ZonedDateTime.now(ZoneOffset.UTC);
        }

        Long daysSinceLastReview = (card.lastReview == null) ? null :
                java.time.Duration.between(card.lastReview, reviewDatetime).toDays();

        Duration nextInterval;

        switch (card.state) {
            case Learning: {
                if (card.stability == null && card.difficulty == null) {
                    card.stability = _initial_stability(rating);
                    card.difficulty = _initial_difficulty(rating);
                } else if (daysSinceLastReview != null && daysSinceLastReview < 1) {
                    card.stability = _short_term_stability(card.stability, rating);
                    card.difficulty = _next_difficulty(card.difficulty, rating);
                } else {
                    card.stability = _next_stability(card.difficulty, card.stability,
                            getCardRetrievability(card, reviewDatetime), rating);
                    card.difficulty = _next_difficulty(card.difficulty, rating);
                }

                if (this.learningSteps.isEmpty()
                        || (card.step != null && card.step >= this.learningSteps.size()
                        && (rating == ReviewLog.Rating.Hard || rating == ReviewLog.Rating.Good || rating == ReviewLog.Rating.Easy))) {
                    card.state = Card.State.Review;
                    card.step = null;
                    int days = _next_interval(card.stability);
                    nextInterval = Duration.ofDays(days);
                } else {
                    switch (rating) {
                        case Again:
                            card.step = 0;
                            nextInterval = this.learningSteps.get(card.step);
                            break;
                        case Hard:
                            if (card.step != null && card.step == 0 && this.learningSteps.size() == 1) {
                                nextInterval = this.learningSteps.get(0).multipliedBy(3).dividedBy(2); // *1.5
                            } else if (card.step != null && card.step == 0 && this.learningSteps.size() >= 2) {
                                long s = (this.learningSteps.get(0).getSeconds() + this.learningSteps.get(1).getSeconds()) / 2;
                                nextInterval = Duration.ofSeconds(s);
                            } else {
                                nextInterval = this.learningSteps.get(card.step == null ? 0 : card.step);
                            }
                            break;
                        case Good:
                            if (card.step != null && card.step + 1 == this.learningSteps.size()) {
                                card.state = Card.State.Review;
                                card.step = null;
                                int days = _next_interval(card.stability);
                                nextInterval = Duration.ofDays(days);
                            } else {
                                card.step = (card.step == null) ? 0 : card.step + 1;
                                nextInterval = this.learningSteps.get(card.step);
                            }
                            break;
                        case Easy:
                            card.state = Card.State.Review;
                            card.step = null;
                            nextInterval = Duration.ofDays(_next_interval(card.stability));
                            break;
                        default:
                            throw new IllegalStateException("Unhandled rating");
                    }
                }
                break;
            }
            case Review: {
                if (daysSinceLastReview != null && daysSinceLastReview < 1) {
                    card.stability = _short_term_stability(card.stability, rating);
                } else {
                    card.stability = _next_stability(card.difficulty, card.stability,
                            getCardRetrievability(card, reviewDatetime), rating);
                }
                card.difficulty = _next_difficulty(card.difficulty, rating);

                if (rating == ReviewLog.Rating.Again) {
                    if (this.relearningSteps.isEmpty()) {
                        nextInterval = Duration.ofDays(_next_interval(card.stability));
                    } else {
                        card.state = Card.State.Relearning;
                        card.step = 0;
                        nextInterval = this.relearningSteps.get(card.step);
                    }
                } else {
                    nextInterval = Duration.ofDays(_next_interval(card.stability));
                }
                break;
            }
            case Relearning: {
                if (daysSinceLastReview != null && daysSinceLastReview < 1) {
                    card.stability = _short_term_stability(card.stability, rating);
                    card.difficulty = _next_difficulty(card.difficulty, rating);
                } else {
                    card.stability = _next_stability(card.difficulty, card.stability,
                            getCardRetrievability(card, reviewDatetime), rating);
                    card.difficulty = _next_difficulty(card.difficulty, rating);
                }

                if (this.relearningSteps.isEmpty()
                        || (card.step != null && card.step >= this.relearningSteps.size()
                        && (rating == ReviewLog.Rating.Hard || rating == ReviewLog.Rating.Good || rating == ReviewLog.Rating.Easy))) {
                    card.state = Card.State.Review;
                    card.step = null;
                    nextInterval = Duration.ofDays(_next_interval(card.stability));
                } else {
                    switch (rating) {
                        case Again:
                            card.step = 0;
                            nextInterval = this.relearningSteps.get(card.step);
                            break;
                        case Hard:
                            if (card.step != null && card.step == 0 && this.relearningSteps.size() == 1) {
                                nextInterval = this.relearningSteps.get(0).multipliedBy(3).dividedBy(2);
                            } else if (card.step != null && card.step == 0 && this.relearningSteps.size() >= 2) {
                                long s = (this.relearningSteps.get(0).getSeconds() + this.relearningSteps.get(1).getSeconds()) / 2;
                                nextInterval = Duration.ofSeconds(s);
                            } else {
                                nextInterval = this.relearningSteps.get(card.step == null ? 0 : card.step);
                            }
                            break;
                        case Good:
                            if (card.step != null && card.step + 1 == this.relearningSteps.size()) {
                                card.state = Card.State.Review;
                                card.step = null;
                                nextInterval = Duration.ofDays(_next_interval(card.stability));
                            } else {
                                card.step = (card.step == null) ? 0 : card.step + 1;
                                nextInterval = this.relearningSteps.get(card.step);
                            }
                            break;
                        case Easy:
                            card.state = Card.State.Review;
                            card.step = null;
                            nextInterval = Duration.ofDays(_next_interval(card.stability));
                            break;
                        default:
                            throw new IllegalStateException("Unhandled rating");
                    }
                }
                break;
            }
            default:
                throw new IllegalStateException("Unhandled card state");
        }

        if (this.enableFuzzing && card.state == Card.State.Review) {
            nextInterval = getFuzzedInterval(nextInterval);
        }

        card.due = reviewDatetime.plus(nextInterval);
        card.lastReview = reviewDatetime;

        ReviewLog log = new ReviewLog(card.cardId, rating, reviewDatetime, reviewDuration);
        return new AbstractMap.SimpleImmutableEntry<>(card, log);
    }

    private static Card cloneCard(Card c) {
        return new Card(c.cardId, c.state, c.step, c.stability, c.difficulty, c.due, c.lastReview);
    }

    // clamp helpers
    private static double clampDifficulty(double difficulty) {
        return Math.max(MIN_DIFFICULTY, Math.min(MAX_DIFFICULTY, difficulty));
    }

    private static double clampStability(double stability) {
        return Math.max(STABILITY_MIN, stability);
    }

    private double _initial_stability(ReviewLog.Rating rating) {
        double s = this.parameters[rating.value - 1];
        return clampStability(s);
        // torch 分支无需处理（Java 无 torch）
    }

    private double _initial_difficulty(ReviewLog.Rating rating) {
        double d = this.parameters[4] - (Math.exp(this.parameters[5] * (rating.value - 1))) + 1.0;
        return clampDifficulty(d);
    }

    private int _next_interval(double stability) {
        double nextInterval = (stability / this._FACTOR) * (Math.pow(this.desiredRetention, 1.0 / this._DECAY) - 1.0);
        int ivl = (int) Math.round(nextInterval);
        ivl = Math.max(ivl, 1);
        ivl = Math.min(ivl, this.maximumInterval);
        return ivl;
    }

    private double _short_term_stability(double stability, ReviewLog.Rating rating) {
        double inc = Math.exp(this.parameters[17] * (rating.value - 3 + this.parameters[18]))
                * Math.pow(stability, -this.parameters[19]);

        if (rating == ReviewLog.Rating.Good || rating == ReviewLog.Rating.Easy) {
            inc = Math.max(inc, 1.0);
        }

        double s = stability * inc;
        return clampStability(s);
    }

    private double _next_difficulty(double difficulty, ReviewLog.Rating rating) {
        // _linear_damping
        java.util.function.BiFunction<Double, Double, Double> linearDamping =
                (delta, diff) -> (10.0 - diff) * delta / 9.0;

        // _mean_reversion
        java.util.function.BiFunction<Double, Double, Double> meanReversion =
                (arg1, arg2) -> this.parameters[7] * arg1 + (1 - this.parameters[7]) * arg2;

        double arg1 = _initial_difficulty(ReviewLog.Rating.Easy);
        double delta = -(this.parameters[6] * (rating.value - 3));
        double arg2 = difficulty + linearDamping.apply(delta, difficulty);

        double next = meanReversion.apply(arg1, arg2);
        return clampDifficulty(next);
    }

    private double _next_stability(double difficulty, double stability, double retrievability, ReviewLog.Rating rating) {
        double next;
        if (rating == ReviewLog.Rating.Again) {
            next = _next_forget_stability(difficulty, stability, retrievability);
        } else {
            next = _next_recall_stability(difficulty, stability, retrievability, rating);
        }
        return clampStability(next);
    }

    private double _next_forget_stability(double difficulty, double stability, double retrievability) {
        double longTerm = this.parameters[11]
                * Math.pow(difficulty, -this.parameters[12])
                * (Math.pow(stability + 1.0, this.parameters[13]) - 1.0)
                * Math.exp((1 - retrievability) * this.parameters[14]);

        double shortTerm = stability / Math.exp(this.parameters[17] * this.parameters[18]);

        return Math.min(longTerm, shortTerm);
    }

    private double _next_recall_stability(double difficulty, double stability, double retrievability, ReviewLog.Rating rating) {
        double hardPenalty = (rating == ReviewLog.Rating.Hard) ? this.parameters[15] : 1.0;
        double easyBonus = (rating == ReviewLog.Rating.Easy) ? this.parameters[16] : 1.0;

        return stability * (1.0
                + Math.exp(this.parameters[8])
                * (11.0 - difficulty)
                * Math.pow(stability, -this.parameters[9])
                * (Math.exp((1 - retrievability) * this.parameters[10]) - 1.0)
                * hardPenalty
                * easyBonus);
    }

    private Duration getFuzzedInterval(Duration interval) {
        long intervalDays = interval.toDays();
        if (intervalDays < 2.5) {
            return interval;
        }

        // _get_fuzz_range
        java.util.function.LongFunction<long[]> getFuzzRange = (days) -> {
            double delta = 1.0;
            for (FuzzRange fr : FUZZ_RANGES) {
                double minPart = Math.min(days, (long) Math.ceil(fr.end));
                delta += fr.factor * Math.max(minPart - fr.start, 0.0);
            }

            long minIvl = Math.round(days - delta);
            long maxIvl = Math.round(days + delta);

            minIvl = Math.max(2, minIvl);
            maxIvl = Math.min(maxIvl, this.maximumInterval);
            minIvl = Math.min(minIvl, maxIvl);

            return new long[]{minIvl, maxIvl};
        };

        long[] range = getFuzzRange.apply(intervalDays);
        long minIvl = range[0], maxIvl = range[1];

        double r = Math.random();
        double fuzzed = r * (maxIvl - minIvl + 1) + minIvl;
        long fuzzedDays = Math.min(Math.round(fuzzed), this.maximumInterval);

        return Duration.ofDays(fuzzedDays);
    }
}