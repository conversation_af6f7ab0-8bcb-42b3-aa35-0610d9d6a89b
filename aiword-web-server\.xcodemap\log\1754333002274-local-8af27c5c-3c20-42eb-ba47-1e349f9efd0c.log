[2025-08-05 02:43:22.276] [WARN] [main] - 检测到 JRebel，发现相关类: org.zeroturnaround.javarebel.integration.cxf.CXFPlugin [o.a.x.a.x.X]
[2025-08-05 02:43:22.279] [WARN] [main] - [XCodeMap] 检测到 JRebel，发现相关类: org.zeroturnaround.javarebel.integration.cxf.CXFPlugin
 [o.a.x.a.p]
[2025-08-05 02:43:22.280] [WARN] [main] - [XCodeMap] 开始启动, pid:1788, xcodemap.executionId:8af27c5c-3c20-42eb-ba47-1e349f9efd0c, version:1.16.0
 [o.a.x.a.p]
[2025-08-05 02:43:22.284] [WARN] [main] - workDir:D:\MyApplication\love-word\aiword-web-server\.xcodemap agentMain:false [o.a.x.a.p]
[2025-08-05 02:43:22.458] [WARN] [main] - AsmSysChangeData 加载完成，共加载 58 个集合类，67 个系统设置方法，147 个集合变更方法 [o.a.x.a.x.X]
[2025-08-05 02:43:22.466] [WARN] [XCodeMap-Install] - [XCodeMap] Install Agent, version:1.16.0
 [o.a.x.a.p]
[2025-08-05 02:43:22.467] [WARN] [XCodeMap-Install] - Start retransformAllClasses [o.a.x.a.x.X]
[2025-08-05 02:43:22.475] [WARN] [XCodeMap-Install] - ReTransform agentmain metrics other classNum:0 [o.a.x.a.x.X]
[2025-08-05 02:43:22.478] [WARN] [XCodeMap-Install] - [XCodeMap] ReTransform class num:0
 [o.a.x.a.p]
[2025-08-05 02:43:22.478] [WARN] [XCodeMap-Install] - [XCodeMap] All 0 batches completed, total time: 0ms
 [o.a.x.a.p]
[2025-08-05 02:43:22.478] [WARN] [XCodeMap-Install] - End retransformAllClasses, cost 11 ms [o.a.x.a.x.X]
[2025-08-05 02:43:22.478] [WARN] [XCodeMap-Install] - [XCodeMap] Install Agent finished, cost 12 ms, version:1.16.0
 [o.a.x.a.p]
[2025-08-05 02:43:22.559] [WARN] [main] - [XCodeMap] 启动完成, cost 328 ms, version:1.16.0
 [o.a.x.a.p]
[2025-08-05 02:43:22.710] [WARN] [XCodeMap-Trans-4] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.B [o.a.x.a.v]
[2025-08-05 02:43:22.711] [WARN] [XCodeMap-Trans-4] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.C [o.a.x.a.v]
[2025-08-05 02:43:22.711] [WARN] [XCodeMap-Trans-4] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.A [o.a.x.a.v]
[2025-08-05 02:43:22.718] [WARN] [XCodeMap-Trans-4] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.q [o.a.x.a.v]
[2025-08-05 02:43:22.719] [WARN] [XCodeMap-Trans-4] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.y [o.a.x.a.v]
[2025-08-05 02:43:22.719] [WARN] [XCodeMap-Trans-4] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.V [o.a.x.a.v]
[2025-08-05 02:43:22.721] [WARN] [XCodeMap-Trans-4] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.P [o.a.x.a.v]
[2025-08-05 02:43:22.736] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:24, classes:1 [o.a.x.a.x.X]
[2025-08-05 02:43:22.749] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:13, classes:1 [o.a.x.a.x.X]
[2025-08-05 02:43:22.760] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:10, classes:1 [o.a.x.a.x.X]
[2025-08-05 02:43:22.777] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:17, classes:1 [o.a.x.a.x.X]
[2025-08-05 02:43:22.791] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:14, classes:1 [o.a.x.a.x.X]
[2025-08-05 02:43:22.811] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:19, classes:1 [o.a.x.a.x.X]
[2025-08-05 02:43:22.824] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:13, classes:1 [o.a.x.a.x.X]
[2025-08-05 02:43:23.133] [WARN] [XCodeMap-Trans-3] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.org.a.a.o [o.a.x.a.v]
[2025-08-05 02:43:23.134] [WARN] [XCodeMap-Trans-3] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.org.a.a.n [o.a.x.a.v]
[2025-08-05 02:43:23.150] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:13, classes:1 [o.a.x.a.x.X]
[2025-08-05 02:43:23.188] [WARN] [XCodeMap-Trans-9] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.Y [o.a.x.a.v]
[2025-08-05 02:43:23.374] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: jakarta.servlet.Servlet:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> jakarta.servlet.Servlet:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef [o.a.x.a.x.X]
[2025-08-05 02:43:23.377] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: jakarta.servlet.GenericServlet:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> jakarta.servlet.Servlet:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef [o.a.x.a.x.X]
[2025-08-05 02:43:23.377] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: jakarta.servlet.Servlet:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> jakarta.servlet.Servlet:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef [o.a.x.a.x.X]
[2025-08-05 02:43:23.377] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: org.springframework.web.servlet.DispatcherServlet:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> jakarta.servlet.Servlet:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef [o.a.x.a.x.X]
[2025-08-05 02:43:23.377] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: org.springframework.web.servlet.FrameworkServlet:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> jakarta.servlet.Servlet:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef [o.a.x.a.x.X]
[2025-08-05 02:43:23.377] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: jakarta.servlet.http.HttpServlet:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> jakarta.servlet.Servlet:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef [o.a.x.a.x.X]
[2025-08-05 02:43:23.377] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: org.springframework.web.servlet.HttpServletBean:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> jakarta.servlet.Servlet:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef [o.a.x.a.x.X]
[2025-08-05 02:43:24.518] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.h [o.a.x.a.v]
[2025-08-05 02:43:24.519] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.m [o.a.x.a.v]
[2025-08-05 02:43:24.523] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.m$a [o.a.x.a.v]
[2025-08-05 02:43:24.523] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.l [o.a.x.a.v]
[2025-08-05 02:43:24.524] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.k [o.a.x.a.v]
[2025-08-05 02:43:24.524] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.n [o.a.x.a.v]
[2025-08-05 02:43:24.533] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.m [o.a.x.a.v]
[2025-08-05 02:43:24.535] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.d [o.a.x.a.v]
[2025-08-05 02:43:24.535] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.i [o.a.x.a.v]
[2025-08-05 02:43:24.536] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.h [o.a.x.a.v]
[2025-08-05 02:43:24.536] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:12, classes:1 [o.a.x.a.x.X]
[2025-08-05 02:43:24.536] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.e [o.a.x.a.v]
[2025-08-05 02:43:24.546] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.i [o.a.x.a.v]
[2025-08-05 02:43:24.548] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:12, classes:1 [o.a.x.a.x.X]
[2025-08-05 02:43:24.548] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.b [o.a.x.a.v]
[2025-08-05 02:43:24.558] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.q [o.a.x.a.v]
[2025-08-05 02:43:24.559] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.a.v [o.a.x.a.v]
[2025-08-05 02:43:24.560] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.j [o.a.x.a.v]
[2025-08-05 02:43:24.561] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.f [o.a.x.a.v]
[2025-08-05 02:43:24.562] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.r [o.a.x.a.v]
[2025-08-05 02:43:24.562] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:14, classes:1 [o.a.x.a.x.X]
[2025-08-05 02:43:24.572] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.n [o.a.x.a.v]
[2025-08-05 02:43:24.572] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.a.i [o.a.x.a.v]
[2025-08-05 02:43:24.573] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.a.a [o.a.x.a.v]
[2025-08-05 02:43:24.573] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.a.b [o.a.x.a.v]
[2025-08-05 02:43:24.574] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.a.t [o.a.x.a.v]
[2025-08-05 02:43:24.574] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.l [o.a.x.a.v]
[2025-08-05 02:43:24.575] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:13, classes:1 [o.a.x.a.x.X]
[2025-08-05 02:43:24.575] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.h [o.a.x.a.v]
[2025-08-05 02:43:24.576] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.g [o.a.x.a.v]
[2025-08-05 02:43:24.576] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.p [o.a.x.a.v]
[2025-08-05 02:43:24.584] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.a.u [o.a.x.a.v]
[2025-08-05 02:43:24.585] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.a.o [o.a.x.a.v]
[2025-08-05 02:43:24.585] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.a.k [o.a.x.a.v]
[2025-08-05 02:43:24.585] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.a.n [o.a.x.a.v]
[2025-08-05 02:43:24.586] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.a.s [o.a.x.a.v]
[2025-08-05 02:43:24.586] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.a.f [o.a.x.a.v]
[2025-08-05 02:43:24.586] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.a.e [o.a.x.a.v]
[2025-08-05 02:43:24.586] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.a.l [o.a.x.a.v]
[2025-08-05 02:43:24.586] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.a.g [o.a.x.a.v]
[2025-08-05 02:43:24.586] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.a.r [o.a.x.a.v]
[2025-08-05 02:43:24.588] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.j$a [o.a.x.a.v]
[2025-08-05 02:43:24.588] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:13, classes:1 [o.a.x.a.x.X]
[2025-08-05 02:43:24.588] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.a [o.a.x.a.v]
[2025-08-05 02:43:24.597] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.s [o.a.x.a.v]
[2025-08-05 02:43:24.597] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.k [o.a.x.a.v]
[2025-08-05 02:43:24.598] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.b.c [o.a.x.a.v]
[2025-08-05 02:43:24.599] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.m$d [o.a.x.a.v]
[2025-08-05 02:43:24.600] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.ab [o.a.x.a.v]
[2025-08-05 02:43:24.600] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.b.b [o.a.x.a.v]
[2025-08-05 02:43:24.602] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:14, classes:1 [o.a.x.a.x.X]
[2025-08-05 02:43:24.611] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.b.c [o.a.x.a.v]
[2025-08-05 02:43:24.614] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.b.e [o.a.x.a.v]
[2025-08-05 02:43:24.616] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.b.f [o.a.x.a.v]
[2025-08-05 02:43:24.616] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:14, classes:1 [o.a.x.a.x.X]
[2025-08-05 02:43:24.626] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.b.a [o.a.x.a.v]
[2025-08-05 02:43:24.626] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.b.f$a [o.a.x.a.v]
[2025-08-05 02:43:24.627] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.b.a$c [o.a.x.a.v]
[2025-08-05 02:43:24.628] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.b.a$d [o.a.x.a.v]
[2025-08-05 02:43:24.628] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:12, classes:1 [o.a.x.a.x.X]
[2025-08-05 02:43:24.638] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.b.d [o.a.x.a.v]
[2025-08-05 02:43:24.639] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.b.d$d [o.a.x.a.v]
[2025-08-05 02:43:24.639] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.b.d$e [o.a.x.a.v]
[2025-08-05 02:43:24.639] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.b.d$j [o.a.x.a.v]
[2025-08-05 02:43:24.640] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.b.d$a [o.a.x.a.v]
[2025-08-05 02:43:24.640] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.b.d$h [o.a.x.a.v]
[2025-08-05 02:43:24.640] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.b.d$g [o.a.x.a.v]
[2025-08-05 02:43:24.642] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.ac$h [o.a.x.a.v]
[2025-08-05 02:43:24.643] [WARN] [XCodeMap-Trans-0] - Change loadClass by javassist for classname:org.springframework.context.support.ContextTypeMatchClassLoader [o.a.x.a.i]
[2025-08-05 02:43:24.643] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:14, classes:1 [o.a.x.a.x.X]
[2025-08-05 02:43:24.657] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:14, classes:1 [o.a.x.a.x.X]
[2025-08-05 02:43:24.673] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:15, classes:1 [o.a.x.a.x.X]
[2025-08-05 02:43:24.689] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:16, classes:1 [o.a.x.a.x.X]
[2025-08-05 02:43:24.703] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:14, classes:1 [o.a.x.a.x.X]
[2025-08-05 02:43:24.720] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:17, classes:1 [o.a.x.a.x.X]
[2025-08-05 02:43:24.737] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:17, classes:1 [o.a.x.a.x.X]
[2025-08-05 02:43:24.753] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:16, classes:1 [o.a.x.a.x.X]
[2025-08-05 02:43:24.767] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:14, classes:1 [o.a.x.a.x.X]
[2025-08-05 02:43:24.780] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:13, classes:1 [o.a.x.a.x.X]
[2025-08-05 02:43:24.794] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:14, classes:1 [o.a.x.a.x.X]
[2025-08-05 02:43:24.806] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:12, classes:1 [o.a.x.a.x.X]
[2025-08-05 02:43:24.818] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:12, classes:1 [o.a.x.a.x.X]
[2025-08-05 02:43:24.829] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:10, classes:1 [o.a.x.a.x.X]
[2025-08-05 02:43:24.843] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:14, classes:1 [o.a.x.a.x.X]
[2025-08-05 02:43:24.860] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:17, classes:1 [o.a.x.a.x.X]
[2025-08-05 02:43:24.872] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:12, classes:1 [o.a.x.a.x.X]
[2025-08-05 02:43:24.884] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:12, classes:1 [o.a.x.a.x.X]
[2025-08-05 02:43:24.894] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:10, classes:1 [o.a.x.a.x.X]
[2025-08-05 02:43:24.906] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:12, classes:1 [o.a.x.a.x.X]
[2025-08-05 02:43:24.918] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:12, classes:1 [o.a.x.a.x.X]
[2025-08-05 02:43:24.929] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:11, classes:1 [o.a.x.a.x.X]
[2025-08-05 02:43:24.939] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:10, classes:1 [o.a.x.a.x.X]
[2025-08-05 02:43:24.952] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:13, classes:1 [o.a.x.a.x.X]
[2025-08-05 02:43:24.963] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:11, classes:1 [o.a.x.a.x.X]
[2025-08-05 02:43:24.974] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:10, classes:1 [o.a.x.a.x.X]
[2025-08-05 02:43:24.986] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:11, classes:1 [o.a.x.a.x.X]
[2025-08-05 02:43:25.004] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:18, classes:1 [o.a.x.a.x.X]
[2025-08-05 02:43:25.019] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:15, classes:1 [o.a.x.a.x.X]
[2025-08-05 02:43:25.030] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:11, classes:1 [o.a.x.a.x.X]
[2025-08-05 02:43:25.046] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:16, classes:1 [o.a.x.a.x.X]
[2025-08-05 02:43:25.061] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:15, classes:1 [o.a.x.a.x.X]
[2025-08-05 02:43:25.072] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:11, classes:1 [o.a.x.a.x.X]
[2025-08-05 02:43:25.083] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:11, classes:1 [o.a.x.a.x.X]
[2025-08-05 02:43:25.094] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:11, classes:1 [o.a.x.a.x.X]
[2025-08-05 02:43:25.105] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:11, classes:1 [o.a.x.a.x.X]
[2025-08-05 02:43:25.116] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:10, classes:1 [o.a.x.a.x.X]
[2025-08-05 02:43:25.126] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:10, classes:1 [o.a.x.a.x.X]
[2025-08-05 02:43:25.137] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:11, classes:1 [o.a.x.a.x.X]
[2025-08-05 02:43:25.147] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:10, classes:1 [o.a.x.a.x.X]
[2025-08-05 02:43:25.159] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:12, classes:1 [o.a.x.a.x.X]
[2025-08-05 02:43:25.170] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:11, classes:1 [o.a.x.a.x.X]
[2025-08-05 02:43:25.181] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:11, classes:1 [o.a.x.a.x.X]
[2025-08-05 02:43:27.092] [WARN] [XCodeMap-Trans-4] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.af [o.a.x.a.v]
[2025-08-05 02:43:27.109] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:15, classes:1 [o.a.x.a.x.X]
[2025-08-05 02:43:27.368] [WARN] [XCodeMap-Trans-5] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.t [o.a.x.a.v]
[2025-08-05 02:43:27.390] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:21, classes:1 [o.a.x.a.x.X]
[2025-08-05 02:43:27.988] [WARN] [XCodeMap-Trans-1] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.org.a.a.c$a [o.a.x.a.v]
[2025-08-05 02:43:27.989] [WARN] [XCodeMap-Trans-1] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.org.a.a.l [o.a.x.a.v]
[2025-08-05 02:43:28.007] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:17, classes:1 [o.a.x.a.x.X]
[2025-08-05 02:43:28.023] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:16, classes:1 [o.a.x.a.x.X]
[2025-08-05 02:43:28.283] [WARN] [XCodeMap-Trans-0] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.U [o.a.x.a.v]
[2025-08-05 02:43:28.284] [WARN] [XCodeMap-Trans-0] - Method is not accessible:org/dromara/demo/service/impl/ExportExcelServiceImpl.setPData(Lorg/dromara/demo/service/impl/ExportExcelServiceImpl$DemoCityData;)V [o.a.x.a.u]
[2025-08-05 02:43:28.300] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:16, classes:1 [o.a.x.a.x.X]
[2025-08-05 02:43:28.568] [WARN] [XCodeMap-Trans-1] - Method is not accessible:org/dromara/love/handler/DisturbOptionsHandler.ordinal()I [o.a.x.a.u]
[2025-08-05 02:43:28.568] [WARN] [XCodeMap-Trans-1] - Method is not accessible:org/dromara/love/handler/DisturbOptionsHandler.ordinal()I [o.a.x.a.u]
[2025-08-05 02:43:28.569] [WARN] [XCodeMap-Trans-1] - Method is not accessible:org/dromara/love/handler/DisturbOptionsHandler.ordinal()I [o.a.x.a.u]
[2025-08-05 02:43:28.599] [WARN] [XCodeMap-Trans-1] - Trigger loading new class from xcodemap trans thread group, java.nio.file.StandardCopyOption [o.a.x.a.v]
[2025-08-05 02:43:28.615] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:15, classes:1 [o.a.x.a.x.X]
[2025-08-05 02:43:29.964] [WARN] [XCodeMap-Trans-6] - Method is not accessible:org/dromara/love/service/impl/LoveUserTestBookWordsRecordServiceImpl.calculateSuitableDifficultyLevel(Lorg/dromara/love/service/impl/LoveUserTestBookWordsRecordServiceImpl$TestScoreResult;Lorg/dromara/love/service/impl/LoveUserTestBookWordsRecordServiceImpl$TestScoreResult;Lorg/dromara/love/service/impl/LoveUserTestBookWordsRecordServiceImpl$TestScoreResult;)J [o.a.x.a.u]
[2025-08-05 02:43:30.209] [WARN] [XCodeMap-Trans-1] - Method is not accessible:org/dromara/workflow/service/impl/FlwCommonServiceImpl.ordinal()I [o.a.x.a.u]
[2025-08-05 02:43:30.264] [WARN] [XCodeMap-Trans-8] - Method is not accessible:org/dromara/workflow/service/impl/FlwTaskAssigneeServiceImpl.ordinal()I [o.a.x.a.u]
[2025-08-05 02:43:30.264] [WARN] [XCodeMap-Trans-8] - Method is not accessible:org/dromara/workflow/service/impl/FlwTaskAssigneeServiceImpl.ordinal()I [o.a.x.a.u]
[2025-08-05 02:43:31.917] [WARN] [XCodeMap-Trans-3] - Trigger loading new class from xcodemap trans thread group, xcodemap.shaded.b.a.b [o.a.x.a.v]
[2025-08-05 02:43:31.935] [WARN] [XCodeMap-ReTrans] - retransformClasses success, costMs:15, classes:1 [o.a.x.a.x.X]
[2025-08-05 02:43:31.986] [WARN] [XCodeMap-Trans-7] - Method is not accessible:org/dromara/common/log/aspect/LogAspect.ordinal()I [o.a.x.a.u]
[2025-08-05 02:43:31.986] [WARN] [XCodeMap-Trans-7] - Method is not accessible:org/dromara/common/log/aspect/LogAspect.ordinal()I [o.a.x.a.u]
[2025-08-05 02:43:31.987] [WARN] [XCodeMap-Trans-7] - Method is not accessible:org/dromara/common/log/aspect/LogAspect.ordinal()I [o.a.x.a.u]
[2025-08-05 02:43:31.987] [WARN] [XCodeMap-Trans-7] - Method is not accessible:org/dromara/common/log/aspect/LogAspect.ordinal()I [o.a.x.a.u]
[2025-08-05 02:43:33.274] [WARN] [XCodeMap-Trans-6] - Skip clinit method of interface, classname:org/dromara/common/mybatis/core/mapper/BaseMapperPlus method:<clinit> desc:()V [o.a.x.a.u]
[2025-08-05 02:43:34.524] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: java.sql.Connection:jdk.internal.loader.ClassLoaders$PlatformClassLoader@3cc2931c -> java.sql.Connection:jdk.internal.loader.ClassLoaders$PlatformClassLoader@3cc2931c [o.a.x.a.x.X]
[2025-08-05 02:43:34.528] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: java.sql.Connection:jdk.internal.loader.ClassLoaders$PlatformClassLoader@3cc2931c -> java.sql.Connection:jdk.internal.loader.ClassLoaders$PlatformClassLoader@3cc2931c [o.a.x.a.x.X]
[2025-08-05 02:43:36.554] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: org.zeroturnaround.javarebel.integration.fileservlet.JakartaFileServlet:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> jakarta.servlet.Servlet:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef [o.a.x.a.x.X]
[2025-08-05 02:43:36.554] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: io.undertow.servlet.handlers.DefaultServlet:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> jakarta.servlet.Servlet:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef [o.a.x.a.x.X]
[2025-08-05 02:43:36.554] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: com.mysql.cj.jdbc.JdbcConnection:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> java.sql.Connection:jdk.internal.loader.ClassLoaders$PlatformClassLoader@3cc2931c [o.a.x.a.x.X]
[2025-08-05 02:43:38.579] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: com.mysql.cj.jdbc.ConnectionImpl:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> java.sql.Connection:jdk.internal.loader.ClassLoaders$PlatformClassLoader@3cc2931c [o.a.x.a.x.X]
[2025-08-05 02:43:38.579] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: java.sql.Statement:jdk.internal.loader.ClassLoaders$PlatformClassLoader@3cc2931c -> java.sql.Statement:jdk.internal.loader.ClassLoaders$PlatformClassLoader@3cc2931c [o.a.x.a.x.X]
[2025-08-05 02:43:38.579] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: com.mysql.cj.jdbc.JdbcStatement:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> java.sql.Statement:jdk.internal.loader.ClassLoaders$PlatformClassLoader@3cc2931c [o.a.x.a.x.X]
[2025-08-05 02:43:38.582] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: java.sql.PreparedStatement:jdk.internal.loader.ClassLoaders$PlatformClassLoader@3cc2931c -> java.sql.Statement:jdk.internal.loader.ClassLoaders$PlatformClassLoader@3cc2931c [o.a.x.a.x.X]
[2025-08-05 02:43:38.582] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: java.sql.CallableStatement:jdk.internal.loader.ClassLoaders$PlatformClassLoader@3cc2931c -> java.sql.Statement:jdk.internal.loader.ClassLoaders$PlatformClassLoader@3cc2931c [o.a.x.a.x.X]
[2025-08-05 02:43:38.582] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: java.sql.Statement:jdk.internal.loader.ClassLoaders$PlatformClassLoader@3cc2931c -> java.sql.Statement:jdk.internal.loader.ClassLoaders$PlatformClassLoader@3cc2931c [o.a.x.a.x.X]
[2025-08-05 02:43:38.582] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: com.mysql.cj.jdbc.JdbcStatement:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> java.sql.Statement:jdk.internal.loader.ClassLoaders$PlatformClassLoader@3cc2931c [o.a.x.a.x.X]
[2025-08-05 02:43:39.597] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: com.mysql.cj.jdbc.JdbcPreparedStatement:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> java.sql.Statement:jdk.internal.loader.ClassLoaders$PlatformClassLoader@3cc2931c [o.a.x.a.x.X]
[2025-08-05 02:43:39.597] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: com.mysql.cj.jdbc.StatementImpl:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> java.sql.Statement:jdk.internal.loader.ClassLoaders$PlatformClassLoader@3cc2931c [o.a.x.a.x.X]
[2025-08-05 02:43:40.394] [WARN] [XCodeMap-Trans-1] - Method is not accessible:com/mysql/cj/jdbc/result/ResultSetImpl.ordinal()I [o.a.x.a.u]
[2025-08-05 02:43:40.397] [WARN] [XCodeMap-Trans-1] - Method is not accessible:com/mysql/cj/jdbc/result/ResultSetImpl.ordinal()I [o.a.x.a.u]
[2025-08-05 02:43:40.611] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: com.mysql.cj.jdbc.result.ResultSetInternalMethods:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> java.sql.ResultSet:jdk.internal.loader.ClassLoaders$PlatformClassLoader@3cc2931c [o.a.x.a.x.X]
[2025-08-05 02:43:40.612] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: java.sql.ResultSet:jdk.internal.loader.ClassLoaders$PlatformClassLoader@3cc2931c -> java.sql.ResultSet:jdk.internal.loader.ClassLoaders$PlatformClassLoader@3cc2931c [o.a.x.a.x.X]
[2025-08-05 02:43:40.612] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: com.mysql.cj.jdbc.ServerPreparedStatement:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> java.sql.Statement:jdk.internal.loader.ClassLoaders$PlatformClassLoader@3cc2931c [o.a.x.a.x.X]
[2025-08-05 02:43:40.612] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: com.mysql.cj.jdbc.ClientPreparedStatement:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> java.sql.Statement:jdk.internal.loader.ClassLoaders$PlatformClassLoader@3cc2931c [o.a.x.a.x.X]
[2025-08-05 02:43:40.616] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: com.mysql.cj.jdbc.result.ResultSetInternalMethods:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> java.sql.ResultSet:jdk.internal.loader.ClassLoaders$PlatformClassLoader@3cc2931c [o.a.x.a.x.X]
[2025-08-05 02:43:40.616] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: java.sql.ResultSet:jdk.internal.loader.ClassLoaders$PlatformClassLoader@3cc2931c -> java.sql.ResultSet:jdk.internal.loader.ClassLoaders$PlatformClassLoader@3cc2931c [o.a.x.a.x.X]
[2025-08-05 02:43:40.762] [WARN] [XCodeMap-Trans-1] - Method is not accessible:com/mysql/cj/jdbc/result/UpdatableResultSet.ordinal()I [o.a.x.a.u]
[2025-08-05 02:43:40.767] [WARN] [XCodeMap-Trans-1] - Method is not accessible:com/mysql/cj/jdbc/result/UpdatableResultSet.ordinal()I [o.a.x.a.u]
[2025-08-05 02:43:41.625] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: com.mysql.cj.jdbc.result.ResultSetImpl:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> java.sql.ResultSet:jdk.internal.loader.ClassLoaders$PlatformClassLoader@3cc2931c [o.a.x.a.x.X]
[2025-08-05 02:43:41.625] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: com.mysql.cj.jdbc.result.UpdatableResultSet:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> java.sql.ResultSet:jdk.internal.loader.ClassLoaders$PlatformClassLoader@3cc2931c [o.a.x.a.x.X]
[2025-08-05 02:44:19.996] [WARN] [XCodeMap-Trans-9] - Error getting class modifier for not found class:org/noear/snack/core/Feature [o.a.x.a.u]
[2025-08-05 02:44:19.996] [WARN] [XCodeMap-Trans-9] - Method is not accessible:org/dromara/warm/plugin/json/JsonConvertSnack.add([Lorg/noear/snack/core/Feature;)Lorg/noear/snack/core/Options; [o.a.x.a.u]
[2025-08-05 02:44:19.997] [WARN] [XCodeMap-Trans-9] - Error getting class modifier for not found class:org/noear/snack/core/Options [o.a.x.a.u]
[2025-08-05 02:44:19.997] [WARN] [XCodeMap-Trans-9] - Method is not accessible:org/dromara/warm/plugin/json/JsonConvertSnack.stringify(Ljava/lang/Object;Lorg/noear/snack/core/Options;)Ljava/lang/String; [o.a.x.a.u]
[2025-08-05 02:44:20.006] [WARN] [XCodeMap-Trans-5] - Skip clinit method of interface, classname:org/dromara/warm/flow/core/condition/ConditionStrategy method:<clinit> desc:()V [o.a.x.a.u]
[2025-08-05 02:44:20.021] [WARN] [XCodeMap-Trans-1] - Skip clinit method of interface, classname:org/dromara/warm/flow/core/variable/VariableStrategy method:<clinit> desc:()V [o.a.x.a.u]
[2025-08-05 02:44:20.026] [WARN] [XCodeMap-Trans-3] - Skip clinit method of interface, classname:org/dromara/warm/flow/core/listener/ListenerStrategy method:<clinit> desc:()V [o.a.x.a.u]
[2025-08-05 02:44:22.173] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: com.zaxxer.hikari.pool.ProxyStatement:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> java.sql.Statement:jdk.internal.loader.ClassLoaders$PlatformClassLoader@3cc2931c [o.a.x.a.x.X]
[2025-08-05 02:44:22.173] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: com.zaxxer.hikari.pool.ProxyResultSet:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> java.sql.ResultSet:jdk.internal.loader.ClassLoaders$PlatformClassLoader@3cc2931c [o.a.x.a.x.X]
[2025-08-05 02:44:22.173] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: com.p6spy.engine.wrapper.ResultSetWrapper:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> java.sql.ResultSet:jdk.internal.loader.ClassLoaders$PlatformClassLoader@3cc2931c [o.a.x.a.x.X]
[2025-08-05 02:44:22.173] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: jdk.proxy3.$Proxy277:jdk.internal.loader.ClassLoaders$PlatformClassLoader@3cc2931c -> java.sql.Connection:jdk.internal.loader.ClassLoaders$PlatformClassLoader@3cc2931c [o.a.x.a.x.X]
[2025-08-05 02:44:22.173] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: com.zaxxer.hikari.pool.ProxyPreparedStatement:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> java.sql.Statement:jdk.internal.loader.ClassLoaders$PlatformClassLoader@3cc2931c [o.a.x.a.x.X]
[2025-08-05 02:44:22.173] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: com.p6spy.engine.wrapper.ConnectionWrapper:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> java.sql.Connection:jdk.internal.loader.ClassLoaders$PlatformClassLoader@3cc2931c [o.a.x.a.x.X]
[2025-08-05 02:44:22.173] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: com.zaxxer.hikari.pool.HikariProxyConnection:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> java.sql.Connection:jdk.internal.loader.ClassLoaders$PlatformClassLoader@3cc2931c [o.a.x.a.x.X]
[2025-08-05 02:44:22.173] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: com.zaxxer.hikari.pool.ProxyConnection:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> java.sql.Connection:jdk.internal.loader.ClassLoaders$PlatformClassLoader@3cc2931c [o.a.x.a.x.X]
[2025-08-05 02:44:22.173] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: com.p6spy.engine.wrapper.PreparedStatementWrapper:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> java.sql.Statement:jdk.internal.loader.ClassLoaders$PlatformClassLoader@3cc2931c [o.a.x.a.x.X]
[2025-08-05 02:44:22.173] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: com.p6spy.engine.wrapper.StatementWrapper:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> java.sql.Statement:jdk.internal.loader.ClassLoaders$PlatformClassLoader@3cc2931c [o.a.x.a.x.X]
[2025-08-05 02:44:22.173] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: com.zaxxer.hikari.pool.HikariProxyPreparedStatement:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> java.sql.Statement:jdk.internal.loader.ClassLoaders$PlatformClassLoader@3cc2931c [o.a.x.a.x.X]
[2025-08-05 02:44:22.173] [WARN] [XCodeMap-ParentClassManager] - updateChildToParentMapping: com.zaxxer.hikari.pool.HikariProxyResultSet:jdk.internal.loader.ClassLoaders$AppClassLoader@38cccef -> java.sql.ResultSet:jdk.internal.loader.ClassLoaders$PlatformClassLoader@3cc2931c [o.a.x.a.x.X]
[2025-08-05 02:46:12.013] [WARN] [Thread-4] - Graceful shutdown..., metrics totalClassNum:0	transClassNum:1242	transLoadedNum:0	objectExtractNum:0	objectTravelNum:0	objectExtractErrorNum:0	diffObjectNum:0	totalFuncNum:0	transFuncNum:19394	errorTransNum:0	instanceNum:0	totalFuncCallNum:0	storedFuncCallNum:0	totalObjectNum:0	conflictedObjectNum:0	totalObjectChangeNum:0	totalArrayChangeNum:0	totalPutFieldNum:0	totalSysSetNum:0	totalArrayCopyNum:0	totalColChangeNum:0	totalAtomicChangeNum:0	totalGetFieldNum:0	storedPutFieldNum:0	storedGetFieldNum:0	invokeMethodPosNum:0	putFieldPosNum:0	maxLocalsValue:0	maxStacksValue:0	stackNodeNum:0	transformTimeNano:0	 [o.a.x.a.x.X]
