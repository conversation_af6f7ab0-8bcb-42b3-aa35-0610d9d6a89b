package org.dromara.love.domain;

import io.github.linpeilie.AutoMapperConfig__787;
import io.github.linpeilie.BaseMapper;
import org.dromara.love.domain.bo.LoveUserTestBookWordsRecordBoToLoveUserTestBookWordsRecordMapper;
import org.dromara.love.domain.vo.LoveUserTestBookWordsRecordVo;
import org.dromara.love.domain.vo.LoveUserTestBookWordsRecordVoToLoveUserTestBookWordsRecordMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__787.class,
    uses = {LoveUserTestBookWordsRecordVoToLoveUserTestBookWordsRecordMapper.class,LoveUserTestBookWordsRecordBoToLoveUserTestBookWordsRecordMapper.class},
    imports = {}
)
public interface LoveUserTestBookWordsRecordToLoveUserTestBookWordsRecordVoMapper extends BaseMapper<LoveUserTestBookWordsRecord, LoveUserTestBookWordsRecordVo> {
}
