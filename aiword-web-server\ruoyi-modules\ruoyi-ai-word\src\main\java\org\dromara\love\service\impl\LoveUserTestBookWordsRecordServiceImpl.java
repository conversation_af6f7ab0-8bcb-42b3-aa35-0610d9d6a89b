package org.dromara.love.service.impl;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.annotation.Resource;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.dromara.love.domain.LoveUserTestBookWordsRecord;
import org.dromara.love.domain.bo.LoveUserTestBookWordsRecordBo;
import org.dromara.love.domain.vo.LoveUserTestBookWordsRecordVo;
import org.dromara.love.service.ILoveUserTestBookWordsRecordService;
import org.springframework.stereotype.Service;
import org.dromara.love.domain.bo.LoveTestAnswerSubmitBo;
import org.dromara.love.domain.vo.LoveBookTestQuestionsVo;
import org.dromara.love.domain.vo.LoveBookTestCn2EnWordVo;
import org.dromara.love.domain.vo.LoveBookTestEn2CnWordVo;
import org.dromara.love.domain.LoveBookWords;
import org.dromara.love.domain.LoveBookTextBook;
import org.dromara.love.mapper.LoveUserTestBookWordsRecordMapper;
import org.dromara.love.service.ILoveBookWordsService;
import org.dromara.love.service.ILoveBookTextBookService;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.love.handler.DisturbOptionsHandler;

import java.util.List;
import java.util.Map;
import java.util.Collection;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Random;
import java.util.HashMap;
import java.util.stream.Collectors;

/**
 * 学习-用户测试记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-20
 */
@RequiredArgsConstructor
@Service
public class LoveUserTestBookWordsRecordServiceImpl extends ServiceImpl<LoveUserTestBookWordsRecordMapper, LoveUserTestBookWordsRecord> implements ILoveUserTestBookWordsRecordService {

    private final LoveUserTestBookWordsRecordMapper baseMapper;

    @Resource
    private ILoveBookWordsService loveBookWordsService;

    @Resource
    private ILoveBookTextBookService loveBookTextBookService;

    private final DisturbOptionsHandler disturbOptionsHandler;

    /**
     * 查询学习-用户测试记录
     *
     * @param id 主键
     * @return 学习-用户测试记录
     */
    @Override
    public LoveUserTestBookWordsRecordVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询学习-用户测试记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 学习-用户测试记录分页列表
     */
    @Override
    public TableDataInfo<LoveUserTestBookWordsRecordVo> queryPageList(LoveUserTestBookWordsRecordBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<LoveUserTestBookWordsRecord> lqw = buildQueryWrapper(bo);
        Page<LoveUserTestBookWordsRecordVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的学习-用户测试记录列表
     *
     * @param bo 查询条件
     * @return 学习-用户测试记录列表
     */
    @Override
    public List<LoveUserTestBookWordsRecordVo> queryList(LoveUserTestBookWordsRecordBo bo) {
        LambdaQueryWrapper<LoveUserTestBookWordsRecord> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<LoveUserTestBookWordsRecord> buildQueryWrapper(LoveUserTestBookWordsRecordBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<LoveUserTestBookWordsRecord> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(LoveUserTestBookWordsRecord::getId);
        lqw.eq(bo.getUserId() != null, LoveUserTestBookWordsRecord::getUserId, bo.getUserId());
        lqw.eq(bo.getBookTextBookId() != null, LoveUserTestBookWordsRecord::getBookTextBookId, bo.getBookTextBookId());
        lqw.like(StringUtils.isNotBlank(bo.getBookTextBookName()), LoveUserTestBookWordsRecord::getBookTextBookName, bo.getBookTextBookName());
        lqw.eq(bo.getTestDuration() != null, LoveUserTestBookWordsRecord::getTestDuration, bo.getTestDuration());
        lqw.eq(bo.getCorrectAnswers() != null, LoveUserTestBookWordsRecord::getCorrectAnswers, bo.getCorrectAnswers());
        lqw.eq(bo.getAccuracyRate() != null, LoveUserTestBookWordsRecord::getAccuracyRate, bo.getAccuracyRate());
        lqw.eq(bo.getEn2cnAccuracy() != null, LoveUserTestBookWordsRecord::getEn2cnAccuracy, bo.getEn2cnAccuracy());
        lqw.eq(bo.getCn2enAccuracy() != null, LoveUserTestBookWordsRecord::getCn2enAccuracy, bo.getCn2enAccuracy());
        lqw.eq(bo.getAudio2cnAccuracy() != null, LoveUserTestBookWordsRecord::getAudio2cnAccuracy, bo.getAudio2cnAccuracy());
        lqw.eq(bo.getSuitableDifficultyLevel() != null, LoveUserTestBookWordsRecord::getSuitableDifficultyLevel, bo.getSuitableDifficultyLevel());
        lqw.eq(bo.getBaseScore() != null, LoveUserTestBookWordsRecord::getBaseScore, bo.getBaseScore());
        lqw.eq(bo.getDifficultyBonus() != null, LoveUserTestBookWordsRecord::getDifficultyBonus, bo.getDifficultyBonus());
        lqw.eq(bo.getTimeBonus() != null, LoveUserTestBookWordsRecord::getTimeBonus, bo.getTimeBonus());
        lqw.eq(bo.getCompletionBonus() != null, LoveUserTestBookWordsRecord::getCompletionBonus, bo.getCompletionBonus());
        lqw.eq(bo.getTotalScore() != null, LoveUserTestBookWordsRecord::getTotalScore, bo.getTotalScore());
        lqw.eq(StringUtils.isNotBlank(bo.getGradeLevel()), LoveUserTestBookWordsRecord::getGradeLevel, bo.getGradeLevel());
        lqw.eq(StringUtils.isNotBlank(bo.getGradeDesc()), LoveUserTestBookWordsRecord::getGradeDesc, bo.getGradeDesc());
        lqw.eq(StringUtils.isNotBlank(bo.getAnswerDetails()), LoveUserTestBookWordsRecord::getAnswerDetails, bo.getAnswerDetails());
        lqw.eq(StringUtils.isNotBlank(bo.getRemarks()), LoveUserTestBookWordsRecord::getRemarks, bo.getRemarks());
        lqw.eq(StringUtils.isNotBlank(bo.getTags()), LoveUserTestBookWordsRecord::getTags, bo.getTags());
        return lqw;
    }

    /**
     * 新增学习-用户测试记录
     *
     * @param bo 学习-用户测试记录
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(LoveUserTestBookWordsRecordBo bo) {
        LoveUserTestBookWordsRecord add = MapstructUtils.convert(bo, LoveUserTestBookWordsRecord.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改学习-用户测试记录
     *
     * @param bo 学习-用户测试记录
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(LoveUserTestBookWordsRecordBo bo) {
        LoveUserTestBookWordsRecord update = MapstructUtils.convert(bo, LoveUserTestBookWordsRecord.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(LoveUserTestBookWordsRecord entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除学习-用户测试记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    @Override
    public LoveUserTestBookWordsRecordVo submitTest(LoveTestAnswerSubmitBo submitBo) {
        Long userId = LoginHelper.getUserId();

        // 获取书籍信息
        LoveBookTextBook textBook = loveBookTextBookService.getById(submitBo.getBookId());
        if (textBook == null) {
            throw new RuntimeException("书籍不存在");
        }

        // 获取书籍所有单词用于对比答案
        List<LoveBookWords> bookWords = loveBookWordsService.getBookWords(submitBo.getBookId());
        Map<Long, LoveBookWords> wordsMap = bookWords.stream()
            .collect(Collectors.toMap(LoveBookWords::getId, word -> word));

        // 计算各类题型的得分
        TestScoreResult en2cnScore = calculateScore(submitBo.getEn2cnList(), wordsMap, false);
        TestScoreResult cn2enScore = calculateScore(submitBo.getCn2enList(), wordsMap, true);
        TestScoreResult audio2cnScore = calculateScore(submitBo.getAudio2cnList(), wordsMap, false);

        // 计算总体统计
        long totalQuestions = en2cnScore.totalQuestions + cn2enScore.totalQuestions + audio2cnScore.totalQuestions;
        long totalCorrect = en2cnScore.correctAnswers + cn2enScore.correctAnswers + audio2cnScore.correctAnswers;

        // 计算总体正确率
        long overallAccuracy = totalQuestions > 0 ? (totalCorrect * 100 / totalQuestions) : 0;

        // 计算各类型正确率
        long en2cnAccuracy = en2cnScore.totalQuestions > 0 ? (en2cnScore.correctAnswers * 100 / en2cnScore.totalQuestions) : 0;
        long cn2enAccuracy = cn2enScore.totalQuestions > 0 ? (cn2enScore.correctAnswers * 100 / cn2enScore.totalQuestions) : 0;
        long audio2cnAccuracy = audio2cnScore.totalQuestions > 0 ? (audio2cnScore.correctAnswers * 100 / audio2cnScore.totalQuestions) : 0;

        // 计算适合的难度级别（根据各难度级别的表现）
        long suitableDifficultyLevel = calculateSuitableDifficultyLevel(en2cnScore, cn2enScore, audio2cnScore);

        // 计算实际答题数量（有用户答案的题目）
        long answeredQuestions = 0;
        if (submitBo.getEn2cnList() != null) {
            for (LoveTestAnswerSubmitBo.TestAnswerItem answer : submitBo.getEn2cnList()) {
                if (answer.getUserAnswer() != null && !answer.getUserAnswer().trim().isEmpty()) {
                    answeredQuestions++;
                }
            }
        }
        if (submitBo.getCn2enList() != null) {
            for (LoveTestAnswerSubmitBo.TestAnswerItem answer : submitBo.getCn2enList()) {
                if (answer.getUserAnswer() != null && !answer.getUserAnswer().trim().isEmpty()) {
                    answeredQuestions++;
                }
            }
        }
        if (submitBo.getAudio2cnList() != null) {
            for (LoveTestAnswerSubmitBo.TestAnswerItem answer : submitBo.getAudio2cnList()) {
                if (answer.getUserAnswer() != null && !answer.getUserAnswer().trim().isEmpty()) {
                    answeredQuestions++;
                }
            }
        }

        // 计算分数
        ScoreResult scoreResult = calculateFinalScore(overallAccuracy, suitableDifficultyLevel, submitBo.getTestDuration(), totalQuestions, answeredQuestions);

        // 生成标签
        List<String> tags = generateTestTags(en2cnAccuracy, cn2enAccuracy, audio2cnAccuracy, overallAccuracy, submitBo.getTestDuration(), suitableDifficultyLevel);

        // 构建答案详情
        Map<String, Object> answerDetails = new HashMap<>();
        answerDetails.put("en2cnAnswers", submitBo.getEn2cnList());
        answerDetails.put("cn2enAnswers", submitBo.getCn2enList());
        answerDetails.put("audio2cnAnswers", submitBo.getAudio2cnList());
        answerDetails.put("en2cnScore", en2cnScore);
        answerDetails.put("cn2enScore", cn2enScore);
        answerDetails.put("audio2cnScore", audio2cnScore);

        // 创建测试记录
        LoveUserTestBookWordsRecord record = new LoveUserTestBookWordsRecord();
        record.setUserId(userId);
        record.setBookTextBookId(submitBo.getBookId());
        record.setBookTextBookName(textBook.getName());
        record.setTestDuration(submitBo.getTestDuration());
        record.setCorrectAnswers(totalCorrect);
        record.setAccuracyRate(overallAccuracy);
        record.setEn2cnAccuracy(en2cnAccuracy);
        record.setCn2enAccuracy(cn2enAccuracy);
        record.setAudio2cnAccuracy(audio2cnAccuracy);
        record.setSuitableDifficultyLevel(suitableDifficultyLevel);
        record.setBaseScore(scoreResult.baseScore);
        record.setDifficultyBonus(scoreResult.difficultyBonus);
        record.setTimeBonus(scoreResult.timeBonus);
        record.setCompletionBonus(scoreResult.completionBonus);
        record.setTotalScore(scoreResult.totalScore);
        record.setGradeLevel(scoreResult.gradeLevel);
        record.setGradeDesc(scoreResult.gradeDesc);
        record.setTags(String.join(",", tags));
        record.setAnswerDetails(JSONUtil.toJsonStr(answerDetails));

        // 保存记录
        baseMapper.insert(record);

        // 返回结果
        return baseMapper.selectVoById(record.getId());
    }

    /**
     * 生成测试标签
     */
    private List<String> generateTestTags(long en2cnAccuracy, long cn2enAccuracy, long audio2cnAccuracy,
                                         long overallAccuracy, long testDuration, long difficultyLevel) {
        List<String> tags = new ArrayList<>();

        // 各类型表现标签 - 确保每种类型都有一个标签
        if (en2cnAccuracy > 0) {  // 只有参与了英选汉测试才添加标签
            if (en2cnAccuracy >= 80) {
                tags.add("英选汉不错");
            } else if (en2cnAccuracy >= 60) {
                tags.add("英选汉一般");
            } else {
                tags.add("英选汉待加强");
            }
        }

        if (cn2enAccuracy > 0) {  // 只有参与了汉选英测试才添加标签
            if (cn2enAccuracy >= 80) {
                tags.add("汉选英不错");
            } else if (cn2enAccuracy >= 60) {
                tags.add("汉选英一般");
            } else {
                tags.add("汉选英待加强");
            }
        }

        if (audio2cnAccuracy > 0) {  // 只有参与了听力测试才添加标签
            if (audio2cnAccuracy >= 80) {
                tags.add("听力不错");
            } else if (audio2cnAccuracy >= 60) {
                tags.add("听力一般");
            } else {
                tags.add("听力待加强");
            }
        }

        // 整体水平标签 - 总是添加
        if (overallAccuracy >= 90) {
            tags.add("基础扎实");
        } else if (overallAccuracy >= 75) {
            tags.add("基础良好");
        } else if (overallAccuracy >= 60) {
            tags.add("基础一般");
        } else {
            tags.add("基础薄弱");
        }

        // 速度标签 - 总是添加
        if (testDuration <= 180) {
            tags.add("答题速度快");
        } else if (testDuration <= 240) {
            tags.add("答题速度适中");
        } else {
            tags.add("答题较慢");
        }

        // 难度级别标签 - 总是添加
        if (difficultyLevel >= 3) {
            tags.add("适合高难度");
        } else if (difficultyLevel == 2) {
            tags.add("适合中等难度");
        } else {
            tags.add("适合基础难度");
        }

        return tags;
    }

    /**
     * 计算题型得分
     */
    private TestScoreResult calculateScore(List<LoveTestAnswerSubmitBo.TestAnswerItem> answers, Map<Long, LoveBookWords> wordsMap, boolean isCn2En) {
        if (answers == null || answers.isEmpty()) {
            return new TestScoreResult(0, 0, new HashMap<>());
        }

        long correctAnswers = 0;
        Map<Integer, Integer> difficultyStats = new HashMap<>();

        for (LoveTestAnswerSubmitBo.TestAnswerItem answer : answers) {
            LoveBookWords word = wordsMap.get(answer.getId());
            if (word != null) {
                String correctAnswer;
                if (isCn2En) {
                    // 汉选英：正确答案是英文单词
                    correctAnswer = word.getName();
                } else {
                    // 英选汉/听选汉：正确答案是中文释义
                    correctAnswer = word.getParaphrase();
                }

                if (correctAnswer != null && correctAnswer.equals(answer.getUserAnswer())) {
                    correctAnswers++;
                }

                // 统计各难度级别
                Integer difficulty = answer.getDifficultyLevel() != null ? answer.getDifficultyLevel() : 1;
                difficultyStats.put(difficulty, difficultyStats.getOrDefault(difficulty, 0) + 1);
            }
        }

        return new TestScoreResult(answers.size(), correctAnswers, difficultyStats);
    }

    /**
     * 计算适合的难度级别
     */
    private long calculateSuitableDifficultyLevel(TestScoreResult en2cnScore, TestScoreResult cn2enScore, TestScoreResult audio2cnScore) {
        // 简单的算法：根据各个级别的平均正确率来判断适合的难度
        // 这里可以根据实际需求调整算法
        double totalCorrect = en2cnScore.correctAnswers + cn2enScore.correctAnswers + audio2cnScore.correctAnswers;
        double totalQuestions = en2cnScore.totalQuestions + cn2enScore.totalQuestions + audio2cnScore.totalQuestions;

        if (totalQuestions == 0) {
            return 1;
        }

        double accuracy = totalCorrect / totalQuestions;

        if (accuracy >= 0.9) {
            return 3; // 困难
        } else if (accuracy >= 0.7) {
            return 2; // 中等
        } else {
            return 1; // 简单
        }
    }

    /**
     * 计算最终分数
     */
    private ScoreResult calculateFinalScore(long accuracy, long difficultyLevel, long testDuration, long totalQuestions, long answeredQuestions) {
        // 基础分 (60分制)
        long baseScore = Math.min(60, accuracy * 60 / 100);

        // 难度加分 (25分制) - 优化全对时的加分逻辑
        long difficultyBonus = 0;
        if (difficultyLevel == 1) {
            difficultyBonus = accuracy == 100 ? 5 : 0; // 简单难度全对给5分
        } else if (difficultyLevel == 2) {
            difficultyBonus = accuracy == 100 ? 15 : 12; // 中等难度全对给15分，否则12分
        } else if (difficultyLevel == 3) {
            difficultyBonus = accuracy == 100 ? 25 : 20; // 困难全对给满分25分，否则20分
        }
        difficultyBonus = Math.min(25, difficultyBonus);

        // 时间加分 (10分制) - 根据用时给分，用时越短分数越高
        // 但如果用户没有答任何题，不给时间加分
        long timeBonus = 0;
        if (answeredQuestions > 0) { // 只有实际答题才给时间加分
            if (testDuration <= 30) { // 小于等于30秒直接为0分
                timeBonus = 0;
            } else if (testDuration <= 180) { // 3分钟以内
                timeBonus = 10;
            } else if (testDuration <= 240) { // 4分钟以内
                timeBonus = 7;
            } else if (testDuration <= 300) { // 5分钟以内
                timeBonus = 5;
            } else {
                timeBonus = 2;
            }
        }

        // 完整度加分 (5分制) - 根据实际答题数量计算，没有答题时不给加分
        long completionBonus = 0;
        if (totalQuestions > 0 && answeredQuestions > 0) {
            double completionRate = (double) answeredQuestions / totalQuestions;
            completionBonus = Math.round(completionRate * 5);
            completionBonus = Math.min(5, completionBonus);
        }

        // 总分 - 全对时允许超过100分
        long totalScore = baseScore + difficultyBonus + timeBonus + completionBonus;
        // 移除100分限制，允许全对时的高难度获得更高分数

        // 等级评定（学前测试场景）- 调整评级标准适应新的分数范围
        String gradeLevel;
        String gradeDesc;

        if (totalScore >= 100) {
            gradeLevel = "S++";
            gradeDesc = "完美水平，全对高难度题目，天赋卓越";
        } else if (totalScore >= 95) {
            gradeLevel = "S+";
            gradeDesc = "卓越水平，基础非常扎实，可直接学习本书高难度内容";
        } else if (totalScore >= 90) {
            gradeLevel = "S";
            gradeDesc = "优秀水平，基础扎实，建议从本书中高难度开始学习";
        } else if (totalScore >= 85) {
            gradeLevel = "A+";
            gradeDesc = "良好+水平，有一定基础，建议从本书中等难度开始";
        } else if (totalScore >= 80) {
            gradeLevel = "A";
            gradeDesc = "良好水平，基础尚可，适合从本书中等偏易开始";
        } else if (totalScore >= 75) {
            gradeLevel = "B+";
            gradeDesc = "中等+水平，基础一般，建议从本书简单难度开始";
        } else if (totalScore >= 70) {
            gradeLevel = "B";
            gradeDesc = "中等水平，基础偏弱，需要从本书基础内容开始";
        } else if (totalScore >= 60) {
            gradeLevel = "C";
            gradeDesc = "入门水平，基础薄弱，建议先学习本书基础知识";
        } else {
            gradeLevel = "D";
            gradeDesc = "起步水平，需要从本书最基础的内容开始学习";
        }

        return new ScoreResult(baseScore, difficultyBonus, timeBonus, completionBonus, totalScore, gradeLevel, gradeDesc);
    }

    /**
     * 测试得分结果
     */
    private static class TestScoreResult {
        long totalQuestions;
        long correctAnswers;
        Map<Integer, Integer> difficultyStats;

        public TestScoreResult(long totalQuestions, long correctAnswers, Map<Integer, Integer> difficultyStats) {
            this.totalQuestions = totalQuestions;
            this.correctAnswers = correctAnswers;
            this.difficultyStats = difficultyStats;
        }
    }

    /**
     * 最终分数结果
     */
    private static class ScoreResult {
        long baseScore;
        long difficultyBonus;
        long timeBonus;
        long completionBonus;
        long totalScore;
        String gradeLevel;
        String gradeDesc;

        public ScoreResult(long baseScore, long difficultyBonus, long timeBonus, long completionBonus,
                          long totalScore, String gradeLevel, String gradeDesc) {
            this.baseScore = baseScore;
            this.difficultyBonus = difficultyBonus;
            this.timeBonus = timeBonus;
            this.completionBonus = completionBonus;
            this.totalScore = totalScore;
            this.gradeLevel = gradeLevel;
            this.gradeDesc = gradeDesc;
        }
    }

    @Override
    public LoveBookTestQuestionsVo getTestContent(Long bookId) {
        // 返回实体类
        LoveBookTestQuestionsVo testQuestionsVo = new LoveBookTestQuestionsVo();
        // 英选汉总题数
        int totalEn2CnQuestionNum = 12;
        List<LoveBookTestEn2CnWordVo> loveBookTestEn2CnWordVos = new ArrayList<>();
        // 汉选英总题数
        int totalCn2EnQuestionNum = 12;
        List<LoveBookTestCn2EnWordVo> loveBookTestCn2EnWordVos = new ArrayList<>();
        // 听词选意总题数
        int totalListenQuestionNum = 12;
        List<LoveBookTestEn2CnWordVo> loveBookTestAudio2CnWordVos = new ArrayList<>();
        // 获取书籍单词列表
        List<LoveBookWords> bookWordList = loveBookWordsService.getBookWords(bookId);
        // 单词总数
        int wordCount = bookWordList.size();
        if (wordCount <= 30) {
            // 获取平均题数
            int averageQuestionNum = wordCount / 3;
            totalEn2CnQuestionNum = averageQuestionNum;
            totalCn2EnQuestionNum = averageQuestionNum;
            totalListenQuestionNum = averageQuestionNum;
        }

        // 生成英选汉题目
        List<LoveBookTestEn2CnWordVo> en2CnQuestions = generateEnglishToChineseQuestions(bookWordList, totalEn2CnQuestionNum);
        // 生成汉选英题目
        List<LoveBookTestCn2EnWordVo> cn2EnQuestions = generateChineseToEnglishQuestions(bookWordList, totalCn2EnQuestionNum);
        // 生成听词选意题目
        List<LoveBookTestEn2CnWordVo> listenQuestions = generateEnglishToChineseQuestions(bookWordList, totalListenQuestionNum);
        // 合并所有题目
        loveBookTestEn2CnWordVos.addAll(en2CnQuestions);
        loveBookTestCn2EnWordVos.addAll(cn2EnQuestions);
        loveBookTestAudio2CnWordVos.addAll(listenQuestions);

        testQuestionsVo.setEn2CnWordList(loveBookTestEn2CnWordVos);
        testQuestionsVo.setCn2EnWordList(loveBookTestCn2EnWordVos);
        testQuestionsVo.setAudio2CnWordList(loveBookTestAudio2CnWordVos);
        return testQuestionsVo;
    }

    /**
     * 生成英选汉题目
     *
     * @param bookWordList     单词列表
     * @param totalQuestionNum 总题数
     * @return 英选汉题目列表
     */
    private List<LoveBookTestEn2CnWordVo> generateEnglishToChineseQuestions(List<LoveBookWords> bookWordList, int totalQuestionNum) {
        List<LoveBookTestEn2CnWordVo> questions = new ArrayList<>();
        Random random = new Random();
        // 查找bookWordList中单词字母数量小于5的单词
        List<LoveBookWords> simple = new ArrayList<>(bookWordList.stream().filter(f -> {
            if (f.getName().length() < 5) {
                return true;
            }
            return false;
        }).toList());

        List<LoveBookWords> medium = new ArrayList<>(bookWordList.stream().filter(f -> {
            if (f.getName().length() >= 5 && f.getName().length() <= 10) {
                return true;
            }
            return false;
        }).toList());

        List<LoveBookWords> difficult = new ArrayList<>(bookWordList.stream().filter(f -> {
            if (f.getName().length() > 10) {
                return true;
            }
            return false;
        }).toList());

        // 英选汉题型：按三个难度等级平均分配题数
        int simpleQuestionNum = totalQuestionNum / 3;
        int mediumQuestionNum = totalQuestionNum / 3;
        // 确保总数正确
        int difficultQuestionNum = totalQuestionNum - simpleQuestionNum - mediumQuestionNum;

        // 从简单级别随机选择单词
        Collections.shuffle(simple, random);
        List<LoveBookWords> selectedSimple = simple.size() >= simpleQuestionNum ?
            simple.subList(0, simpleQuestionNum) : simple;
        for (LoveBookWords word : selectedSimple) {
            LoveBookTestEn2CnWordVo questionVo = new LoveBookTestEn2CnWordVo();
            questionVo.setId(word.getId());
            questionVo.setWordName(word.getName());
            // 简单级别
            questionVo.setDiffcultLevel(1);
            // 生成选项：正确答案 + 3个干扰项
            List<String> options = disturbOptionsHandler.generateOptionsFromWordList(word, bookWordList, 1, false);
            questionVo.setOptions(options);
            questions.add(questionVo);
        }

        // 从中等级别随机选择单词
        Collections.shuffle(medium, random);
        List<LoveBookWords> selectedMedium = medium.size() >= mediumQuestionNum ?
            medium.subList(0, mediumQuestionNum) : medium;
        for (LoveBookWords word : selectedMedium) {
            LoveBookTestEn2CnWordVo questionVo = new LoveBookTestEn2CnWordVo();
            questionVo.setId(word.getId());
            questionVo.setWordName(word.getName());
            // 中等级别
            questionVo.setDiffcultLevel(2);
            // 生成选项：正确答案 + 3个干扰项
            List<String> options = disturbOptionsHandler.generateOptionsFromWordList(word, bookWordList, 2, false);
            questionVo.setOptions(options);
            questions.add(questionVo);
        }

        // 从困难级别随机选择单词
        Collections.shuffle(difficult, random);
        List<LoveBookWords> selectedDifficult = difficult.size() >= difficultQuestionNum ?
            difficult.subList(0, difficultQuestionNum) : difficult;
        for (LoveBookWords word : selectedDifficult) {
            LoveBookTestEn2CnWordVo questionVo = new LoveBookTestEn2CnWordVo();
            questionVo.setId(word.getId());
            questionVo.setWordName(word.getName());
            // 困难级别
            questionVo.setDiffcultLevel(3);
            // 生成选项：正确答案 + 3个干扰项
            List<String> options = disturbOptionsHandler.generateOptionsFromWordList(word, bookWordList, 3, false);
            questionVo.setOptions(options);
            questions.add(questionVo);
        }

        return questions;
    }

    /**
     * 生成汉选英题目
     *
     * @param bookWordList     单词列表
     * @param totalQuestionNum 总题数
     * @return 汉选英题目列表
     */
    private List<LoveBookTestCn2EnWordVo> generateChineseToEnglishQuestions(List<LoveBookWords> bookWordList, int totalQuestionNum) {
        List<LoveBookTestCn2EnWordVo> questions = new ArrayList<>();
        Random random = new Random();

        // 按照相同的难度分级逻辑：简单(<5字母)、中等(5-10字母)、困难(>10字母)
        List<LoveBookWords> simple = new ArrayList<>(bookWordList.stream().filter(f -> {
            if (f.getName().length() < 5) {
                return true;
            }
            return false;
        }).toList());

        List<LoveBookWords> medium = new ArrayList<>(bookWordList.stream().filter(f -> {
            if (f.getName().length() >= 5 && f.getName().length() <= 10) {
                return true;
            }
            return false;
        }).toList());

        List<LoveBookWords> difficult = new ArrayList<>(bookWordList.stream().filter(f -> {
            if (f.getName().length() > 10) {
                return true;
            }
            return false;
        }).toList());

        // 汉选英题型：按三个难度等级平均分配题数
        int simpleQuestionNum = totalQuestionNum / 3;
        int mediumQuestionNum = totalQuestionNum / 3;
        // 确保总数正确
        int difficultQuestionNum = totalQuestionNum - simpleQuestionNum - mediumQuestionNum;

        // 从简单级别随机选择单词
        Collections.shuffle(simple, random);
        List<LoveBookWords> selectedSimple = simple.size() >= simpleQuestionNum ?
            simple.subList(0, simpleQuestionNum) : simple;
        for (LoveBookWords word : selectedSimple) {
            LoveBookTestCn2EnWordVo questionVo = new LoveBookTestCn2EnWordVo();
            questionVo.setId(word.getId());
            // 汉选英：题目显示中文释义
            questionVo.setName(word.getParaphrase());
            // 简单级别
            questionVo.setDiffcultLevel(1);
            // 生成选项：正确答案(英文单词) + 3个干扰项(英文单词)
            List<String> options = disturbOptionsHandler.generateOptionsFromWordList(word, bookWordList, 1, true);
            questionVo.setOptions(options);
            questions.add(questionVo);
        }

        // 从中等级别随机选择单词
        Collections.shuffle(medium, random);
        List<LoveBookWords> selectedMedium = medium.size() >= mediumQuestionNum ?
            medium.subList(0, mediumQuestionNum) : medium;
        for (LoveBookWords word : selectedMedium) {
            LoveBookTestCn2EnWordVo questionVo = new LoveBookTestCn2EnWordVo();
            questionVo.setId(word.getId());
            // 汉选英：题目显示中文释义
            questionVo.setName(word.getParaphrase());
            // 中等级别
            questionVo.setDiffcultLevel(2);
            // 生成选项：正确答案(英文单词) + 3个干扰项(英文单词)
            List<String> options = disturbOptionsHandler.generateOptionsFromWordList(word, bookWordList, 2, true);
            questionVo.setOptions(options);
            questions.add(questionVo);
        }

        // 从困难级别随机选择单词
        Collections.shuffle(difficult, random);
        List<LoveBookWords> selectedDifficult = difficult.size() >= difficultQuestionNum ?
            difficult.subList(0, difficultQuestionNum) : difficult;
        for (LoveBookWords word : selectedDifficult) {
            LoveBookTestCn2EnWordVo questionVo = new LoveBookTestCn2EnWordVo();
            questionVo.setId(word.getId());
            // 汉选英：题目显示中文释义
            questionVo.setName(word.getParaphrase());
            // 困难级别
            questionVo.setDiffcultLevel(3);
            // 生成选项：正确答案(英文单词) + 3个干扰项(英文单词)
            List<String> options = disturbOptionsHandler.generateOptionsFromWordList(word, bookWordList, 3, true);
            questionVo.setOptions(options);
            questions.add(questionVo);
        }

        return questions;
    }


}
