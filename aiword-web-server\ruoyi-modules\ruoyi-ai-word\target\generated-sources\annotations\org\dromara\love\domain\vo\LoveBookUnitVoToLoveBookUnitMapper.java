package org.dromara.love.domain.vo;

import io.github.linpeilie.AutoMapperConfig__787;
import io.github.linpeilie.BaseMapper;
import org.dromara.love.domain.LoveBookUnit;
import org.dromara.love.domain.LoveBookUnitToLoveBookUnitVoMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__787.class,
    uses = {LoveBookUnitToLoveBookUnitVoMapper.class},
    imports = {}
)
public interface LoveBookUnitVoToLoveBookUnitMapper extends BaseMapper<LoveBookUnitVo, LoveBookUnit> {
}
