package org.dromara.love.domain;

import io.github.linpeilie.AutoMapperConfig__787;
import io.github.linpeilie.BaseMapper;
import org.dromara.love.domain.bo.LoveBookWordsBoToLoveBookWordsMapper;
import org.dromara.love.domain.vo.LoveBookWordsVo;
import org.dromara.love.domain.vo.LoveBookWordsVoToLoveBookWordsMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__787.class,
    uses = {LoveBookWordsBoToLoveBookWordsMapper.class,LoveBookWordsVoToLoveBookWordsMapper.class},
    imports = {}
)
public interface LoveBookWordsToLoveBookWordsVoMapper extends BaseMapper<LoveBookWords, LoveBookWordsVo> {
}
