package org.dromara.love.domain;

import javax.annotation.processing.Generated;
import org.dromara.love.domain.vo.LoveBookTypeVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-07T01:55:47+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (Microsoft)"
)
@Component
public class LoveBookTypeToLoveBookTypeVoMapperImpl implements LoveBookTypeToLoveBookTypeVoMapper {

    @Override
    public LoveBookTypeVo convert(LoveBookType arg0) {
        if ( arg0 == null ) {
            return null;
        }

        LoveBookTypeVo loveBookTypeVo = new LoveBookTypeVo();

        loveBookTypeVo.setId( arg0.getId() );
        loveBookTypeVo.setName( arg0.getName() );

        return loveBookTypeVo;
    }

    @Override
    public LoveBookTypeVo convert(LoveBookType arg0, LoveBookTypeVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setName( arg0.getName() );

        return arg1;
    }
}
