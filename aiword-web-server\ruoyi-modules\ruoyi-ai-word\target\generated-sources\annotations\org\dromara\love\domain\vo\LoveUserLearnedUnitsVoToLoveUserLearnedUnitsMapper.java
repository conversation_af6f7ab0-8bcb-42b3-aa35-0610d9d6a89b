package org.dromara.love.domain.vo;

import io.github.linpeilie.AutoMapperConfig__787;
import io.github.linpeilie.BaseMapper;
import org.dromara.love.domain.LoveUserLearnedUnits;
import org.dromara.love.domain.LoveUserLearnedUnitsToLoveUserLearnedUnitsVoMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__787.class,
    uses = {LoveUserLearnedUnitsToLoveUserLearnedUnitsVoMapper.class},
    imports = {}
)
public interface LoveUserLearnedUnitsVoToLoveUserLearnedUnitsMapper extends BaseMapper<LoveUserLearnedUnitsVo, LoveUserLearnedUnits> {
}
