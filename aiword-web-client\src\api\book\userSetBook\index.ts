import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { UserSetBookVO, UserSetBookForm, UserSetBookQuery } from '@/api/love/userSetBook/types';

/**
 * 获取学习-用户设置书籍详细信息（按record_type分组）
 * @param userId
 */
export const getInfoByUserId = (userId: string | number) => {
  return request({
    url: '/love/userSetBook/' + userId,
    method: 'get'
  });
};

/**
 * 用户设置书籍（简化版）
 * @param data
 */
export const setBook = (data: UserSetBookForm) => {
  return request({
    url: '/love/userSetBook/setBook',
    method: 'post',
    data: data
  });
};