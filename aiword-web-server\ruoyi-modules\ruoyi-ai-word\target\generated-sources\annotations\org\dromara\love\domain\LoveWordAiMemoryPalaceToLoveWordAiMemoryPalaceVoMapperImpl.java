package org.dromara.love.domain;

import javax.annotation.processing.Generated;
import org.dromara.love.domain.vo.LoveWordAiMemoryPalaceVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-07T01:55:47+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (Microsoft)"
)
@Component
public class LoveWordAiMemoryPalaceToLoveWordAiMemoryPalaceVoMapperImpl implements LoveWordAiMemoryPalaceToLoveWordAiMemoryPalaceVoMapper {

    @Override
    public LoveWordAiMemoryPalaceVo convert(LoveWordAiMemoryPalace arg0) {
        if ( arg0 == null ) {
            return null;
        }

        LoveWordAiMemoryPalaceVo loveWordAiMemoryPalaceVo = new LoveWordAiMemoryPalaceVo();

        loveWordAiMemoryPalaceVo.setId( arg0.getId() );
        loveWordAiMemoryPalaceVo.setUserId( arg0.getUserId() );
        loveWordAiMemoryPalaceVo.setBookId( arg0.getBookId() );
        loveWordAiMemoryPalaceVo.setUnitId( arg0.getUnitId() );
        loveWordAiMemoryPalaceVo.setTitle( arg0.getTitle() );
        loveWordAiMemoryPalaceVo.setTheme( arg0.getTheme() );
        loveWordAiMemoryPalaceVo.setSelectedWords( arg0.getSelectedWords() );
        loveWordAiMemoryPalaceVo.setStoryContent( arg0.getStoryContent() );
        loveWordAiMemoryPalaceVo.setGenerationParams( arg0.getGenerationParams() );

        return loveWordAiMemoryPalaceVo;
    }

    @Override
    public LoveWordAiMemoryPalaceVo convert(LoveWordAiMemoryPalace arg0, LoveWordAiMemoryPalaceVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setBookId( arg0.getBookId() );
        arg1.setUnitId( arg0.getUnitId() );
        arg1.setTitle( arg0.getTitle() );
        arg1.setTheme( arg0.getTheme() );
        arg1.setSelectedWords( arg0.getSelectedWords() );
        arg1.setStoryContent( arg0.getStoryContent() );
        arg1.setGenerationParams( arg0.getGenerationParams() );

        return arg1;
    }
}
