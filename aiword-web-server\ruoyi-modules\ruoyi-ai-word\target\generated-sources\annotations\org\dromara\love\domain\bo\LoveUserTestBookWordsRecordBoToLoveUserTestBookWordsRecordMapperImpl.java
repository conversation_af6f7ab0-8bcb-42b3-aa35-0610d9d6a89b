package org.dromara.love.domain.bo;

import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.dromara.love.domain.LoveUserTestBookWordsRecord;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-07T01:55:47+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.15 (Microsoft)"
)
@Component
public class LoveUserTestBookWordsRecordBoToLoveUserTestBookWordsRecordMapperImpl implements LoveUserTestBookWordsRecordBoToLoveUserTestBookWordsRecordMapper {

    @Override
    public LoveUserTestBookWordsRecord convert(LoveUserTestBookWordsRecordBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        LoveUserTestBookWordsRecord loveUserTestBookWordsRecord = new LoveUserTestBookWordsRecord();

        loveUserTestBookWordsRecord.setSearchValue( arg0.getSearchValue() );
        loveUserTestBookWordsRecord.setCreateDept( arg0.getCreateDept() );
        loveUserTestBookWordsRecord.setCreateBy( arg0.getCreateBy() );
        loveUserTestBookWordsRecord.setCreateTime( arg0.getCreateTime() );
        loveUserTestBookWordsRecord.setUpdateBy( arg0.getUpdateBy() );
        loveUserTestBookWordsRecord.setUpdateTime( arg0.getUpdateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            loveUserTestBookWordsRecord.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        loveUserTestBookWordsRecord.setId( arg0.getId() );
        loveUserTestBookWordsRecord.setUserId( arg0.getUserId() );
        loveUserTestBookWordsRecord.setBookTextBookId( arg0.getBookTextBookId() );
        loveUserTestBookWordsRecord.setBookTextBookName( arg0.getBookTextBookName() );
        loveUserTestBookWordsRecord.setTestDuration( arg0.getTestDuration() );
        loveUserTestBookWordsRecord.setCorrectAnswers( arg0.getCorrectAnswers() );
        loveUserTestBookWordsRecord.setAccuracyRate( arg0.getAccuracyRate() );
        loveUserTestBookWordsRecord.setEn2cnAccuracy( arg0.getEn2cnAccuracy() );
        loveUserTestBookWordsRecord.setCn2enAccuracy( arg0.getCn2enAccuracy() );
        loveUserTestBookWordsRecord.setAudio2cnAccuracy( arg0.getAudio2cnAccuracy() );
        loveUserTestBookWordsRecord.setSuitableDifficultyLevel( arg0.getSuitableDifficultyLevel() );
        loveUserTestBookWordsRecord.setBaseScore( arg0.getBaseScore() );
        loveUserTestBookWordsRecord.setDifficultyBonus( arg0.getDifficultyBonus() );
        loveUserTestBookWordsRecord.setTimeBonus( arg0.getTimeBonus() );
        loveUserTestBookWordsRecord.setCompletionBonus( arg0.getCompletionBonus() );
        loveUserTestBookWordsRecord.setTotalScore( arg0.getTotalScore() );
        loveUserTestBookWordsRecord.setGradeLevel( arg0.getGradeLevel() );
        loveUserTestBookWordsRecord.setGradeDesc( arg0.getGradeDesc() );
        loveUserTestBookWordsRecord.setAnswerDetails( arg0.getAnswerDetails() );
        loveUserTestBookWordsRecord.setRemarks( arg0.getRemarks() );
        loveUserTestBookWordsRecord.setTags( arg0.getTags() );

        return loveUserTestBookWordsRecord;
    }

    @Override
    public LoveUserTestBookWordsRecord convert(LoveUserTestBookWordsRecordBo arg0, LoveUserTestBookWordsRecord arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setId( arg0.getId() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setBookTextBookId( arg0.getBookTextBookId() );
        arg1.setBookTextBookName( arg0.getBookTextBookName() );
        arg1.setTestDuration( arg0.getTestDuration() );
        arg1.setCorrectAnswers( arg0.getCorrectAnswers() );
        arg1.setAccuracyRate( arg0.getAccuracyRate() );
        arg1.setEn2cnAccuracy( arg0.getEn2cnAccuracy() );
        arg1.setCn2enAccuracy( arg0.getCn2enAccuracy() );
        arg1.setAudio2cnAccuracy( arg0.getAudio2cnAccuracy() );
        arg1.setSuitableDifficultyLevel( arg0.getSuitableDifficultyLevel() );
        arg1.setBaseScore( arg0.getBaseScore() );
        arg1.setDifficultyBonus( arg0.getDifficultyBonus() );
        arg1.setTimeBonus( arg0.getTimeBonus() );
        arg1.setCompletionBonus( arg0.getCompletionBonus() );
        arg1.setTotalScore( arg0.getTotalScore() );
        arg1.setGradeLevel( arg0.getGradeLevel() );
        arg1.setGradeDesc( arg0.getGradeDesc() );
        arg1.setAnswerDetails( arg0.getAnswerDetails() );
        arg1.setRemarks( arg0.getRemarks() );
        arg1.setTags( arg0.getTags() );

        return arg1;
    }
}
