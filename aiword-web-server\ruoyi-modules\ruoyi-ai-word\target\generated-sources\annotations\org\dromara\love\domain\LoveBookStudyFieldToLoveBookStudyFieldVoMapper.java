package org.dromara.love.domain;

import io.github.linpeilie.AutoMapperConfig__787;
import io.github.linpeilie.BaseMapper;
import org.dromara.love.domain.bo.LoveBookStudyFieldBoToLoveBookStudyFieldMapper;
import org.dromara.love.domain.vo.LoveBookStudyFieldVo;
import org.dromara.love.domain.vo.LoveBookStudyFieldVoToLoveBookStudyFieldMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__787.class,
    uses = {LoveBookStudyFieldVoToLoveBookStudyFieldMapper.class,LoveBookStudyFieldBoToLoveBookStudyFieldMapper.class},
    imports = {}
)
public interface LoveBookStudyFieldToLoveBookStudyFieldVoMapper extends BaseMapper<LoveBookStudyField, LoveBookStudyFieldVo> {
}
